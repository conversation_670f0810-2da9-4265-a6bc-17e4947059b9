{"#": "for a detail explaination of this file.", "project_name": "fss", "module_name": "api", "build": {"commands": ["CHASSIS_CONF_DIR=/workspace/conf/ apolloconf", "GO111MODULE=on GOPRIVATE=git.garena.com go-module-collector -step=1", "mkdir -p /tmp && cd /tmp && GO111MODULE=on GOPRIVATE=git.garena.com go get git.garena.com/shopee/bg-logistics/techplatform/replayer-agent/v2 && cd -", "mv $GOPATH/bin/replayer-agent /workspace/", "mkdir -p /workspace/agentconf/agent_client", "mv $GOPATH/pkg/mod/git.garena.com/shopee/bg-logistics/techplatform/replayer-agent/*/conf/agent_client/* /workspace/agentconf/agent_client/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.22.12-20", "squash": false, "run_commands": ["GOPRIVATE=git.garena.com go mod download", "GO111MODULE=on GOPRIVATE=git.garena.com go build -tags=jsoniter -o api_server ./cmd/rest_server", "GO111MODULE=on GOPRIVATE=git.garena.com go get -u git.garena.com/shopee/bg-logistics/go/go-module-collector", "GO111MODULE=on GOPRIVATE=git.garena.com go-module-collector", "go clean -cache", "go clean -modcache"], "dependent_libraries_files": ["."], "res_files": [], "image_language": "golang", "enable_multi_stage_golang_image": true, "multi_stage_base_image": "harbor.shopeemobile.com/shopee/golang-base:1.22.12-20", "workdir": "/workspace", "generate_dockerignore": true}}, "run": {"depend_services": [], "command": "sscinit -n -c deploy/supervisor/api.ini", "#": "check：就绪探针 (Readiness Probes)，作用：确定何时准备好接受请求流量", "smoke": {"#": "在 grace_period 时间（秒-s）内不探测，若 retry * interval 中任意次成功后开始执行 liveness 探针和check 探针。若 retry * interval 探测中都失败，则重启。interval时间（秒-s）", "protocol": "HTTP", "endpoint": "/ping", "timeout": 2, "retry": 10, "interval": 10, "grace_period": 180, "max_fails": 10}, "liveness": {"#": "在 grace_period 时间（秒-s）内不探测，超过grace_period后，若 连续 retry 次探测(每隔 interval时间（秒-s）探测一次)均失败，则k8s 重启 pod", "protocol": "HTTP", "endpoint": "/ping", "timeout": 2, "retry": 18, "interval": 10, "grace_period": 180, "max_fails": 18}, "check": {"#": "在 grace_period 时间（秒-s）内不探测，超过grace_period后，若后续探测(每隔 interval时间（秒-s）探测一次)中，有一次成功，则转为Normal 状态，并接受流量，连续 retry 次探测失败，流量摘除(新集群目前机制未完善)", "protocol": "HTTP", "endpoint": "/ping", "timeout": 2, "retry": 10, "interval": 10, "grace_period": 15, "max_fails": 10}, "enable_prometheus": true, "acquire_prometheus_port": true, "prometheus_path": "/metrics", "pre_hook_commands": []}, "deploy": {"deploy_timeout": 1800, "idcs": {"live": {"sg": ["sg", "sg1"], "my": ["sg", "sg1"], "th": ["sg", "sg1"], "ph": ["sg", "sg1"], "vn": ["sg", "sg1"], "id": ["sg", "sg1"], "tw": ["sg", "sg1"], "br": ["sg", "sg1"], "mx": ["sg", "sg1"], "ar": ["sg", "sg1"], "pl": ["sg", "sg1"], "es": ["sg", "sg1"], "fr": ["sg", "sg1"], "in": ["sg", "sg1"], "co": ["sg", "sg1"], "cl": ["sg", "sg1"]}}, "resources": {"live": {"cpu": 8, "mem": 8192}, "staging": {"cpu": 8, "mem": 8192}, "uat": {"cpu": 8, "mem": 8192}, "test": {"cpu": 4, "mem": 4096}}, "instances": {"live": {"sg": 2, "my": 2, "ph": 2, "th": 2, "vn": 2, "id": 2, "tw": 2, "br": 2, "mx": 2, "ar": 2, "pl": 2, "es": 2, "fr": 2, "in": 2, "co": 2, "cl": 2}, "canary": {"sg": 1, "my": 1, "ph": 1, "th": 1, "vn": 1, "id": 1, "tw": 1, "br": 1, "mx": 1, "ar": 1, "pl": 1, "es": 1, "fr": 1, "in": 1}}}}