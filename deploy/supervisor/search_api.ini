[unix_http_server]
file=/workspace/deploy/supervisor.search.sock

[supervisord]
logfile=/workspace/log/supervisor.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/workspace/deploy/supervisor.search.pid
nodaemon=false
minfds=1024
minprocs=200

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///workspace/deploy/supervisor.search.sock

[program:search_server]
environment=CHASSIS_CONF_DIR="/workspace/conf",REST_PORT=%(ENV_PORT)s,GOLANG_PROTOBUF_REGISTRATION_CONFLICT="warn"
command=/bin/sh -c ./search_server
directory=/workspace/
user=root
autorestart=true
redirect_stderr=true
stdout_logfile=/workspace/log/daemon.log
loglevel=info
startsecs=300

[program:replayer-agent]
command=/bin/sh -c "/workspace/replayer-agent -yamlConfigPath /workspace/conf/chassis.yaml -chassisTag -rest -restPort %(ENV_PORT)s"
directory = /workspace
user = root
autorestart = false
redirect_stderr = true
stdout_logfile = /workspace/log/daemon.log
loglevel = info