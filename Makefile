.PHONY: all wire build test lint clean tools install-tools help

# 获取 Go 版本相关的构建标志
GO_LDFLAGS := $(shell chmod +x scripts/check-go-version.sh && ./scripts/check-go-version.sh)

# 默认目标
all: wire build

# 代码生成
wire:
	go mod tidy
	go get github.com/google/wire/cmd/wire
	go install github.com/google/wire/cmd/wire
	@go generate -run="wire" ./...

# 构建
build:
	go build $(GO_LDFLAGS) -o server ./cmd/rest_server
	rm -f server

# 测试
test:
	go test $(GO_LDFLAGS) -v ./...

test-coverage:
	go test $(GO_LDFLAGS) -cover -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

test-benchmark:
	go test $(GO_LDFLAGS) -bench=. -benchmem ./...

# 代码质量检查
lint:
	golangci-lint run --timeout=5m

lint-fast:
	golangci-lint run --fast

vet:
	go vet ./...

# 代码格式化
fmt:
	goimports -w .
	gofumpt -w .

# 依赖管理
deps:
	go mod tidy
	go mod download

# 清理
clean:
	go clean -cache -modcache -testcache
	rm -f coverage.out coverage.html
	rm -f server

# 安装开发工具
install-tools:
	@echo "安装 Go 开发工具..."
	@chmod +x scripts/install-go-tools.sh
	@./scripts/install-go-tools.sh

# 安全检查
security:
	govulncheck ./...
	gosec ./...

# 性能分析
profile:
	go test $(GO_LDFLAGS) -cpuprofile=cpu.prof -memprofile=mem.prof -bench=. ./...

# 文档生成
docs:
	gomarkdoc ./...

# 显示当前 Go 版本和构建标志
version:
	@echo "Go 版本: $(shell go version)"
	@echo "构建标志: $(GO_LDFLAGS)"

# 帮助信息
help:
	@echo "可用的命令："
	@echo "  make all          - 运行 wire 和 build"
	@echo "  make wire         - 生成依赖注入代码"
	@echo "  make build        - 构建项目"
	@echo "  make test         - 运行测试"
	@echo "  make test-coverage - 运行测试并生成覆盖率报告"
	@echo "  make test-benchmark - 运行基准测试"
	@echo "  make lint         - 运行 golangci-lint"
	@echo "  make lint-fast    - 快速运行 golangci-lint"
	@echo "  make vet          - 运行 go vet"
	@echo "  make fmt          - 格式化代码"
	@echo "  make deps         - 整理和下载依赖"
	@echo "  make clean        - 清理缓存和临时文件"
	@echo "  make install-tools - 安装开发工具"
	@echo "  make security     - 运行安全检查"
	@echo "  make profile      - 生成性能分析文件"
	@echo "  make docs         - 生成文档"
	@echo "  make version      - 显示 Go 版本和构建标志"
	@echo "  make help         - 显示此帮助信息"