run:
  timeout: 5m
  go: "1.21"
  modules-download-mode: readonly

linters-settings:
  gocyclo:
    min-complexity: 15
  maligned:
    suggest-new: true
  dupl:
    threshold: 100
  goconst:
    min-len: 2
    min-occurrences: 3
  misspell:
    locale: US
  lll:
    line-length: 140
  goimports:
    local-prefixes: git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport # https://github.com/go-critic/go-critic/issues/845
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc
      - commentFormatting
      - hugeParam
      - newDeref
      - sloppyReassign
      - unslice
      - valSwap
      - weakCond
      - yodaStyleExpr

linters:
  disable-all: true
  enable:
    - bodyclose
    - dogsled
    - dupl
    - errcheck
    - exhaustive
    - forbidigo
    - funlen
    - gci
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - gofumpt
    - goheader
    - goimports
    - mnd
    - gomodguard
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - makezero
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - prealloc
    - predeclared
    - promlinter
    - revive
    - rowserrcheck
    - staticcheck
    - stylecheck
    - thelper
    - tparallel
    - typecheck
    - unconvert
    - unparam
    - unused
    - whitespace
    - wrapcheck

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - mnd
        - goconst
        - gocyclo
        - funlen
        - dupl
        - gocognit
    - path: internal/common/utils/
      linters:
        - mnd
        - goconst
    - path: pkg/
      linters:
        - mnd
        - goconst
  max-issues-per-linter: 0
  max-same-issues: 0 