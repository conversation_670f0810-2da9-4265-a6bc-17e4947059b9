# Fulfillment Sourcing Service

基于领域驱动设计(DDD)架构的Fulfillment Sourcing Service

## 项目架构

本项目采用DDD(领域驱动设计)架构，核心目录结构如下：

```
fulfillment-sourcing-service
  cmd/                    # 应用程序入口
    rest_server/          # REST 服务入口
    search_server/        # Search 服务入口
    task_server/          # Task 服务入口
  conf/                   # 配置
  deploy/                 # 部署脚本
  docs/                   # 项目文档
  internal/               # 私有应用代码
    application/          # 应用层
      dto/                # 数据传输对象
      service/            # 应用服务
    bootstrap/            # 启动引导
    common/               # 通用代码
    domain/               # 领域层
      entity/             # 实体
      repository/         # 仓储接口
      service/            # 领域服务
    infrastructure/       # 基础设施层
      external/           # 外部服务集成
      messaging/          # 消息实现
      redishelper/        # Redis助手
    interceptor/          # 拦截器
    interface/            # 接口层
      api/                # API定义
      handler/            # 请求处理器
      middleware/         # 中间件
      ping/               # Ping服务
      task_handler/       # 任务处理器
    logger/               # 日志
  log/                    # 日志文件
  pkg/                    # 可公开复用的库
    cache/                # 缓存
    config/               # 配置
    logger/               # 日志
    ratelimiter/          # 限流器
    utils/                # 工具函数
  scripts/                # 各种脚本
  tests/                  # 测试代码
    integration/          # 集成测试
    unit/                 # 单元测试
```

## 开发指南

### 1. 环境准备

在开始开发之前，请确保你已经安装了以下软件：

- **Go**: 版本 `1.22` 或更高。
- **Make**: 用于执行项目中定义的各种自动化任务。
- **Git**: 用于版本控制。

### 2. 安装开发工具

本项目依赖一些 Go 开发工具来进行代码格式化、质量检查、测试等。你可以通过以下命令一键安装所有推荐的工具：

```bash
make install-tools
```

此命令会执行 `scripts/install-go-tools.sh` 脚本，安装包括 `golangci-lint`, `goimports`, `gofumpt`, `wire` 在内的所有必要工具。

### 3. 依赖管理

本项目使用 Go Modules 来管理依赖。

- **下载和整理依赖**:
  ```bash
  make deps
  ```
  这个命令会运行 `go mod tidy` 和 `go mod download`，确保你的 `go.mod` 和 `go.sum` 文件是最新的，并且所有依赖都已下载。

### 4. 代码生成

本项目使用 `wire` 来进行依赖注入。在添加或修改了有依赖关系的代码后，需要重新生成 `wire_gen.go` 文件。

- **生成依赖注入代码**:
  ```bash
  make wire
  ```

### 5. 构建和运行

- **构建项目**:
  ```bash
  make build
  ```
  此命令会编译 `cmd/rest_server` 并生成一个名为 `server` 的可执行文件。

- **运行服务**:
  ```bash
  ./server
  ```

### 6. 代码风格和质量

- **代码格式化**:
  在提交代码前，请确保你的代码已经过格式化。
  ```bash
  make fmt
  ```
  此命令会使用 `goimports` 和 `gofumpt` 来格式化代码。

- **代码质量检查**:
  使用 `golangci-lint` 来检查代码质量。
  ```bash
  make lint
  ```
  为了更快的检查，你也可以使用：
  ```bash
  make lint-fast
  ```

### 7. 测试

- **运行所有测试**:
  ```bash
  make test
  ```

- **生成测试覆盖率报告**:
  ```bash
  make test-coverage
  ```
  此命令会生成 `coverage.html` 文件，你可以在浏览器中打开它来查看详细的测试覆盖率报告。

- **运行基准测试**:
  ```bash
  make test-benchmark
  ```

### 8. 安全检查

- **检查安全漏洞**:
  ```bash
  make security
  ```
  此命令会使用 `govulncheck` 和 `gosec` 来扫描项目中的安全漏洞。

### 9. Makefile 命令参考

`Makefile` 中定义了许多有用的命令，你可以通过 `make help` 来查看所有可用的命令及其说明。



## 部署指南

待补充
