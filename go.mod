module git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service

go 1.22

require (
	git.garena.com/shopee/bg-logistics/dms/go-proto v0.0.10
	git.garena.com/shopee/bg-logistics/go/chassis v0.4.3-r.13
	git.garena.com/shopee/bg-logistics/go/chassis-saturn-server v1.0.8-r.17
	git.garena.com/shopee/bg-logistics/go/go-redis v0.0.1-r.10
	git.garena.com/shopee/bg-logistics/go/gocommon v0.4.2-r.33
	git.garena.com/shopee/bg-logistics/go/lrucache v0.0.1-r6
	git.garena.com/shopee/bg-logistics/go/scsps v0.0.1-r.5
	git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol v0.0.0-20250813131055-63db23d4f15c
	git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache v0.2.0-r.13
	git.garena.com/shopee/bg-logistics/logistics/spex-proto-center v0.1.3-0.20250722131728-dc7d1859b5dc
	git.garena.com/shopee/bg-logistics/service/saturn-rpc-job v1.3.2-r.16
	git.garena.com/shopee/experiment-platform/abtest-core/v2 v2.5.9
	git.garena.com/shopee/platform/service-governance/viewercontext v1.0.13
	git.garena.com/shopee/platform/tracing v1.11.10 // indirect
	git.garena.com/shopee/sp_protocol v1.3.24
	github.com/bytedance/sonic v1.13.2
	github.com/cespare/xxhash/v2 v2.3.0
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-faker/faker/v4 v4.0.0-beta.3
	github.com/gogo/protobuf v1.3.2
	github.com/golang/mock v1.6.0
	github.com/google/uuid v1.6.0
	github.com/google/wire v0.6.0
	github.com/howeyc/crc16 v0.0.0-20171223171357-2b2a61e366a6
	github.com/json-iterator/go v1.1.12
	github.com/juju/ratelimit v1.0.2
	github.com/modern-go/reflect2 v1.0.2
	github.com/panjf2000/ants/v2 v2.11.3
	github.com/pkg/errors v0.9.1
	github.com/satori/go.uuid v1.2.0
	github.com/stretchr/testify v1.10.0
	github.com/zeromicro/go-zero v1.8.3
	google.golang.org/grpc v1.65.0
	google.golang.org/protobuf v1.36.6
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	git.garena.com/common/go-common-tool v0.1.1 // indirect
	git.garena.com/common/gommon/crypt v0.0.0-20210211075301-867bb6bc3c33 // indirect
	git.garena.com/shopee/bg-logistics/go/agollo v0.0.24-r.1.0.20230423091235-848a7b063133 // indirect
	git.garena.com/shopee/bg-logistics/go/configmr v0.1.0-alpha.1.1 // indirect
	git.garena.com/shopee/bg-logistics/go/gojq v0.12.9-r2 // indirect
	git.garena.com/shopee/bg-logistics/go/gorm v0.0.7-0.20240515082807-ccaa79d931b5 // indirect
	git.garena.com/shopee/bg-logistics/go/gorm-hints v0.0.1-r1 // indirect
	git.garena.com/shopee/bg-logistics/go/scorm v0.2.2-r.18 // indirect
	git.garena.com/shopee/bg-logistics/go/scormv2 v0.0.1-r.26
	git.garena.com/shopee/bg-logistics/go/scregistry v0.2.6 // indirect
	git.garena.com/shopee/common/circuitbreaker v0.8.0-rc.1 // indirect
	git.garena.com/shopee/common/gdbc/datum v0.1.1 // indirect
	git.garena.com/shopee/common/gdbc/hardy v0.8.0-beta.4 // indirect
	git.garena.com/shopee/common/gdbc/parser v0.8.0-rc.3 // indirect
	git.garena.com/shopee/common/gdbc/sddl v0.4.0 // indirect
	git.garena.com/shopee/common/jsonext v0.1.0 // indirect
	git.garena.com/shopee/common/observability_config v0.2.1 // indirect
	git.garena.com/shopee/common/ulog v0.2.4 // indirect
	git.garena.com/shopee/devops/golang_aegislib v0.0.9 // indirect
	git.garena.com/shopee/experiment-platform/abtest-config-service v1.1.4-sdk // indirect
	git.garena.com/shopee/experiment-platform/abtest-model v0.3.23 // indirect
	git.garena.com/shopee/experiment-platform/gateway-sdk-go v1.2.2 // indirect
	git.garena.com/shopee/golang_splib v0.3.1 // indirect
	git.garena.com/shopee/mts/servicecontext v0.2.0 // indirect
	git.garena.com/shopee/platform/config-sdk-go v0.8.0 // indirect
	git.garena.com/shopee/platform/ipds/ipds-sdk-go v0.3.3 // indirect
	git.garena.com/shopee/platform/service-governance/observability/metric v1.0.6 // indirect
	git.garena.com/shopee/platform/splog v1.4.12 // indirect
	git.garena.com/shopee/platform/trace v0.1.0 // indirect
	git.garena.com/shopee/platform/tracing-contrib/dynamic-sampler v0.1.1 // indirect
	github.com/360EntSecGroup-Skylar/excelize/v2 v2.3.2 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.2 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/Shopify/sarama v1.29.1 // indirect
	github.com/alecthomas/template v0.0.0-20190718012654-fb15b899a751 // indirect
	github.com/alecthomas/units v0.0.0-20190924025748-f65c72e2690d // indirect
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/benbjohnson/clock v1.1.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bufbuild/protocompile v0.14.1 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cenkalti/backoff v2.2.1+incompatible // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cloudwego/hertz v0.9.0 // indirect
	github.com/coocood/freecache v1.0.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/deepmap/oapi-codegen v1.3.6 // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dolthub/maphash v0.1.0 // indirect
	github.com/dolthub/swiss v0.2.1
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/emicklei/go-restful v2.16.0+incompatible // indirect
	github.com/envoyproxy/protoc-gen-validate v1.2.1 // indirect
	github.com/fsnotify/fsnotify v1.5.4 // indirect
	github.com/gin-contrib/sse v0.0.0-20190301062529-5545eab6dad3 // indirect
	github.com/gin-gonic/gin v1.4.0 // indirect
	github.com/go-chassis/foundation v0.3.0 // indirect
	github.com/go-chassis/go-archaius v1.6.0-beta1 // indirect
	github.com/go-chassis/go-restful-swagger20 v1.0.3-0.20200310030431-17d80f34264f // indirect
	github.com/go-chassis/openlog v1.1.2 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-sql-driver/mysql v1.9.2 // indirect
	github.com/golang/glog v1.2.1 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/gorilla/csrf v1.7.0 // indirect
	github.com/gorilla/securecookie v1.1.1 // indirect
	github.com/gorilla/websocket v1.4.1 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.2.0 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.0.0 // indirect
	github.com/hashicorp/go-uuid v1.0.2 // indirect
	github.com/hashicorp/go-version v1.2.0 // indirect
	github.com/hashicorp/golang-lru v0.6.0 // indirect
	github.com/influxdata/influxdb-client-go v1.4.0 // indirect
	github.com/influxdata/line-protocol v0.0.0-20200327222509-2487e7298839 // indirect
	github.com/itchyny/timefmt-go v0.1.3 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.2 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jhump/protoreflect v1.17.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/klauspost/compress v1.17.11 // indirect
	github.com/klauspost/cpuid/v2 v2.2.4 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.3 // indirect
	github.com/labstack/echo/v4 v4.1.11 // indirect
	github.com/labstack/gommon v0.3.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.2-0.20181231171920-c182affec369 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/parnurzeal/gorequest v0.2.16 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/pierrec/lz4 v2.6.0+incompatible // indirect
	github.com/pingcap/errors v0.11.5-0.20210425183316-da1aaba5fb63 // indirect
	github.com/pingcap/log v0.0.0-20210625125904-98ed8e2eb1c7 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/prometheus/client_golang v1.21.1 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.62.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/richardlehane/mscfb v1.0.3 // indirect
	github.com/richardlehane/msoleps v1.0.1 // indirect
	github.com/rs/cors v1.8.2 // indirect
	github.com/samuel/go-zookeeper v0.0.0-20201211165307-7117e9ea2414 // indirect
	github.com/shirou/gopsutil/v3 v3.21.7 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/sirupsen/logrus v1.6.0 // indirect
	github.com/soheilhy/cmux v0.1.5 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/cast v1.4.1
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tidwall/gjson v1.14.4 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go v1.1.4 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.21.0 // indirect
	github.com/valyala/fasttemplate v1.1.0 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/xuri/efp v0.0.0-20201016154823-031c29024257 // indirect
	github.com/xwb1989/sqlparser v0.0.0-20180606152119-120387863bf2 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	go.uber.org/atomic v1.10.0 // indirect
	go.uber.org/multierr v1.9.0 // indirect
	go.uber.org/ratelimit v0.1.0 // indirect
	go.uber.org/zap v1.24.0 // indirect
	golang.org/x/arch v0.3.0 // indirect
	golang.org/x/crypto v0.33.0 // indirect
	golang.org/x/net v0.35.0 // indirect
	golang.org/x/sync v0.11.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	golang.org/x/time v0.10.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240711142825-46eb208f015d // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240701130421-f6361c86f094 // indirect
	google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.1.0 // indirect
	gopkg.in/alecthomas/kingpin.v2 v2.2.6 // indirect
	gopkg.in/go-playground/validator.v8 v8.18.2
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.3.4
	gorm.io/gorm v1.23.8 // indirect
	k8s.io/apimachinery v0.29.4 // indirect
	moul.io/http2curl v1.0.0 // indirect; indirectgit.garena.com/common/go-common-tool v0.1.1 // indirect
)

replace (
	github.com/prometheus/client_golang => github.com/prometheus/client_golang v1.11.0
	github.com/prometheus/common => github.com/prometheus/common v0.26.0
	github.com/shirou/gopsutil/v3 => github.com/shirou/gopsutil/v3 v3.24.5
)
