#!/bin/bash

# Go 开发工具安装脚本
# 这个脚本会安装推荐的 Go 开发工具

set -e

echo "🚀 开始安装 Go 开发工具..."

# 确保 GOPATH 和 GOBIN 已设置
export GOPATH=${GOPATH:-$HOME/go}
export GOBIN=${GOBIN:-$GOPATH/bin}

# 创建 bin 目录
mkdir -p $GOBIN

# 工具列表
TOOLS=(
    # 代码格式化
    "golang.org/x/tools/cmd/goimports@latest"
    "mvdan.cc/gofumpt@latest"
    
    # 代码质量
    "github.com/golangci/golangci-lint/cmd/golangci-lint@latest"
    "honnef.co/go/tools/cmd/staticcheck@latest"
    "github.com/fzipp/gocyclo/cmd/gocyclo@latest"
    "github.com/jgautheron/goconst/cmd/goconst@latest"
    "github.com/mgechev/revive@latest"
    
    # 测试和覆盖率
    "github.com/axw/gocov/gocov@latest"
    "github.com/AlekSi/gocov-xml@latest"
    "github.com/tebeka/go2xunit@latest"
    "gotest.tools/gotestsum@latest"
    
    # 性能分析
    "github.com/google/pprof@latest"
    "github.com/uber/go-torch@latest"
    
    # 依赖管理
    "github.com/golang-migrate/migrate/v4/cmd/migrate@latest"
    "github.com/go-delve/delve/cmd/dlv@latest"
    
    # 代码生成
    "github.com/google/wire/cmd/wire@latest"
    "github.com/golang/mock/mockgen@latest"
    "github.com/vektra/mockery/v2@latest"
    
    # 文档
    "golang.org/x/tools/cmd/godoc@latest"
    "github.com/princjef/gomarkdoc/cmd/gomarkdoc@latest"
    
    # 安全
    "golang.org/x/vuln/cmd/govulncheck@latest"
    
    # 其他实用工具
    "github.com/ramya-rao-a/go-outline@latest"
    "github.com/uudashr/gopkgs/v2/cmd/gopkgs@latest"
    "github.com/newhook/go-symbols@latest"
    "github.com/rogpeppe/godef@latest"
    "github.com/sqs/goreturns@latest"
    "github.com/golangci/golangci-lint/cmd/golangci-lint@latest"
    "github.com/go-critic/go-critic/cmd/gocritic@latest"
)

# 安装工具
for tool in "${TOOLS[@]}"; do
    echo "📦 安装 $tool..."
    go install $tool
done

# 验证安装
echo "✅ 验证安装的工具..."

# 检查关键工具
TOOLS_TO_CHECK=(
    "goimports"
    "gofumpt"
    "golangci-lint"
    "staticcheck"
    "dlv"
    "wire"
    "mockgen"
    "govulncheck"
)

for tool in "${TOOLS_TO_CHECK[@]}"; do
    if command -v $tool &> /dev/null; then
        echo "✅ $tool 已安装"
    else
        echo "❌ $tool 安装失败"
    fi
done

echo ""
echo "🎉 Go 开发工具安装完成！"
echo ""
echo "📋 可用的工具："
echo "  • goimports - 代码导入格式化"
echo "  • gofumpt - 代码格式化"
echo "  • golangci-lint - 代码质量检查"
echo "  • staticcheck - 静态分析"
echo "  • dlv - 调试器"
echo "  • wire - 依赖注入"
echo "  • mockgen - Mock 生成"
echo "  • govulncheck - 安全漏洞检查"
echo ""
echo "💡 提示："
echo "  • 在 VS Code 中，这些工具会自动集成"
echo "  • 使用 Ctrl+Shift+P 运行任务"
echo "  • 在终端中直接运行工具名称即可使用" 