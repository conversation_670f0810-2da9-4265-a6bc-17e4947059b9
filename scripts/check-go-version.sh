#!/bin/bash

# 检测 Go 版本并返回相应的构建标志
# Go 1.23 以下不需要 -ldflags=-checklinkname=0
# Go 1.23 及以上需要 -ldflags=-checklinkname=0

# 获取 Go 版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')

# 解析版本号
IFS='.' read -ra VERSION_PARTS <<< "$GO_VERSION"
MAJOR=${VERSION_PARTS[0]}
MINOR=${VERSION_PARTS[1]}

# 检查是否需要添加链接标志
# Go 1.23 及以上版本需要
if [ "$MAJOR" -gt 1 ] || ([ "$MAJOR" -eq 1 ] && [ "$MINOR" -ge 23 ]); then
    echo "-ldflags=-checklinkname=0"
else
    echo ""
fi 