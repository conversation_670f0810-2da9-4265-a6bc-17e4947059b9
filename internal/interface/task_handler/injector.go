package task_handler

import (
	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/layer_cache"
)

var (
	TaskProvideSet = wire.NewSet(
		wire.Struct(new(TaskServer), "*"),
		redishelper.InitRedisClientMap,
		layer_cache.NewLayerCacheManager,
		NewSalesOrderListener,
		spexlib.SpexProviderSet,
		seller_tag.SellerTagServiceProviderSet,
		seller_tag.SellerTagApiProviderSet,
		order.SalesOrdersCountServiceProvideSet,
		order.CacheStoreProvideSet,
	)
)
