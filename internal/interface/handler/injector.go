package handler

import (
	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/service"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/abtesting"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/channel"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/channel_generator"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/checkout_promo"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_info"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_stock"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/logistics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/sbs"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_logistics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/soc"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/omslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/layer_cache"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(RestServer), "*"),
	redishelper.InitRedisClientMap,
	layer_cache.NewLayerCacheManager,
	NewItemGroupingHandler,
	NewDebugHandler,
	service.ItemGroupingServiceProviderSet,
	item_grouping.ItemGrouperProviderSet,
	item_grouping.ShippingOrderProcessorProviderSet,
	organizer.ItemOrganizerProviderSet,
	shop_local_sip.ShopLocalSIPServiceProviderSet,
	address.AddressServiceProviderSet,
	soc.SOCServiceProviderSet,
	warehouse_priority.WarehousePriorityServiceProviderSet,
	warehouse_priority.SellerWHPriorityServiceProviderSet,
	spexlib.SpexProviderSet,
	item_stock.ItemServiceProviderSet,
	item_stock.ItemStockServiceProviderSet,
	checkout_promo.CheckoutPromoApiProviderSet,
	seller_tag.SellerTagServiceProviderSet,
	seller_tag.SellerTagApiProviderSet,
	abtesting.AbtestServiceProvideSet,
	order.SalesOrdersCountServiceProvideSet,
	order.CacheStoreProvideSet,
	shop.ShopServiceProviderSet,
	shop.ShopMultiWHCacheProviderSet,
	item_tag.ItemTagApiProviderSet,
	item_tag.ItemTagServiceProviderSet,
	sbs.SBSServiceProviderSet,
	item_info.ItemInfoApiProviderSet,
	item_info.ItemServiceProviderSet,
	shop_logistics.ShopLogisticsApiProviderSet,
	shop_logistics.WarehouseLogisticsApiProviderSet,
	shop_logistics.ShopLogisticsServiceProviderSet,
	channel_generator.ChannelGeneratorProviderSet,
	logistics.LogisticsInfoApiProviderSet,
	logistics.LogisticsServiceProviderSet,
	channel.ChannelServiceProviderSet,
	lpslib.LpsClientProviderSet,
	omslib.OmsClientProviderSet,
)
