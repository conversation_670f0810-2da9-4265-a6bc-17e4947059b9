package handler

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/dto"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/mocker"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/api"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type DebugHandler struct {
	ConfAccessor config.ConfAccessor
}

func NewDebugHandler(confAccessor config.ConfAccessor) *DebugHandler {
	return &DebugHandler{
		ConfAccessor: confAccessor,
	}
}

func (h *DebugHandler) URLPatterns() []restful.Route {
	routerGroup := restful.NewRouterGroup("/api/debug")

	confRouterGroup := routerGroup.Group("/conf")
	confRouterGroup.POST("/keys", h.ConfKeys)
	confRouterGroup.POST("/value", h.ConfValue)
	confRouterGroup.POST("/all", h.ConfAll)
	return routerGroup.GetRouters()
}

func (h *DebugHandler) ConfKeys(reqCtx *restful.Context) {
	keys := h.ConfAccessor.Keys()
	resp := &dto.ConfKeysResponse{
		Keys: keys,
	}
	api.WriteJsonResp(reqCtx, resp, nil)
}

func (h *DebugHandler) ConfValue(reqCtx *restful.Context) {
	req := &dto.ConfValueRequest{}
	if err := api.JsonSchemaParseValidate(reqCtx, req); err != nil {
		api.WriteJsonResp(reqCtx, nil, err)
		return
	}

	ctx := mocker.MockContext(reqCtx)
	value, existed := h.ConfAccessor.ValueICtx(ctx, req.Key)
	if !existed {
		api.WriteJsonResp(reqCtx, nil, fsserr.New(fsserr.ConfigNotExisted, req.Key))
		return
	}
	resp := &dto.ConfValueResponse{
		Value: value,
	}
	api.WriteJsonResp(reqCtx, resp, nil)
}

func (h *DebugHandler) ConfAll(reqCtx *restful.Context) {
	ctx := mocker.MockContext(reqCtx)
	configs := h.ConfAccessor.All(ctx)
	resp := &dto.ConfAllResponse{
		Configs: configs,
	}
	api.WriteJsonResp(reqCtx, resp, nil)
}
