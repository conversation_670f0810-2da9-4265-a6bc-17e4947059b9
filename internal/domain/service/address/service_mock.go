// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package address is a generated GoMock package.
package address

import (
	context "context"
	reflect "reflect"

	typ "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	fsserr "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
	gomock "github.com/golang/mock/gomock"
)

// MockAddrService is a mock of AddrService interface.
type MockAddrService struct {
	ctrl     *gomock.Controller
	recorder *MockAddrServiceMockRecorder
}

// MockAddrServiceMockRecorder is the mock recorder for MockAddrService.
type MockAddrServiceMockRecorder struct {
	mock *MockAddrService
}

// NewMockAddrService creates a new mock instance.
func NewMockAddrService(ctrl *gomock.Controller) *MockAddrService {
	mock := &MockAddrService{ctrl: ctrl}
	mock.recorder = &MockAddrServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAddrService) EXPECT() *MockAddrServiceMockRecorder {
	return m.recorder
}

// BatchGetShopWarehouseGeo mocks base method.
func (m *MockAddrService) BatchGetShopWarehouseGeo(ctx context.Context, userID typ.UserIdType, whAddressIDs []uint64) (map[uint64]entity.GeoLocation, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetShopWarehouseGeo", ctx, userID, whAddressIDs)
	ret0, _ := ret[0].(map[uint64]entity.GeoLocation)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// BatchGetShopWarehouseGeo indicates an expected call of BatchGetShopWarehouseGeo.
func (mr *MockAddrServiceMockRecorder) BatchGetShopWarehouseGeo(ctx, userID, whAddressIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetShopWarehouseGeo", reflect.TypeOf((*MockAddrService)(nil).BatchGetShopWarehouseGeo), ctx, userID, whAddressIDs)
}

// GetAddressWithGeo mocks base method.
func (m *MockAddrService) GetAddressWithGeo(ctx context.Context, userID typ.UserIdType, addressID uint64) (entity.AddressWithGeo, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddressWithGeo", ctx, userID, addressID)
	ret0, _ := ret[0].(entity.AddressWithGeo)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// GetAddressWithGeo indicates an expected call of GetAddressWithGeo.
func (mr *MockAddrServiceMockRecorder) GetAddressWithGeo(ctx, userID, addressID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddressWithGeo", reflect.TypeOf((*MockAddrService)(nil).GetAddressWithGeo), ctx, userID, addressID)
}

// GetBuyerGeoLocation mocks base method.
func (m *MockAddrService) GetBuyerGeoLocation(ctx context.Context, userID typ.UserIdType, addressID uint64) (entity.GeoLocation, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuyerGeoLocation", ctx, userID, addressID)
	ret0, _ := ret[0].(entity.GeoLocation)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// GetBuyerGeoLocation indicates an expected call of GetBuyerGeoLocation.
func (mr *MockAddrServiceMockRecorder) GetBuyerGeoLocation(ctx, userID, addressID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuyerGeoLocation", reflect.TypeOf((*MockAddrService)(nil).GetBuyerGeoLocation), ctx, userID, addressID)
}

// GetDummyBuyerGeoLocation mocks base method.
func (m *MockAddrService) GetDummyBuyerGeoLocation(ctx context.Context, dummyBuyerID typ.UserIdType, sipPrimaryRegion string) (entity.GeoLocation, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDummyBuyerGeoLocation", ctx, dummyBuyerID, sipPrimaryRegion)
	ret0, _ := ret[0].(entity.GeoLocation)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// GetDummyBuyerGeoLocation indicates an expected call of GetDummyBuyerGeoLocation.
func (mr *MockAddrServiceMockRecorder) GetDummyBuyerGeoLocation(ctx, dummyBuyerID, sipPrimaryRegion interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDummyBuyerGeoLocation", reflect.TypeOf((*MockAddrService)(nil).GetDummyBuyerGeoLocation), ctx, dummyBuyerID, sipPrimaryRegion)
}

// GetShopeeWarehouseAddress mocks base method.
func (m *MockAddrService) GetShopeeWarehouseAddress(ctx context.Context, warehouseCode string) (entity.Address, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShopeeWarehouseAddress", ctx, warehouseCode)
	ret0, _ := ret[0].(entity.Address)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// GetShopeeWarehouseAddress indicates an expected call of GetShopeeWarehouseAddress.
func (mr *MockAddrServiceMockRecorder) GetShopeeWarehouseAddress(ctx, warehouseCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShopeeWarehouseAddress", reflect.TypeOf((*MockAddrService)(nil).GetShopeeWarehouseAddress), ctx, warehouseCode)
}
