// Code generated by MockGen. DO NOT EDIT.
// Source: api.go

// Package abtesting is a generated GoMock package.
package abtesting

import (
	context "context"
	reflect "reflect"

	business_config "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	typ "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// CheckUserInExperimentGroup mocks base method.
func (m *MockService) CheckUserInExperimentGroup(ctx context.Context, userid typ.UserIdType, sceneKey, layerKey string, groupId int64) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserInExperimentGroup", ctx, userid, sceneKey, layerKey, groupId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserInExperimentGroup indicates an expected call of CheckUserInExperimentGroup.
func (mr *MockServiceMockRecorder) CheckUserInExperimentGroup(ctx, userid, sceneKey, layerKey, groupId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserInExperimentGroup", reflect.TypeOf((*MockService)(nil).CheckUserInExperimentGroup), ctx, userid, sceneKey, layerKey, groupId)
}

// CheckUserInExperimentGroupUsingFeature mocks base method.
func (m *MockService) CheckUserInExperimentGroupUsingFeature(ctx context.Context, userId typ.UserIdType, abTestConfig business_config.ABTestConfig) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserInExperimentGroupUsingFeature", ctx, userId, abTestConfig)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserInExperimentGroupUsingFeature indicates an expected call of CheckUserInExperimentGroupUsingFeature.
func (mr *MockServiceMockRecorder) CheckUserInExperimentGroupUsingFeature(ctx, userId, abTestConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserInExperimentGroupUsingFeature", reflect.TypeOf((*MockService)(nil).CheckUserInExperimentGroupUsingFeature), ctx, userId, abTestConfig)
}

// CheckUserInExperimentGroupUsingParameter mocks base method.
func (m *MockService) CheckUserInExperimentGroupUsingParameter(ctx context.Context, userId typ.UserIdType, sceneKey, expName, parameter string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserInExperimentGroupUsingParameter", ctx, userId, sceneKey, expName, parameter)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserInExperimentGroupUsingParameter indicates an expected call of CheckUserInExperimentGroupUsingParameter.
func (mr *MockServiceMockRecorder) CheckUserInExperimentGroupUsingParameter(ctx, userId, sceneKey, expName, parameter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserInExperimentGroupUsingParameter", reflect.TypeOf((*MockService)(nil).CheckUserInExperimentGroupUsingParameter), ctx, userId, sceneKey, expName, parameter)
}

// GetUserExperimentGroups mocks base method.
func (m *MockService) GetUserExperimentGroups(ctx context.Context, userid typ.UserIdType, sceneKey, layerKey string) ([]*entity.ExperimentGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExperimentGroups", ctx, userid, sceneKey, layerKey)
	ret0, _ := ret[0].([]*entity.ExperimentGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExperimentGroups indicates an expected call of GetUserExperimentGroups.
func (mr *MockServiceMockRecorder) GetUserExperimentGroups(ctx, userid, sceneKey, layerKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExperimentGroups", reflect.TypeOf((*MockService)(nil).GetUserExperimentGroups), ctx, userid, sceneKey, layerKey)
}

// GetUserExperimentParameters mocks base method.
func (m *MockService) GetUserExperimentParameters(ctx context.Context, userId typ.UserIdType, sceneKey string) (map[string]*entity.ExperimentGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExperimentParameters", ctx, userId, sceneKey)
	ret0, _ := ret[0].(map[string]*entity.ExperimentGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExperimentParameters indicates an expected call of GetUserExperimentParameters.
func (mr *MockServiceMockRecorder) GetUserExperimentParameters(ctx, userId, sceneKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExperimentParameters", reflect.TypeOf((*MockService)(nil).GetUserExperimentParameters), ctx, userId, sceneKey)
}
