package abtesting

import (
	"context"
	"strings"

	"git.garena.com/shopee/experiment-platform/abtest-core/v2/api"
	"git.garena.com/shopee/experiment-platform/abtest-core/v2/api/param"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

type Service interface {
	GetUserExperimentGroups(ctx context.Context, userid typ.UserIdType, sceneKey, layerKey string) ([]*entity.ExperimentGroup, error)
	CheckUserInExperimentGroup(ctx context.Context, userid typ.UserIdType, sceneKey, layerKey string, groupId int64) (bool, error)
	GetUserExperimentParameters(ctx context.Context, userId typ.UserIdType, sceneKey string) (map[string]*entity.ExperimentGroup, error)
	CheckUserInExperimentGroupUsingParameter(
		ctx context.Context,
		userId typ.UserIdType,
		sceneKey string,
		expName string,
		parameter string,
	) (bool, error)
	CheckUserInExperimentGroupUsingFeature(
		ctx context.Context,
		userId typ.UserIdType,
		abTestConfig business_config.ABTestConfig,
	) (bool, error)
}

type ServiceImpl struct {
	ConfAccessor config.ConfAccessor
}

func NewServiceImpl(ctx context.Context, confAccessor config.ConfAccessor) *ServiceImpl {
	abTestConfig := confAccessor.GetABTestingConfig(ctx)
	api.Init(api.Config(&abTestConfig))
	return &ServiceImpl{
		ConfAccessor: confAccessor,
	}
}

func (s *ServiceImpl) GetUserExperimentGroups(ctx context.Context, userid typ.UserIdType, sceneKey, layerKey string) ([]*entity.ExperimentGroup, error) {
	expParam := param.NewExpParamBuilder().Region(envvar.GetCIDLower(ctx)).UserID(int64(userid)).SceneKey(sceneKey).LayerKey(layerKey).Build()
	resp, err := api.GetAbClient().GetExpGroups(ctx, expParam)
	if err != nil {
		return nil, err
	}

	groups := make([]*entity.ExperimentGroup, 0, len(resp.Hit))
	for _, hit := range resp.Hit {
		groups = append(groups, &entity.ExperimentGroup{
			ExperimentId: hit.ExpID,
			LayerId:      hit.LayerID,
			LayerKey:     hit.LayerKey,
			LayerName:    hit.LayerName,
			GroupId:      hit.GroupID,
			GroupName:    hit.GroupName,
			Parameter:    hit.Parameter,
		})
	}

	return groups, nil
}

func (s *ServiceImpl) CheckUserInExperimentGroup(ctx context.Context, userid typ.UserIdType, sceneKey, layerKey string, groupId int64) (bool, error) {
	groups, err := s.GetUserExperimentGroups(ctx, userid, sceneKey, layerKey)
	if err != nil {
		return false, err
	}

	for _, group := range groups {
		if group.GroupId == groupId {
			return true, nil
		}
	}

	return false, nil
}

func (s *ServiceImpl) GetUserExperimentParameters(ctx context.Context, userId typ.UserIdType, sceneKey string) (map[string]*entity.ExperimentGroup, error) {
	expParam := param.NewExpParamBuilder().Region(envvar.GetCIDLower(ctx)).UserID(int64(userId)).SceneKey(sceneKey).Build()
	resp, err := api.GetAbClient().GetExpGroups(ctx, expParam)
	if err != nil {
		return nil, err
	}

	result := make(map[string]*entity.ExperimentGroup)
	if resp != nil {
		for _, hit := range resp.Hit {
			result[hit.ExpName] = &entity.ExperimentGroup{
				ExperimentId: hit.ExpID,
				GroupId:      hit.GroupID,
				GroupName:    hit.GroupName,
				Parameter:    hit.Parameter,
			}
		}
	}

	return result, nil
}

func (s *ServiceImpl) CheckUserInExperimentGroupUsingParameter(
	ctx context.Context,
	userId typ.UserIdType,
	sceneKey string,
	expName string,
	parameter string,
) (bool, error) {
	expNameToParams, err := s.GetUserExperimentParameters(ctx, userId, sceneKey)
	if err != nil {
		return false, err
	}

	if expNameToParams[expName] == nil {
		return false, nil
	} else {
		return expNameToParams[expName].Parameter == parameter, nil
	}
}

func (s *ServiceImpl) CheckUserInExperimentGroupUsingFeature(
	ctx context.Context,
	userId typ.UserIdType,
	abTestConfig business_config.ABTestConfig,
) (bool, error) {
	expParam := param.NewFeatureParamBuilder().Region(envvar.GetCIDLower(ctx))
	if abTestConfig.SceneKey != "" {
		expParam = expParam.SceneKey(abTestConfig.SceneKey)
	}
	if len(abTestConfig.FeatureKeys) > 0 {
		expParam = expParam.FeatureKeys(abTestConfig.FeatureKeys)
	}
	if abTestConfig.BizName != "" {
		expParam = expParam.BizName(abTestConfig.BizName)
	}
	if len(abTestConfig.Params) > 0 {
		expParam = expParam.UserParam(abTestConfig.Params)
	}
	newExpParam := expParam.Clone().UserID(int64(userId)).Build()

	abClient := api.GetAbClient()
	hitResultMap, err := abClient.GetFeatures(ctx, newExpParam)
	if err != nil {
		return false, err
	}
	if val, ok := hitResultMap[abTestConfig.ResultKey]; ok {
		return strings.Contains(val.FeatureValue, abTestConfig.Value), nil
	}
	return abTestConfig.Default, nil
}
