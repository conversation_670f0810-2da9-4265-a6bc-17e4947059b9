package item_info

import (
	"context"
	"strconv"

	PBData_beeshop_db "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/common/beeshop_db.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_item_itemaggregation_iteminfo.pb"
	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

const (
	weightInflation    uint64 = 100
	dimensionInflation uint64 = 100000
)

func BuildItemInfoFromProductInfo(
	ctx context.Context,
	item *marketplace_listing_item_itemaggregation_iteminfo.ProductInfo,
	groupSellerCoverShippingFeeConfig int,
) (entity.ItemInfo, fsserr.Error) {
	newItem := entity.ItemInfo{}

	newItem.ShopID = int64(item.GetShopId())
	newItem.ItemID = typ.ItemIdType(item.GetItemId())
	newItem.IsPreOrder = item.GetLogistics().GetIsPreOrder()
	newItem.EstimatedDays = int32(item.GetLogistics().GetEstimateDays())

	newItem.SizeInfo.Weight = item.GetLogistics().GetWeight()
	newItem.SizeInfo.Height = item.GetLogistics().GetDimension().GetHeight()
	newItem.SizeInfo.Width = item.GetLogistics().GetDimension().GetWidth()
	newItem.SizeInfo.Length = item.GetLogistics().GetDimension().GetLength()
	newItem.ModelIDToSizeInfo = make(map[typ.ModelIdType]entity.ItemSizeInfo)
	for _, modelLogistics := range item.GetLogistics().GetModels() {
		newItem.ModelIDToSizeInfo[typ.ModelIdType(modelLogistics.GetModelId())] = entity.ItemSizeInfo{
			Weight: modelLogistics.GetWeight() / weightInflation,
			Height: modelLogistics.GetDimension().GetHeight() / dimensionInflation,
			Width:  modelLogistics.GetDimension().GetWidth() / dimensionInflation,
			Length: modelLogistics.GetDimension().GetLength() / dimensionInflation,
		}
	}

	newItem.CategoryInfo.LocalCatIDs = item.GetCat().GetLocalCat().GetCatIds()
	newItem.CategoryInfo.GlobalCatIDs = item.GetCat().GetGlobalCat().GetCatIds()
	newItem.RawLogisticsInfo = item.GetLogistics().GetLogisticsInfo()
	newItem.DangerousGoods = item.GetLogistics().GetDangerousGoods()
	newItem.DangerousGoodsCategorization = item.GetLogistics().GetDangerousGoodsCategorization()
	newItem.LocalAttrModelId = item.GetAttribute().GetLocalAttrModelId()
	newItem.PriceInfo.PriceMax = item.GetItemPriceInfo().GetAggregatedPrice().GetPriceMax()
	newItem.PriceInfo.PriceMin = item.GetItemPriceInfo().GetAggregatedPrice().GetPriceMin()
	newItem.SellerSKU = item.GetItemBasic().GetSellerSku()

	channelIDStrToLogistics := make(map[string]entity.ItemLogisticsInfo)
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(item.GetLogistics().GetLogisticsInfo(), &channelIDStrToLogistics); err != nil {
		return newItem, fsserr.With(fsserr.JsonErr, err)
	}

	newItem.ChannelIDToLogistics = make(map[int]entity.ItemLogisticsInfo)
	for channelIDStr, logisticsInfo := range channelIDStrToLogistics {
		channelID, _ := strconv.Atoi(channelIDStr)
		logisticsInfo.ChannelID = channelID
		// tmp fix for mx
		if groupSellerCoverShippingFeeConfig == constant.NoCoverShippingFee {
			if logisticsInfo.CoverShippingFee {
				logger.CtxLogErrorf(ctx, "item enable seller cover shipping fee, item_id=%d", item.GetItemId())
			}
			logisticsInfo.CoverShippingFee = false
		}
		newItem.ChannelIDToLogistics[channelID] = logisticsInfo
	}

	newItem.ModelIDToModelDeliveryInfo = getModelDeliveryInfoWithItemLogistics(item.GetLogistics())

	itemFlag := item.GetItemBasic().GetFlag()
	newItem.IsVSKU = isVSKUItem(int32(itemFlag))

	MTSKUItemID := item.GetItemBasic().GetMtskuItemId()
	if !newItem.IsVSKU {
		newItem.ModelIDToMTSKUInfos = getMTSKUInfosForNonVSKUItemUsingModelInfo(MTSKUItemID, item.GetModels())
	}

	newItem.ItemInstallationServiceInfo = getInstallationChannelInfos(item.GetItemInstallationServiceInfo())

	return newItem, nil
}

func getMTSKUInfosForNonVSKUItemUsingModelInfo(
	MTSKUItemID uint64,
	itemModels []*marketplace_listing_item_itemaggregation_iteminfo.ModelInfo,
) map[typ.ModelIdType][]entity.MTSKUInfo {
	MTSKUInfosList := make([]entity.MTSKUInfo, 0)
	modelIDToMTSKUInfos := make(map[typ.ModelIdType][]entity.MTSKUInfo)

	if MTSKUItemID == 0 {
		return modelIDToMTSKUInfos
	}

	for _, itemModel := range itemModels {
		MPSKUModelID := itemModel.GetModelId()
		MTSKUModelID := itemModel.GetBasic().GetMtskuModelId()
		newMTSKUInfo := entity.MTSKUInfo{
			ItemID:   typ.ItemIdType(MTSKUItemID),
			ModelID:  typ.ModelIdType(MTSKUModelID),
			Quantity: 1,
		}
		MTSKUInfosList = append(MTSKUInfosList, newMTSKUInfo)
		modelIDToMTSKUInfos[typ.ModelIdType(MPSKUModelID)] = MTSKUInfosList
	}

	return modelIDToMTSKUInfos
}

func getInstallationChannelInfos(itemInstallationLogistics *marketplace_listing_item_itemaggregation_iteminfo.ItemInstallationServiceInfo) entity.ItemInstallationServiceInfo {
	installationChannelInfo := make([]entity.ItemInstallationLogisticsInfo, len(itemInstallationLogistics.GetInstallationServiceChannelList()))
	for i, rawInstallationChannelInfo := range itemInstallationLogistics.GetInstallationServiceChannelList() {
		installationChannelInfo[i] = entity.ItemInstallationLogisticsInfo{
			ChannelID:                int(rawInstallationChannelInfo.GetMaskingChannelId()),
			IsMaskingChannelEnrolled: rawInstallationChannelInfo.GetMaskingChannelEnrollStatus() == uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_INSTALLATION_SERVICE_CHANNEL_ENABLE),
		}
	}

	return entity.ItemInstallationServiceInfo{
		IsItemEnrolled:          itemInstallationLogistics.GetItemEnrollStatus() == uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_INSTALLATION_SERVICE_ENABLE),
		InstallationChannelInfo: installationChannelInfo,
	}
}

func getModelDeliveryInfoWithItemLogistics(itemLogistics *marketplace_listing_item_itemaggregation_iteminfo.Logistics) map[typ.ModelIdType]entity.ModelDeliveryInfo {
	modelIDToModelDeliveryInfos := make(map[typ.ModelIdType]entity.ModelDeliveryInfo)

	for _, itemModel := range itemLogistics.GetModels() {
		modelID := itemModel.GetModelId()

		modelIDToModelDeliveryInfos[typ.ModelIdType(modelID)] = entity.ModelDeliveryInfo{
			IsPreOrder:    itemModel.GetIsPreOrder(),
			EstimatedDays: int32(itemModel.GetEstimateDays()),
		}
	}

	return modelIDToModelDeliveryInfos
}

func isVSKUItem(itemFlag int32) bool {
	if itemFlag&int32(PBData_beeshop_db.ItemFlags_HAS_VIRTUAL_SKU) > 0 {
		return true
	}

	return false
}
