// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package item_info is a generated GoMock package.
package item_info

import (
	context "context"
	reflect "reflect"

	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockItemService is a mock of ItemService interface.
type MockItemService struct {
	ctrl     *gomock.Controller
	recorder *MockItemServiceMockRecorder
}

// MockItemServiceMockRecorder is the mock recorder for MockItemService.
type MockItemServiceMockRecorder struct {
	mock *MockItemService
}

// NewMockItemService creates a new mock instance.
func NewMockItemService(ctrl *gomock.Controller) *MockItemService {
	mock := &MockItemService{ctrl: ctrl}
	mock.recorder = &MockItemServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockItemService) EXPECT() *MockItemServiceMockRecorder {
	return m.recorder
}

// BatchItemInfo mocks base method.
func (m *MockItemService) BatchItemInfo(ctx context.Context, shopItemIDs []entity.ShopItemID) (map[entity.ShopItemID]entity.ItemInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchItemInfo", ctx, shopItemIDs)
	ret0, _ := ret[0].(map[entity.ShopItemID]entity.ItemInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchItemInfo indicates an expected call of BatchItemInfo.
func (mr *MockItemServiceMockRecorder) BatchItemInfo(ctx, shopItemIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchItemInfo", reflect.TypeOf((*MockItemService)(nil).BatchItemInfo), ctx, shopItemIDs)
}

// EnabledChannelIDsWithPFFValidation mocks base method.
func (m *MockItemService) EnabledChannelIDsWithPFFValidation(ctx context.Context, itemInfo entity.ItemInfo, manageBySBS bool, warehouseCodes []string) []int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnabledChannelIDsWithPFFValidation", ctx, itemInfo, manageBySBS, warehouseCodes)
	ret0, _ := ret[0].([]int)
	return ret0
}

// EnabledChannelIDsWithPFFValidation indicates an expected call of EnabledChannelIDsWithPFFValidation.
func (mr *MockItemServiceMockRecorder) EnabledChannelIDsWithPFFValidation(ctx, itemInfo, manageBySBS, warehouseCodes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnabledChannelIDsWithPFFValidation", reflect.TypeOf((*MockItemService)(nil).EnabledChannelIDsWithPFFValidation), ctx, itemInfo, manageBySBS, warehouseCodes)
}
