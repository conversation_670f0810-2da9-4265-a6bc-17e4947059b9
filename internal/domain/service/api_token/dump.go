package api_token

import (
	"context"

	"github.com/dolthub/swiss"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/dbutils"
)

func DumpAPITokens(ctx context.Context, confAccessor config.ConfAccessor) (*swiss.Map[string, *ApiTokenTab], error) {
	apiTokens := make([]*ApiTokenTab, 0)

	db, err := dbutils.ReadDB(ctx, ApiTokenTabHook)
	if err != nil {
		return nil, err
	}

	db = db.Table(ApiTokenTabHook.TableName())
	db = db.Where("status = ?", []interface{}{dbutils.StatusActive}...)
	if err := db.Find(&apiTokens).GetError(); err != nil {
		return nil, err
	}

	ret := swiss.NewMap[string, *ApiTokenTab](uint32(len(apiTokens)))
	for _, apiToken := range apiTokens {
		ret.Put(apiToken.Token, apiToken)
	}

	return ret, nil
}
