package api_token

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/dbutils"
)

var (
	ApiTokenTabHook = &ApiTokenTab{}
)

type ApiTokenTab struct {
	Id     uint64 `gorm:"column:id"`
	Token  string `gorm:"column:token"`
	Caller string `gorm:"column:caller"`
	Status int8   `gorm:"column:status"`
}

func (p *ApiTokenTab) TableName() string {
	return "api_token_tab"
}

func (p *ApiTokenTab) DbTag(context.Context) dbutils.DBTag {
	return dbutils.SscSlsCid
}
