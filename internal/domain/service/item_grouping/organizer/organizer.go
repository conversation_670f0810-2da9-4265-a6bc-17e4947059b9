package organizer

import (
	"context"
	"slices"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ItemOrganizeSourceSelectFunc func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error)
type ItemOrganizeSourcePrioritizeFunc func(sources []string) ([]string, fsserr.Error)

type ItemOrganizer interface {
	// OrganizeItems organizes items by source
	OrganizeItems(
		ctx context.Context,
		items []group_entity.ShippingOrderItem,
		sourceSelectFunc ItemOrganizeSourceSelectFunc,
		sourcePrioritizeFunc ItemOrganizeSourcePrioritizeFunc,
		strategy constant.ItemOrganizeStrategy,
	) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, fsserr.Error)
}

type ItemOrganizerImpl struct {
	minimumParcelStrategyItemOrganizer     *MinimumParcelStrategyItemOrganizer
	warehousePriorityStrategyItemOrganizer *WarehousePriorityStrategyItemOrganizer
}

func NewItemOrganizer(
	minimumParcelStrategyItemOrganizer *MinimumParcelStrategyItemOrganizer,
	warehousePriorityStrategyItemOrganizer *WarehousePriorityStrategyItemOrganizer,
) *ItemOrganizerImpl {
	return &ItemOrganizerImpl{
		minimumParcelStrategyItemOrganizer:     minimumParcelStrategyItemOrganizer,
		warehousePriorityStrategyItemOrganizer: warehousePriorityStrategyItemOrganizer,
	}
}

func (i *ItemOrganizerImpl) OrganizeItems(
	ctx context.Context,
	items []group_entity.ShippingOrderItem,
	sourceSelectFunc ItemOrganizeSourceSelectFunc,
	sourcePrioritizeFunc ItemOrganizeSourcePrioritizeFunc,
	strategy constant.ItemOrganizeStrategy,
) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, fsserr.Error) {
	if len(items) == 0 {
		return nil, nil
	}

	withSelectableSourceItems, err := i.calculateSelectableItemSources(items, sourceSelectFunc)
	if err != nil {
		return nil, err
	}
	err = i.checkItemEnoughStock(ctx, withSelectableSourceItems)
	if err != nil {
		return nil, err
	}

	var prioritizedSources []string
	for _, item := range withSelectableSourceItems {
		for _, location := range item.SelectableLocations {
			if !slices.Contains(prioritizedSources, location.Source) {
				prioritizedSources = append(prioritizedSources, location.Source)
			}
		}
	}

	if len(prioritizedSources) > 1 {
		var sourcePrioritizeErr fsserr.Error
		prioritizedSources, sourcePrioritizeErr = sourcePrioritizeFunc(prioritizedSources)
		if sourcePrioritizeErr != nil {
			return nil, sourcePrioritizeErr
		}
	}

	organizer := i.getStrategyItemOrganizer(strategy)
	organizeKeyToItems, err := organizer.Organize(ctx, withSelectableSourceItems, prioritizedSources)
	if err != nil {
		return nil, err
	}

	return organizeKeyToItems, nil
}

func (i *ItemOrganizerImpl) calculateSelectableItemSources(
	items []group_entity.ShippingOrderItem,
	sourceSelectFunc ItemOrganizeSourceSelectFunc,
) ([]group_entity.ShippingOrderItemWithSelectableLocations, fsserr.Error) {
	withSelectableSourceItems := make([]group_entity.ShippingOrderItemWithSelectableLocations, 0, len(items))
	for _, item := range items {
		locations, err := sourceSelectFunc(item)
		if err != nil {
			return nil, err
		}
		selectedStockLocations := make(OrganizeItemKeyToStockLocation, len(locations))
		for _, location := range locations {
			selectedStockLocations[location.Source] = location
		}

		withSelectableSourceItems = append(withSelectableSourceItems, group_entity.ShippingOrderItemWithSelectableLocations{
			ShippingOrderItem:   item,
			SelectableLocations: locations,
		})
	}

	return withSelectableSourceItems, nil
}

func (i *ItemOrganizerImpl) checkItemEnoughStock(ctx context.Context,
	items []group_entity.ShippingOrderItemWithSelectableLocations) fsserr.Error {
	for _, item := range items {
		if item.SelectableLocations.TotalStock() < item.Quantity {
			logger.CtxLogDebugf(ctx, "item out of stock, item: %v", logger.JsonStringForDebugLog(ctx, item))
			return fsserr.NewOutOfStockError(fsserr.OutOfStockErrorData{
				ItemId:             item.ItemID,
				IsPackagePromotion: item.IsPackageItem(),
				ItemStockInfos:     group_entity.BuildOOSItemStockInfos(item.ShippingOrderItem, item.SelectableLocations.TotalStock()),
			})
		}
	}
	return nil
}

func (i *ItemOrganizerImpl) getStrategyItemOrganizer(strategy constant.ItemOrganizeStrategy) strategyItemOrganizer {
	switch strategy {
	case constant.ItemOrganizeStrategyMinimumParcel:
		return i.minimumParcelStrategyItemOrganizer
	default:
		return i.warehousePriorityStrategyItemOrganizer
	}
}

type strategyItemOrganizer interface {
	// Organize organizes items by strategy
	// The prioritizedSourceList should be sorted by source priority, the first source has the highest priority
	// The items should be ensured has enough stock before calling this function
	Organize(
		ctx context.Context,
		items []group_entity.ShippingOrderItemWithSelectableLocations,
		prioritizedSourceList []string,
	) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, fsserr.Error)
}

type MinimumParcelStrategyItemOrganizer struct {
	configAccessor                         config.ConfAccessor
	warehousePriorityStrategyItemOrganizer *WarehousePriorityStrategyItemOrganizer
}

func NewMinimumParcelStrategyItemOrganizer(
	configAccessor config.ConfAccessor,
	warehousePriorityStrategyItemOrganizer *WarehousePriorityStrategyItemOrganizer,
) *MinimumParcelStrategyItemOrganizer {
	return &MinimumParcelStrategyItemOrganizer{
		configAccessor:                         configAccessor,
		warehousePriorityStrategyItemOrganizer: warehousePriorityStrategyItemOrganizer,
	}
}

// Organize organizes items by minimum parcel strategy
// We will generate combinations of warehouses based on their priority to find the combination with the highest overall priority,
// using the fewest number of warehouses, and with sufficient stock to split the order.
func (m *MinimumParcelStrategyItemOrganizer) Organize(
	ctx context.Context,
	items []group_entity.ShippingOrderItemWithSelectableLocations,
	prioritizedSourceList []string,
) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, fsserr.Error) {
	maxSource := m.configAccessor.GetIGSDynamicConfig(ctx).MinParcelLimit
	if maxSource == 0 {
		maxSource = 1
	}

	sourceToPriority := make(map[string]int, len(prioritizedSourceList))
	for i, source := range prioritizedSourceList {
		sourceToPriority[source] = i
	}

	n := len(prioritizedSourceList)
	var fn func(int, int, []string) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, bool)
	fn = func(start, k int, current []string) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, bool) {
		if len(current) == k {
			return m.organizeStock(items, sourceToPriority, current)
		}
		for i := start; i < n; i++ {
			sourceToItems, ok := fn(i+1, k, append(current, prioritizedSourceList[i]))
			if ok {
				return sourceToItems, true
			}
		}
		return nil, false
	}
	for k := 1; k <= min(maxSource, n); k++ {
		sourceToItems, ok := fn(0, k, make([]string, 0, n))
		if ok {
			return sourceToItems, nil
		}
	}

	// if we can not find the solution by using minimum parcel strategy
	// then we should use warehouse priority strategy to organize the items
	return m.warehousePriorityStrategyItemOrganizer.Organize(ctx, items, prioritizedSourceList)
}

func (m *MinimumParcelStrategyItemOrganizer) organizeStock(
	items []group_entity.ShippingOrderItemWithSelectableLocations,
	sourceToPriority map[string]int,
	selectingSources []string,
) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, bool) {
	if !m.checkEnoughStock(items, selectingSources) {
		return nil, false
	}

	sourceToItems := make(map[OrganizeItemKey][]group_entity.ShippingOrderItem, len(items)*len(selectingSources))
	for _, item := range items {
		stockLocations := filterSortStockLocationsByPrioritizedSource(item.SelectableLocations, sourceToPriority, selectingSources)
		organizeKeyToItem, err := organizeItemByStockLocation(item, stockLocations)
		if err != nil {
			return nil, false
		}
		for key, newItem := range organizeKeyToItem {
			sourceToItems[key] = append(sourceToItems[key], newItem)
		}
	}

	return sourceToItems, true
}

func (m *MinimumParcelStrategyItemOrganizer) checkEnoughStock(items []group_entity.ShippingOrderItemWithSelectableLocations, selectingSources []string) bool {
	for _, item := range items {
		var stock int
		for _, location := range item.SelectableLocations {
			if slices.Contains(selectingSources, location.Source) {
				stock += int(location.AvailableStock)
			}
		}
		if stock < int(item.Quantity) {
			return false
		}
	}
	return true
}

type WarehousePriorityStrategyItemOrganizer struct {
}

func NewWarehousePriorityStrategyItemOrganizer() *WarehousePriorityStrategyItemOrganizer {
	return &WarehousePriorityStrategyItemOrganizer{}
}

// Organize organizes items by warehouse priority strategy
// For each item, we are trying to identify the warehouse with the highest priority and sufficient stock.
// If all warehouses have insufficient stock, we will split the order for the item according to the warehouse priority.
func (w *WarehousePriorityStrategyItemOrganizer) Organize(
	ctx context.Context,
	items []group_entity.ShippingOrderItemWithSelectableLocations,
	prioritizedSourceList []string,
) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, fsserr.Error) {
	sourceToItems := make(map[OrganizeItemKey][]group_entity.ShippingOrderItem)
	sourceToPriority := make(map[string]int, len(prioritizedSourceList))
	for i, source := range prioritizedSourceList {
		sourceToPriority[source] = i
	}

	for _, item := range items {
		stockLocations := filterSortStockLocationsByPrioritizedSource(item.SelectableLocations, sourceToPriority, prioritizedSourceList)
		if stockLocation, ok := w.getSingleFulfilledLocation(stockLocations, item.Quantity); ok {
			key := OrganizeItemKey{
				FulfilmentSource: stockLocation.Source,
				FulfilmentType:   stockLocation.FulfilmentType,
			}
			sourceToItems[key] = append(sourceToItems[key], item.ShippingOrderItem)
			continue
		}

		organizeKeyToItem, err := organizeItemByStockLocation(item, stockLocations)
		if err != nil {
			return nil, err
		}
		for key, newItem := range organizeKeyToItem {
			sourceToItems[key] = append(sourceToItems[key], newItem)
		}
	}

	return sourceToItems, nil
}

func (w *WarehousePriorityStrategyItemOrganizer) getSingleFulfilledLocation(
	stockLocations group_entity.StockLocations,
	quantity uint32,
) (group_entity.ItemStockLocation, bool) {
	for _, location := range stockLocations {
		if location.AvailableStock >= quantity {
			return location, true
		}
	}
	return group_entity.ItemStockLocation{}, false
}

func filterSortStockLocationsByPrioritizedSource(
	stockLocations group_entity.StockLocations,
	sourceToPriority map[string]int,
	selectingSources []string,
) group_entity.StockLocations {
	filteredStockLocations := make(group_entity.StockLocations, 0, len(stockLocations))
	for _, location := range stockLocations {
		if slices.Contains(selectingSources, location.Source) {
			filteredStockLocations = append(filteredStockLocations, location)
		}
	}

	sort.SliceStable(filteredStockLocations, func(i, j int) bool {
		return sourceToPriority[filteredStockLocations[i].Source] < sourceToPriority[filteredStockLocations[j].Source]
	})

	return filteredStockLocations
}

func organizeItemByStockLocation(
	item group_entity.ShippingOrderItemWithSelectableLocations,
	stockLocations group_entity.StockLocations,
) (map[OrganizeItemKey]group_entity.ShippingOrderItem, fsserr.Error) {
	if stockLocations.TotalStock() < item.Quantity {
		return nil, fsserr.New(fsserr.StockOrganizationErr, "organize stock failed")
	}

	sourceToItem := make(map[OrganizeItemKey]group_entity.ShippingOrderItem)
	remainingQuantity := item.Quantity
	for _, location := range stockLocations {
		if remainingQuantity == 0 {
			break
		}
		if location.AvailableStock == 0 {
			continue
		}
		// if the item has SingleSource, it can not be split to multiple orders
		// if the source does not have enough stock, then skip to next source
		if item.SingleSource && location.AvailableStock < item.Quantity {
			continue
		}

		reducingQuantity := min(remainingQuantity, location.AvailableStock)
		remainingQuantity -= reducingQuantity

		newItem := item
		newItem.Quantity = reducingQuantity
		sourceToItem[OrganizeItemKey{
			FulfilmentSource: location.Source,
			FulfilmentType:   location.FulfilmentType,
		}] = newItem.ShippingOrderItem
	}

	if remainingQuantity > 0 {
		return nil, fsserr.New(fsserr.StockOrganizationErr, "organize stock failed")
	}

	return sourceToItem, nil
}
