package organizer

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
)

// OrganizeItemKeyToStockLocation maps item keys to their stock locations
type OrganizeItemKeyToStockLocation map[string]group_entity.ItemStockLocation

// TotalStock returns the total available stock across all locations
func (s OrganizeItemKeyToStockLocation) TotalStock() uint32 {
	var stock uint32
	for _, location := range s {
		stock += location.AvailableStock
	}
	return stock
}

// OrganizeItemKey represents a unique key for organizing items by fulfillment source and type
type OrganizeItemKey struct {
	FulfilmentSource string
	FulfilmentType   constant.FulfilmentType
}
