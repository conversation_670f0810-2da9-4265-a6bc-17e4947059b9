package organizer

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
)

func SplitShippingOrderByOrganizedItems(
	baseOrder group_entity.ShippingOrder,
	keyToItems map[OrganizeItemKey][]group_entity.ShippingOrderItem,
	sourceToAddressID map[string]uint64,
) []group_entity.ShippingOrder {
	var orders []group_entity.ShippingOrder
	for key, items := range keyToItems {
		order := baseOrder.SplitNew(items)
		order.FulfilmentInfo = group_entity.OrderFulfilmentInfo{
			Source:    key.FulfilmentSource,
			Type:      key.FulfilmentType,
			AddressID: sourceToAddressID[key.FulfilmentSource],
		}
		orders = append(orders, order)
	}
	return orders
}
