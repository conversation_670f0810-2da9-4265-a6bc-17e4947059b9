// Code generated by MockGen. DO NOT EDIT.
// Source: organizer.go

// Package organizer is a generated GoMock package.
package organizer

import (
	context "context"
	reflect "reflect"

	constant "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	group_entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	gomock "github.com/golang/mock/gomock"
)

// MockItemOrganizer is a mock of ItemOrganizer interface.
type MockItemOrganizer struct {
	ctrl     *gomock.Controller
	recorder *MockItemOrganizerMockRecorder
}

// MockItemOrganizerMockRecorder is the mock recorder for MockItemOrganizer.
type MockItemOrganizerMockRecorder struct {
	mock *MockItemOrganizer
}

// NewMockItemOrganizer creates a new mock instance.
func NewMockItemOrganizer(ctrl *gomock.Controller) *MockItemOrganizer {
	mock := &MockItemOrganizer{ctrl: ctrl}
	mock.recorder = &MockItemOrganizerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockItemOrganizer) EXPECT() *MockItemOrganizerMockRecorder {
	return m.recorder
}

// OrganizeItems mocks base method.
func (m *MockItemOrganizer) OrganizeItems(ctx context.Context, items []group_entity.ShippingOrderItem, sourceSelectFunc ItemOrganizeSourceSelectFunc, sourcePrioritizeFunc ItemOrganizeSourcePrioritizeFunc, strategy constant.ItemOrganizeStrategy) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrganizeItems", ctx, items, sourceSelectFunc, sourcePrioritizeFunc, strategy)
	ret0, _ := ret[0].(map[OrganizeItemKey][]group_entity.ShippingOrderItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OrganizeItems indicates an expected call of OrganizeItems.
func (mr *MockItemOrganizerMockRecorder) OrganizeItems(ctx, items, sourceSelectFunc, sourcePrioritizeFunc, strategy interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrganizeItems", reflect.TypeOf((*MockItemOrganizer)(nil).OrganizeItems), ctx, items, sourceSelectFunc, sourcePrioritizeFunc, strategy)
}

// MockstrategyItemOrganizer is a mock of strategyItemOrganizer interface.
type MockstrategyItemOrganizer struct {
	ctrl     *gomock.Controller
	recorder *MockstrategyItemOrganizerMockRecorder
}

// MockstrategyItemOrganizerMockRecorder is the mock recorder for MockstrategyItemOrganizer.
type MockstrategyItemOrganizerMockRecorder struct {
	mock *MockstrategyItemOrganizer
}

// NewMockstrategyItemOrganizer creates a new mock instance.
func NewMockstrategyItemOrganizer(ctrl *gomock.Controller) *MockstrategyItemOrganizer {
	mock := &MockstrategyItemOrganizer{ctrl: ctrl}
	mock.recorder = &MockstrategyItemOrganizerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockstrategyItemOrganizer) EXPECT() *MockstrategyItemOrganizerMockRecorder {
	return m.recorder
}

// Organize mocks base method.
func (m *MockstrategyItemOrganizer) Organize(ctx context.Context, items []group_entity.ShippingOrderItemWithSelectableLocations, prioritizedSourceList []string) (map[OrganizeItemKey][]group_entity.ShippingOrderItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Organize", ctx, items, prioritizedSourceList)
	ret0, _ := ret[0].(map[OrganizeItemKey][]group_entity.ShippingOrderItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Organize indicates an expected call of Organize.
func (mr *MockstrategyItemOrganizerMockRecorder) Organize(ctx, items, prioritizedSourceList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Organize", reflect.TypeOf((*MockstrategyItemOrganizer)(nil).Organize), ctx, items, prioritizedSourceList)
}
