package processor

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/sbs"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
)

func TestGroupShipmentOrderProcessor_Name(t *testing.T) {
	processor := &GroupShipmentOrderProcessor{}
	expected := GroupShipmentProcessorName
	result := processor.Name()
	assert.Equal(t, expected, result)
}

func TestGroupShipmentOrderProcessor_IsAbleToProcess(t *testing.T) {
	tests := []struct {
		name           string
		buyerInfo      group_entity.BuyerInfo
		shippingOrder  group_entity.ShippingOrder
		expectedResult bool
	}{
		{
			name: "should process when shopee fulfilment type",
			buyerInfo: group_entity.BuyerInfo{
				UserID: typ.UserIdType(12345),
				Address: entity.ItemGroupBuyerAddress{
					State:          "Test State",
					City:           "Test City",
					District:       "Test District",
					BuyerAddressID: 67890,
					Required:       true,
				},
			},
			shippingOrder: group_entity.ShippingOrder{
				FulfilmentInfo: group_entity.OrderFulfilmentInfo{
					Type: constant.FulfilmentTypeShopee,
				},
				Items: []group_entity.ShippingOrderItem{
					{
						ShopInfo: group_entity.ShopInfo{
							ShopID: 1,
						},
					},
				},
			},
			expectedResult: true,
		},
		{
			name: "should not process when not shopee fulfilment and not cache order",
			buyerInfo: group_entity.BuyerInfo{
				UserID: typ.UserIdType(12345),
				Address: entity.ItemGroupBuyerAddress{
					State:          "Test State",
					City:           "Test City",
					District:       "Test District",
					BuyerAddressID: 67890,
					Required:       true,
				},
			},
			shippingOrder: group_entity.ShippingOrder{
				FulfilmentInfo: group_entity.OrderFulfilmentInfo{
					Type: constant.FulfilmentTypeSeller,
				},
				Items: []group_entity.ShippingOrderItem{
					{
						ShopInfo: group_entity.ShopInfo{
							ShopID: 1,
						},
					},
				},
			},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor := &GroupShipmentOrderProcessor{}

			result := processor.IsAbleToProcess(context.Background(), tt.buyerInfo, tt.shippingOrder)

			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestNewGroupShipmentOrderProcessor(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSBSService := sbs.NewMockService(ctrl)
	mockWarehousePriorityService := warehouse_priority.NewMockWarehousePriorityService(ctrl)
	mockAddressService := address.NewMockAddrService(ctrl)
	mockItemOrganizer := organizer.NewMockItemOrganizer(ctrl)

	processor := NewGroupShipmentOrderProcessor(
		mockSBSService,
		mockWarehousePriorityService,
		mockAddressService,
		mockItemOrganizer,
	)

	assert.NotNil(t, processor)
	assert.Equal(t, mockSBSService, processor.sbsService)
	assert.Equal(t, mockWarehousePriorityService, processor.warehousePriorityService)
	assert.Equal(t, mockAddressService, processor.addressService)
	assert.Equal(t, mockItemOrganizer, processor.itemOrganizer)
}

func TestGroupShipmentOrderProcessor_DoProcess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSBSService := sbs.NewMockService(ctrl)
	mockWarehousePriorityService := warehouse_priority.NewMockWarehousePriorityService(ctrl)
	mockAddressService := address.NewMockAddrService(ctrl)
	mockItemOrganizer := organizer.NewMockItemOrganizer(ctrl)

	processor := NewGroupShipmentOrderProcessor(
		mockSBSService,
		mockWarehousePriorityService,
		mockAddressService,
		mockItemOrganizer,
	)

	buyerInfo := group_entity.BuyerInfo{
		UserID: typ.UserIdType(12345),
		Address: entity.ItemGroupBuyerAddress{
			State:          "Test State",
			City:           "Test City",
			District:       "Test District",
			BuyerAddressID: 67890,
			Required:       true,
		},
	}

	shippingOrders := []group_entity.ShippingOrder{
		{
			FulfilmentInfo: group_entity.OrderFulfilmentInfo{
				Type: constant.FulfilmentTypeShopee,
			},
			Items: []group_entity.ShippingOrderItem{
				{
					ShopInfo: group_entity.ShopInfo{
						ShopID: 1,
					},
					ItemID:    123,
					ModelID:   456,
					Quantity:  2,
					IsSBSItem: false,
				},
			},
		},
	}

	// Mock GetSBSShopItemInfos to return empty results to avoid complex setup
	mockSBSService.EXPECT().
		GetSBSShopItemInfos(gomock.Any(), gomock.Any()).
		Return([]entity.SBSShopInfo{}, nil).
		Times(1)

	// Mock BatchGetSBSShipmentGroups which is also called in the process
	mockSBSService.EXPECT().
		BatchGetSBSShipmentGroups(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(map[uint32]entity.SBSShipmentGroup{}, nil).
		AnyTimes()

	// Mock WarehousePriorityService.BatchGetItemOrganizeStrategy
	mockWarehousePriorityService.EXPECT().
		BatchGetItemOrganizeStrategy(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(map[uint64]constant.ItemOrganizeStrategy{
			1: constant.ItemOrganizeStrategyMinimumParcel,
		}).
		AnyTimes()

	result, err := processor.DoProcess(context.Background(), buyerInfo, shippingOrders)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	// The result should be the same as input when no SBS processing is needed
	assert.Equal(t, len(shippingOrders), len(result))
}

func TestGroupShipmentOrderProcessor_DoProcess_WithSBSError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSBSService := sbs.NewMockService(ctrl)
	mockWarehousePriorityService := warehouse_priority.NewMockWarehousePriorityService(ctrl)
	mockAddressService := address.NewMockAddrService(ctrl)
	mockItemOrganizer := organizer.NewMockItemOrganizer(ctrl)

	processor := NewGroupShipmentOrderProcessor(
		mockSBSService,
		mockWarehousePriorityService,
		mockAddressService,
		mockItemOrganizer,
	)

	buyerInfo := group_entity.BuyerInfo{
		UserID: typ.UserIdType(12345),
	}

	shippingOrders := []group_entity.ShippingOrder{
		{
			FulfilmentInfo: group_entity.OrderFulfilmentInfo{
				Type: constant.FulfilmentTypeShopee,
			},
			Items: []group_entity.ShippingOrderItem{
				{
					ShopInfo: group_entity.ShopInfo{
						ShopID: 1,
					},
					ItemID:  123,
					ModelID: 456,
				},
			},
		},
	}

	// Mock GetSBSShopItemInfos to return an error
	mockSBSService.EXPECT().
		GetSBSShopItemInfos(gomock.Any(), gomock.Any()).
		Return(nil, assert.AnError).
		Times(1)

	result, err := processor.DoProcess(context.Background(), buyerInfo, shippingOrders)

	assert.Error(t, err)
	assert.Nil(t, result)
}
