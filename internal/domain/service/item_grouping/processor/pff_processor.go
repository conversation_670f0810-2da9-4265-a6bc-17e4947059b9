package processor

import (
	"context"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/abtesting"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type PffShippingOrderProcessor struct {
	confAccessor            config.ConfAccessor
	addressService          address.AddrService
	whsPriorityService      warehouse_priority.WarehousePriorityService
	abtestService           abtesting.Service
	itemOrganizer           organizer.ItemOrganizer
	salesOrdersCountService order.SalesOrdersCountService
	shopLocalSIPService     shop_local_sip.ShopLocalSIPService
}

func NewPffShippingOrderProcessor(
	confAccessor config.ConfAccessor,
	addressService address.AddrService,
	whsPriorityService warehouse_priority.WarehousePriorityService,
	abTestService abtesting.Service,
	itemOrganizer organizer.ItemOrganizer,
	salesOrdersCountService order.SalesOrdersCountService,
	shopLocalSIPService shop_local_sip.ShopLocalSIPService) *PffShippingOrderProcessor {
	return &PffShippingOrderProcessor{
		confAccessor:            confAccessor,
		addressService:          addressService,
		whsPriorityService:      whsPriorityService,
		abtestService:           abTestService,
		itemOrganizer:           itemOrganizer,
		salesOrdersCountService: salesOrdersCountService,
		shopLocalSIPService:     shopLocalSIPService,
	}
}

func (p *PffShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return PFFShippingOrderProcessorName
}

func (p *PffShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		shippingOrder.ShopInfo().ShopeeWarehouseFulfilmentCapability && shippingOrder.ShopInfo().SellerStockFulfilmentCapability
}

func (p *PffShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, fsserr.Error) {
	shopIDSet := collection.NewSet[uint64]()
	for index := range shippingOrders {
		shopIDSet.Add(shippingOrders[index].ShopInfo().ShopID)
	}
	shopIDToStrategy := p.whsPriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDSet.Values(),
		constant.ItemOrganizeStrategyMinimumParcel,
	)

	var processedOrders []group_entity.ShippingOrder
	for _, shippingOrder := range shippingOrders {
		splitOrders, err := p.splitSingleShippingOrder(ctx, buyerInfo, shippingOrder, shopIDToStrategy[shippingOrder.ShopInfo().ShopID])
		if err != nil {
			return nil, err
		}

		processedOrders = append(processedOrders, splitOrders...)
	}
	return processedOrders, nil
}

func (p *PffShippingOrderProcessor) splitSingleShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	if p.confAccessor.GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix(ctx) {
		return p.splitSingleShippingOrderV2(ctx, buyerInfo, shippingOrder, strategy)
	}
	if p.shouldUseSellerMultiWHPFFFlow(ctx, buyerInfo.UserID, shippingOrder.ShopInfo()) {
		return p.splitSingleSellerMultiWHPFFShippingOrder(ctx, buyerInfo, shippingOrder, constant.ItemOrganizeStrategyMinimumParcel)
	} else {
		if shippingOrder.ShopInfo().PFFCheckoutImprovementWhitelist {
			return p.splitSingleImprovedPFFShippingOrder(ctx, buyerInfo, shippingOrder, strategy)
		}
		return p.splitSingleNormalPFFShippingOrder(ctx, buyerInfo, shippingOrder, strategy)
	}
}

func (p *PffShippingOrderProcessor) splitSingleShippingOrderV2(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	if !shippingOrder.ShopInfo().PFFCheckoutImprovementWhitelist {
		return p.splitSingleNormalPFFShippingOrder(ctx, buyerInfo, shippingOrder, strategy)
	}

	isPrioritizeSellerWarehouse, enableAllocateByRatio := p.getIsPrioritizeSellerWarehouse(ctx, shippingOrder, buyerInfo.Address)
	if enableAllocateByRatio {
		return p.splitSingleRatioAllocationPFFShippingOrder(ctx, buyerInfo, shippingOrder, strategy, isPrioritizeSellerWarehouse)
	}

	if !p.shouldUseSellerMultiWHPFFFlow(ctx, buyerInfo.UserID, shippingOrder.ShopInfo()) {
		return p.splitSingleSellerMultiWHPFFShippingOrder(ctx, buyerInfo, shippingOrder, strategy)
	}

	return p.splitSingleSellerMultiWHPFFShippingOrder(ctx, buyerInfo, shippingOrder, constant.ItemOrganizeStrategyMinimumParcel)
}

func (p *PffShippingOrderProcessor) splitSingleSellerMultiWHPFFShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	keyToItems, err := p.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		p.pffSourceSelectForSellerMultiWH,
		p.pffSourcePrioritize(ctx, shippingOrder.ShopInfo(), buyerInfo.Address),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		if key.FulfilmentType == constant.FulfilmentTypeShopee {
			warehouseAddress, err := p.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = warehouseAddress.AddressID
		}
	}
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	splitOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
	metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFSellerMultiWH, len(splitOrders))
	return splitOrders, nil
}

func (p *PffShippingOrderProcessor) splitSingleImprovedPFFShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	keyToItems, err := p.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		p.pffSourceSelect,
		p.pffSourcePrioritize(ctx, shippingOrder.ShopInfo(), buyerInfo.Address),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		if key.FulfilmentType == constant.FulfilmentTypeShopee {
			warehouseAddress, err := p.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = warehouseAddress.AddressID
		}
	}

	return organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (p *PffShippingOrderProcessor) splitSingleNormalPFFShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	shopeeItems, sellerItems, err := p.splitItemsByFulfilmentType(ctx, shippingOrder, false)
	if err != nil {
		return nil, err
	}

	var splitOrders []group_entity.ShippingOrder
	if len(shopeeItems) > 0 {
		keyToItems, err := p.itemOrganizer.OrganizeItems(
			ctx,
			shopeeItems,
			p.shopeeSourceSelect,
			p.shopeeSourcePrioritize(ctx, buyerInfo.Address),
			strategy,
		)
		if err != nil {
			return nil, err
		}

		sourceToAddressID := make(map[string]uint64)
		for key := range keyToItems {
			warehouseAddress, err := p.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = warehouseAddress.AddressID
		}

		shopeeOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFShopee, len(shopeeOrders))
		splitOrders = append(splitOrders, shopeeOrders...)
	}
	if len(sellerItems) > 0 {
		keyToItems, err := p.itemOrganizer.OrganizeItems(
			ctx,
			sellerItems,
			p.sellerSourceSelect,
			p.sellerSourcePrioritize(ctx, buyerInfo, shippingOrder.ShopInfo()),
			strategy,
		)
		if err != nil {
			return nil, err
		}
		sourceToAddressID := make(map[string]uint64)
		for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
			sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
		}

		sellerOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFSeller, len(sellerOrders))
		splitOrders = append(splitOrders, sellerOrders...)
	}

	return splitOrders, nil
}

func (p *PffShippingOrderProcessor) splitSingleRatioAllocationPFFShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
	isPrioritizeSellerWarehouse bool,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	shopeeItems, sellerItems, err := p.splitItemsByFulfilmentType(ctx, shippingOrder, isPrioritizeSellerWarehouse)
	if err != nil {
		return nil, err
	}

	var splitOrders []group_entity.ShippingOrder
	if len(shopeeItems) > 0 {
		keyToItems, err := p.itemOrganizer.OrganizeItems(
			ctx,
			shopeeItems,
			p.shopeeSourceSelect,
			p.pffSourcePrioritize(ctx, shippingOrder.ShopInfo(), buyerInfo.Address),
			strategy,
		)
		if err != nil {
			return nil, err
		}

		sourceToAddressID := make(map[string]uint64)
		for key := range keyToItems {
			warehouseAddress, err := p.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = warehouseAddress.AddressID
		}

		shopeeOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFShopee, len(shopeeOrders))
		splitOrders = append(splitOrders, shopeeOrders...)
	}
	if len(sellerItems) > 0 {
		keyToItems, err := p.itemOrganizer.OrganizeItems(
			ctx,
			sellerItems,
			p.sellerSourceSelect,
			p.pffSourcePrioritize(ctx, shippingOrder.ShopInfo(), buyerInfo.Address),
			strategy,
		)
		if err != nil {
			return nil, err
		}

		sourceToAddressID := make(map[string]uint64)
		for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
			sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
		}

		sellerOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFSeller, len(sellerOrders))
		splitOrders = append(splitOrders, sellerOrders...)
	}

	return splitOrders, nil
}

func (p *PffShippingOrderProcessor) splitItemsByFulfilmentType(
	ctx context.Context,
	shippingOrder group_entity.ShippingOrder,
	isPrioritizeSellerWarehouse bool,
) ([]group_entity.ShippingOrderItem, []group_entity.ShippingOrderItem, fsserr.Error) {
	var itemSplitter organizer.ItemSplitter[constant.FulfilmentType]
	if isPrioritizeSellerWarehouse {
		itemSplitter = organizer.NewFulfilmentTypeItemSplitter([]constant.FulfilmentType{constant.FulfilmentTypeSeller, constant.FulfilmentTypeShopee}, nil)
	} else {
		itemSplitter = organizer.NewFulfilmentTypeItemSplitter([]constant.FulfilmentType{constant.FulfilmentTypeShopee, constant.FulfilmentTypeSeller}, nil)
	}

	fulfilmentTypeToItems, err := itemSplitter.SplitItems(ctx, shippingOrder.Items)
	if err != nil {
		return nil, nil, err
	}

	shopeeItems := fulfilmentTypeToItems[constant.FulfilmentTypeShopee]
	sellerItems := fulfilmentTypeToItems[constant.FulfilmentTypeSeller]
	return shopeeItems, sellerItems, nil
}

func (p *PffShippingOrderProcessor) pffSourceSelectForSellerMultiWH(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
	locations := item.ShopeeStocks()
	locations = append(locations, item.SellerStocks()...)
	return locations, nil
}

func (p *PffShippingOrderProcessor) pffSourceSelect(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
	locations := item.ShopeeStocks()
	if len(item.SellerStocks()) > 1 {
		locations = append(locations, item.SellerStocks()[:1]...)
	} else {
		locations = append(locations, item.SellerStocks()...)
	}
	return locations, nil
}

func (p *PffShippingOrderProcessor) pffSourcePrioritize(
	ctx context.Context,
	shopInfo group_entity.ShopInfo,
	buyerAddress entity.ItemGroupBuyerAddress,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		return p.whsPriorityService.PrioritizeShopeeAndSellerWarehouse(ctx, shopInfo.ShopID, buyerAddress, sources)
	}
}

func (p *PffShippingOrderProcessor) shopeeSourceSelect(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
	return item.ShopeeStocks(), nil
}

func (p *PffShippingOrderProcessor) shopeeSourcePrioritize(
	ctx context.Context,
	buyerAddress entity.ItemGroupBuyerAddress,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		return p.whsPriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddress, sources)
	}
}

func (p *PffShippingOrderProcessor) sellerSourceSelect(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
	return item.SellerStocks(), nil
}

func (p *PffShippingOrderProcessor) sellerSourcePrioritize(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopInfo group_entity.ShopInfo,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		if len(sources) <= 1 {
			return sources, nil
		}
		buyerAddressGeo, err := p.getBuyerGeoLocation(ctx, buyerInfo, shopInfo.ShopID)
		if err != nil {
			return nil, err
		}
		if shopInfo.SellerWarehouseFlag.IsMultiWarehouse() {
			return p.whsPriorityService.PrioritizeSellerMultiWarehouse(
				ctx,
				buyerAddressGeo,
				shopInfo.SellerID,
				shopInfo.Warehouses,
				sources,
			), nil
		}
		sort.SliceStable(sources, func(i, j int) bool {
			return sources[i] < sources[j]
		})
		return sources, nil
	}
}

// only need the buyer address Geo for multi-warehouse shop
// for SIP affiliated shop, we need the dummy buyer Geo instead of real buyer Geo
func (p *PffShippingOrderProcessor) getBuyerGeoLocation(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopID uint64,
) (entity.GeoLocation, fsserr.Error) {
	localSIPInfo, err := p.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get local SIP shop info error: %v", err)
	}
	var buyerAddressGeo entity.GeoLocation

	if localSIPInfo.IsSIPAffiliated {
		buyerAddressGeo, err = p.addressService.GetDummyBuyerGeoLocation(ctx, localSIPInfo.DummyBuyerID, localSIPInfo.SIPPrimaryRegion)
	} else {
		buyerAddressGeo, err = p.addressService.GetBuyerGeoLocation(ctx, buyerInfo.UserID, buyerInfo.Address.BuyerAddressID)
	}
	if err != nil {
		return entity.GeoLocation{}, err
	}

	return buyerAddressGeo, nil
}

func (p *PffShippingOrderProcessor) shouldUseSellerMultiWHPFFFlow(ctx context.Context, buyerID typ.UserIdType, shopInfo group_entity.ShopInfo) bool {
	if !p.confAccessor.GetSupportSellerMultiWhPFF(ctx) {
		return false
	}
	if !shopInfo.SellerWarehouseFlag.IsMultiWarehouse() {
		return false
	}

	sellerMultiWHWithPartialFBSConfig := p.confAccessor.GetSellerMultiWHWithPartialFBSConfig(ctx)
	result, err := p.abtestService.CheckUserInExperimentGroupUsingFeature(ctx, buyerID, sellerMultiWHWithPartialFBSConfig.ABTestConfig)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "CheckUserInExperimentGroupUsingFeature error, user_id: %d, cfg: %s", buyerID, Logger.JsonString(sellerMultiWHWithPartialFBSConfig.ABTestConfig))
		return false
	}
	return result
}

// getIsPrioritizeSellerWarehouse checks if the seller warehouse should be prioritized based on sales orders count and ratio.
// It returns whether to prioritize the seller warehouse and whether this shop in the allocate warehouse by sales orders group.
func (p *PffShippingOrderProcessor) getIsPrioritizeSellerWarehouse(ctx context.Context, shippingOrder group_entity.ShippingOrder, buyerAddress entity.ItemGroupBuyerAddress) (bool, bool) {
	if !p.confAccessor.GetEnableAllocateWarehouseBySalesOrders(ctx) {
		return false, false
	}
	group := p.getAllocateWarehouseBySalesOrdersGroup(ctx, shippingOrder.ShopInfo().PFFAllocateBySalesOrdersGroupTag)
	if group.SellerTag == "" {
		Logger.CtxLogErrorf(
			ctx,
			"allocate warehouse by sales orders group not found, shop_id: %d, group_tag: %s",
			shippingOrder.ShopInfo().ShopID,
			shippingOrder.ShopInfo().PFFAllocateBySalesOrdersGroupTag)

		return false, false
	}

	compare, err := p.whsPriorityService.CompareShopeeAndSellerWarehousePriorityByESF(
		ctx,
		shippingOrder.ShopInfo().ShopID,
		buyerAddress,
		p.getAllAllocatableWarehouses(shippingOrder),
	)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "compare shopee and seller warehouse priority failed: %v", err)
		return false, true
	}
	if compare != 0 {
		return compare > 0, true
	}

	count, err := p.salesOrdersCountService.GetShopSalesOrdersCount(ctx, shippingOrder.ShopInfo().ShopID, group.CalculateFromPastDays)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get shop sales orders count failed: %v", err)
		return false, true
	}

	if group.SellerWarehouseSalesOrdersMax != 0 && count.SellerWarehouseOrdersCount >= group.SellerWarehouseSalesOrdersMax {
		return false, true
	}
	if group.SellerWarehouseSalesOrdersMin != 0 && count.SellerWarehouseOrdersCount < group.SellerWarehouseSalesOrdersMin {
		return true, true
	}
	if group.SellerWarehouseSalesOrderRatio == 0 {
		return false, true
	}

	var ratio float64
	if count.SellerWarehouseOrdersCount+count.ShopeeWarehouseOrdersCount != 0 {
		ratio = float64(count.SellerWarehouseOrdersCount) / float64(count.SellerWarehouseOrdersCount+count.ShopeeWarehouseOrdersCount)
	}

	return ratio < group.SellerWarehouseSalesOrderRatio, true
}

func (p *PffShippingOrderProcessor) getAllAllocatableWarehouses(shippingOrder group_entity.ShippingOrder) []string {
	warehouseSet := collection.NewSet[string]()
	shippingOrder.ForEachAllItems(func(item group_entity.ShippingOrderItem) {
		for _, location := range item.GetStockLocations(constant.FulfilmentTypeShopee, constant.FulfilmentTypeSeller) {
			if location.AvailableStock > 0 {
				warehouseSet.Add(location.Source)
			}
		}
	})
	return warehouseSet.Values()
}

func (p *PffShippingOrderProcessor) getAllocateWarehouseBySalesOrdersGroup(ctx context.Context, sellerTag string) business_config.AllocateWarehouseBySalesOrdersGroup {
	for _, group := range p.confAccessor.GetAllocateWarehouseBySalesOrderGroups(ctx) {
		if group.SellerTag == sellerTag {
			return group
		}
	}
	return business_config.AllocateWarehouseBySalesOrdersGroup{}
}
