package processor

import (
	"context"
	"slices"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type CBLFFShippingOrderProcessor struct {
	configAccessor           config.ConfAccessor
	itemOrganizer            organizer.ItemOrganizer
	warehousePriorityService warehouse_priority.WarehousePriorityService
	addressService           address.AddrService
	shopLocalSIPService      shop_local_sip.ShopLocalSIPService
}

func NewCBLFFShippingOrderProcessor(
	configAccessor config.ConfAccessor,
	itemOrganizer organizer.ItemOrganizer,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	addressService address.AddrService,
	shopLocalSIPService shop_local_sip.ShopLocalSIPService,
) *CBLFFShippingOrderProcessor {
	return &CBLFFShippingOrderProcessor{
		configAccessor:           configAccessor,
		itemOrganizer:            itemOrganizer,
		warehousePriorityService: warehousePriorityService,
		addressService:           addressService,
		shopLocalSIPService:      shopLocalSIPService,
	}
}

func (c *CBLFFShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return CBLFFShippingOrderProcessorName
}

func (c *CBLFFShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		slices.Contains(c.configAccessor.GetIGSLFFShopFlags(ctx), shippingOrder.ShopInfo().ShopFlag)
}

func (c *CBLFFShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, fsserr.Error) {
	var processedOrders []group_entity.ShippingOrder
	for _, order := range shippingOrders {
		splitOrders, err := c.splitSingleShippingOrder(ctx, buyerInfo, order)
		if err != nil {
			return nil, err
		}

		processedOrders = append(processedOrders, splitOrders...)
	}
	return processedOrders, nil
}

func (c *CBLFFShippingOrderProcessor) splitSingleShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	regionToItems := make(map[string][]group_entity.ShippingOrderItem)
	for _, item := range shippingOrder.Items {
		regionToItem, err := c.splitItemByRegion(ctx, item)
		if err != nil {
			return nil, err
		}
		for region, item := range regionToItem {
			regionToItems[region] = append(regionToItems[region], item)
		}
	}

	var splitOrders []group_entity.ShippingOrder
	for region, items := range regionToItems {
		shopeeItems, sellerItems, err := c.splitItemsByFulfilmentType(ctx, items, region)
		if err != nil {
			return nil, err
		}

		if len(shopeeItems) > 0 {
			shopeeOrders, err := c.organizerShopeeItems(ctx, buyerInfo, shippingOrder, shopeeItems, region)
			if err != nil {
				return nil, err
			}
			splitOrders = append(splitOrders, shopeeOrders...)
		}
		if len(sellerItems) > 0 {
			sellerOrders, err := c.organizerSellerItems(ctx, buyerInfo, shippingOrder, sellerItems, region)
			if err != nil {
				return nil, err
			}
			splitOrders = append(splitOrders, sellerOrders...)
		}
	}

	return splitOrders, nil
}

func (c *CBLFFShippingOrderProcessor) splitItemByRegion(ctx context.Context, item group_entity.ShippingOrderItem) (
	map[string]group_entity.ShippingOrderItem, fsserr.Error) {

	regionToTotalStock := make(map[string]uint32)
	var totalStock uint32
	for _, location := range item.GetStockLocations(constant.FulfilmentTypeShopee, constant.FulfilmentTypeSeller) {
		region := c.getSourceRegion(location.Source)
		if region == "" {
			Logger.CtxLogErrorf(ctx, "invalid source region, item: %d, source: %s", item.ItemID, location.Source)
			continue
		}
		regionToTotalStock[region] += location.AvailableStock
		totalStock += location.AvailableStock
	}
	if totalStock < item.Quantity {
		return nil, fsserr.NewOutOfStockError(fsserr.OutOfStockErrorData{
			ItemId:             item.ItemID,
			IsPackagePromotion: item.IsPackageItem(),
			ItemStockInfos:     group_entity.BuildOOSItemStockInfos(item, totalStock),
		})
	}

	regionToItem := make(map[string]group_entity.ShippingOrderItem)
	remainingStock := item.Quantity
	lffWarehouseRegionPriority := c.configAccessor.GetLFFWarehouseRegionPriority(ctx)
	if len(lffWarehouseRegionPriority) == 0 {
		localRegion := envvar.GetCID(ctx)
		lffWarehouseRegionPriority = append(lffWarehouseRegionPriority, localRegion)
	}
	for _, region := range lffWarehouseRegionPriority {
		if remainingStock <= 0 {
			break
		}

		reducingStock := regionToTotalStock[region]
		if reducingStock <= 0 {
			continue
		}
		reducingStock = min(reducingStock, remainingStock)
		remainingStock -= reducingStock

		newItem := item
		newItem.Quantity = reducingStock
		regionToItem[region] = newItem
	}

	if remainingStock > 0 {
		newItem := item
		newItem.Quantity = remainingStock
		regionToItem[""] = newItem
	}

	return regionToItem, nil
}

func (c *CBLFFShippingOrderProcessor) getSourceRegion(source string) string {
	if len(source) < 2 {
		return ""
	}
	return strings.ToUpper(source[:2])
}

func (c *CBLFFShippingOrderProcessor) splitItemsByFulfilmentType(
	ctx context.Context,
	items []group_entity.ShippingOrderItem,
	region string,
) ([]group_entity.ShippingOrderItem, []group_entity.ShippingOrderItem, fsserr.Error) {
	var shopeeItems, sellerItems []group_entity.ShippingOrderItem
	for _, item := range items {
		shopeeStock := c.filterLocationsByRegion(ctx, item.ShopeeStocks(), region).TotalStock()
		sellerStock := c.filterLocationsByRegion(ctx, item.SellerStocks(), region).TotalStock()

		if shopeeStock >= item.Quantity {
			shopeeItems = append(shopeeItems, item)
			continue
		}
		if sellerStock >= item.Quantity {
			sellerItems = append(sellerItems, item)
			continue
		}
		totalStock := shopeeStock + sellerStock
		if totalStock < item.Quantity {
			return nil, nil, fsserr.NewOutOfStockError(fsserr.OutOfStockErrorData{
				ItemId:             item.ItemID,
				IsPackagePromotion: item.IsPackageItem(),
				ItemStockInfos:     group_entity.BuildOOSItemStockInfos(item, totalStock),
			})
		}

		shopeeItem := item
		shopeeItem.Quantity = shopeeStock
		shopeeItems = append(shopeeItems, shopeeItem)
		sellerItem := item
		sellerItem.Quantity = item.Quantity - shopeeStock
		sellerItems = append(sellerItems, sellerItem)
	}

	return shopeeItems, sellerItems, nil
}

func (c *CBLFFShippingOrderProcessor) organizerShopeeItems(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	items []group_entity.ShippingOrderItem,
	region string,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	keyToItems, err := c.itemOrganizer.OrganizeItems(
		ctx,
		items,
		c.shopeeSourceSelect(ctx, region),
		c.shopeeSourcePrioritize(ctx, buyerInfo.Address),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		if key.FulfilmentType == constant.FulfilmentTypeShopee {
			addr, err := c.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = addr.AddressID
		}
	}

	return organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (c *CBLFFShippingOrderProcessor) organizerSellerItems(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	items []group_entity.ShippingOrderItem,
	region string,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	keyToItems, err := c.itemOrganizer.OrganizeItems(
		ctx,
		items,
		c.sellerSourceSelect(ctx, region),
		c.sellerSourcePrioritize(ctx, buyerInfo, shippingOrder.ShopInfo()),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	return organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (c *CBLFFShippingOrderProcessor) shopeeSourceSelect(ctx context.Context, region string) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
		return c.filterLocationsByRegion(ctx, item.ShopeeStocks(), region), nil
	}
}

func (c *CBLFFShippingOrderProcessor) shopeeSourcePrioritize(ctx context.Context, buyerAddress entity.ItemGroupBuyerAddress) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		return c.warehousePriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddress, sources)
	}
}

func (c *CBLFFShippingOrderProcessor) sellerSourceSelect(ctx context.Context, region string) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
		return c.filterLocationsByRegion(ctx, item.SellerStocks(), region), nil
	}
}

func (c *CBLFFShippingOrderProcessor) sellerSourcePrioritize(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopInfo group_entity.ShopInfo,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		if len(sources) <= 1 {
			return sources, nil
		}
		buyerAddressGeo, err := c.getBuyerGeoLocation(ctx, buyerInfo, shopInfo.ShopID)
		if err != nil {
			return nil, err
		}
		return c.warehousePriorityService.PrioritizeSellerMultiWarehouse(
			ctx,
			buyerAddressGeo,
			shopInfo.SellerID,
			shopInfo.Warehouses,
			sources,
		), nil
	}
}

func (c *CBLFFShippingOrderProcessor) getBuyerGeoLocation(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopID uint64,
) (entity.GeoLocation, fsserr.Error) {
	localSIPInfo, err := c.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get local SIP shop info error: %v", err)
	}
	var buyerAddressGeo entity.GeoLocation

	if localSIPInfo.IsSIPAffiliated {
		buyerAddressGeo, err = c.addressService.GetDummyBuyerGeoLocation(ctx, localSIPInfo.DummyBuyerID, localSIPInfo.SIPPrimaryRegion)
	} else {
		buyerAddressGeo, err = c.addressService.GetBuyerGeoLocation(ctx, buyerInfo.UserID, buyerInfo.Address.BuyerAddressID)
	}
	if err != nil {
		return entity.GeoLocation{}, err
	}

	return buyerAddressGeo, nil
}

func (c *CBLFFShippingOrderProcessor) filterLocationsByRegion(
	ctx context.Context,
	locations group_entity.StockLocations,
	region string) group_entity.StockLocations {

	var filteredLocations group_entity.StockLocations
	for _, location := range locations {
		sourceRegion := c.getSourceRegion(location.Source)
		if region == "" {
			lffWarehouseRegionPriority := c.configAccessor.GetLFFWarehouseRegionPriority(ctx)
			if len(lffWarehouseRegionPriority) == 0 {
				localRegion := envvar.GetCID(ctx)
				if sourceRegion != localRegion {
					filteredLocations = append(filteredLocations, location)
				}
			} else {
				if !slices.Contains(c.configAccessor.GetLFFWarehouseRegionPriority(ctx), sourceRegion) {
					filteredLocations = append(filteredLocations, location)
				}
			}
			continue
		}

		if sourceRegion == region {
			filteredLocations = append(filteredLocations, location)
		}
	}
	return filteredLocations
}
