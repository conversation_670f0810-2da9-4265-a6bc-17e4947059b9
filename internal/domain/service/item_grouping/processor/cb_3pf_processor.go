package processor

import (
	"context"
	"slices"
	"sort"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type Cb3pfShippingOrderProcessor struct {
	configAccessor           config.ConfAccessor
	itemOrganizer            organizer.ItemOrganizer
	shopService              shop.ShopService
	addressService           address.AddrService
	warehousePriorityService warehouse_priority.WarehousePriorityService
	shopLocalSIPService      shop_local_sip.ShopLocalSIPService
}

func NewCb3pfShippingOrderProcessor(
	configAccessor config.ConfAccessor,
	itemOrganizer organizer.ItemOrganizer,
	shopService shop.ShopService,
	addressService address.AddrService,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	shopLocalSIPService shop_local_sip.ShopLocalSIPService,
) *Cb3pfShippingOrderProcessor {
	return &Cb3pfShippingOrderProcessor{
		configAccessor:           configAccessor,
		itemOrganizer:            itemOrganizer,
		shopService:              shopService,
		addressService:           addressService,
		warehousePriorityService: warehousePriorityService,
		shopLocalSIPService:      shopLocalSIPService,
	}
}

func (c *Cb3pfShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return CB3PFShippingOrderProcessorName
}

func (c *Cb3pfShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		slices.Contains(c.configAccessor.GetIGS3PFShopFlags(ctx), shippingOrder.ShopInfo().ShopFlag)
}

func (c *Cb3pfShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, fsserr.Error) {
	if c.configAccessor.GetEnable3PFIgnoreSellerTag(ctx) {
		return c.doProcessWithoutSellerTag(ctx, buyerInfo, shippingOrders)
	} else {
		return c.doProcessWithSellerTag(ctx, shippingOrders)
	}
}

func (c *Cb3pfShippingOrderProcessor) doProcessWithoutSellerTag(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	cb3PFHybridOrders, cb3PFOnlyOrders, err := c.separateShippingOrders(shippingOrders)
	if err != nil {
		return nil, err
	}

	var processedOrders []group_entity.ShippingOrder
	for i, order := range append(cb3PFHybridOrders, cb3PFOnlyOrders...) {
		splitOrders, err := c.splitShippingOrderWithoutSellerTag(
			ctx,
			buyerInfo,
			order,
			i < len(cb3PFHybridOrders),
		)
		if err != nil {
			return nil, err
		}
		processedOrders = append(processedOrders, splitOrders...)
	}

	return processedOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) doProcessWithSellerTag(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	shopIDSet := collection.NewSet[uint64]()
	for _, order := range shippingOrders {
		shopIDSet.Add(order.ShopInfo().ShopID)
	}
	shopIDs := shopIDSet.ToSlice()

	shopIDToStrategy := c.getShopStrategy(ctx, shopIDs)
	cb3PFHybridOrders, cb3PFOnlyOrders, err := c.separateShippingOrders(shippingOrders)
	if err != nil {
		return nil, err
	}

	var processedOrders []group_entity.ShippingOrder
	for i, order := range append(cb3PFHybridOrders, cb3PFOnlyOrders...) {
		splitOrders, err := c.splitShippingOrderWithSellerTag(
			ctx,
			order,
			shopIDToStrategy[order.ShopInfo().ShopID],
			i < len(cb3PFHybridOrders),
		)
		if err != nil {
			return nil, err
		}
		processedOrders = append(processedOrders, splitOrders...)
	}

	return processedOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) separateShippingOrders(
	shippingOrders []group_entity.ShippingOrder,
) (cb3PFHybridOrders []group_entity.ShippingOrder, cb3PFOnlyOrders []group_entity.ShippingOrder, err fsserr.Error) {
	for _, order := range shippingOrders {
		if len(order.ShopInfo().Warehouses) == 0 {
			cb3PFOnlyOrders = append(cb3PFOnlyOrders, order)
		} else {
			cb3PFHybridOrders = append(cb3PFHybridOrders, order)
		}
		continue
	}
	return
}

func (c *Cb3pfShippingOrderProcessor) getShopStrategy(
	ctx context.Context,
	shopIDs []uint64,
) map[uint64]constant.ItemOrganizeStrategy {
	return c.warehousePriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDs,
		constant.ItemOrganizeStrategyWarehousePriority,
	)
}

func (c *Cb3pfShippingOrderProcessor) splitShippingOrderWithoutSellerTag(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	is3PFHybrid bool,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	regionToItems := make(map[string][]group_entity.ShippingOrderItem)
	for _, item := range shippingOrder.Items {
		regionToItem, err := c.splitItemByRegion(ctx, item)
		if err != nil {
			return nil, err
		}
		for region, regionItem := range regionToItem {
			regionToItems[region] = append(regionToItems[region], regionItem)
		}
	}

	var splitOrders []group_entity.ShippingOrder
	for region, items := range regionToItems {
		keyToItems, err := c.itemOrganizer.OrganizeItems(
			ctx,
			items,
			c.selectSourceFuncWithoutSellerTag(ctx, region),
			c.sourcePrioritizeFuncWithoutSellerTag(ctx, buyerInfo, shippingOrder.ShopInfo()),
			constant.ItemOrganizeStrategyMinimumParcel,
		)
		if err != nil {
			return nil, err
		}

		sourceToAddressID := make(map[string]uint64)
		for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
			sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
		}
		shippingOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)

		splitOrders = append(splitOrders, shippingOrders...)
	}

	c.generateMetrics(ctx, is3PFHybrid, len(splitOrders))
	return splitOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) splitShippingOrderWithSellerTag(
	ctx context.Context,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
	is3PFHybrid bool,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	sourceToShopWarehouse := make(map[string]entity.ShopWarehouse)
	sourceToAddressID := make(map[string]uint64)
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToShopWarehouse[warehouse.LocationID] = warehouse
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	keyToItems, err := c.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		c.selectSourceFuncWithSellerTag(ctx, is3PFHybrid, sourceToShopWarehouse),
		c.sourcePrioritizeFuncWithSellerTag(ctx, is3PFHybrid, sourceToShopWarehouse),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	splitOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
	c.generateMetrics(ctx, is3PFHybrid, len(splitOrders))
	return splitOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) splitItemByRegion(ctx context.Context,
	item group_entity.ShippingOrderItem) (map[string]group_entity.ShippingOrderItem, fsserr.Error) {
	regionToTotalStock := make(map[string]uint32)
	var totalStock uint32
	for _, location := range item.SellerStocks() {
		region := c.getSourceRegion(location.Source)
		if region == "" {
			Logger.CtxLogErrorf(ctx, "invalid source region, item: %d, source: %s", item.ItemID, location.Source)
			continue
		}
		regionToTotalStock[region] += location.AvailableStock
		totalStock += location.AvailableStock
	}
	if totalStock < item.Quantity {
		return nil, fsserr.NewOutOfStockError(fsserr.OutOfStockErrorData{
			ItemId:             item.ItemID,
			IsPackagePromotion: item.IsPackageItem(),
			ItemStockInfos:     group_entity.BuildOOSItemStockInfos(item, totalStock),
		})
	}

	regionToItem := make(map[string]group_entity.ShippingOrderItem)
	remainingStock := item.Quantity
	localRegion := []string{envvar.GetCID(ctx)}
	for _, region := range localRegion {
		if remainingStock <= 0 {
			break
		}

		reducingStock := regionToTotalStock[region]
		if reducingStock <= 0 {
			continue
		}
		reducingStock = min(reducingStock, remainingStock)
		remainingStock -= reducingStock

		newItem := item
		newItem.Quantity = reducingStock
		regionToItem[region] = newItem
	}

	if remainingStock > 0 {
		newItem := item
		newItem.Quantity = remainingStock
		regionToItem[""] = newItem
	}

	return regionToItem, nil
}

func (c *Cb3pfShippingOrderProcessor) getSourceRegion(source string) string {
	if len(source) < 2 {
		return ""
	}
	return strings.ToUpper(source[:2])
}

func (c *Cb3pfShippingOrderProcessor) selectSourceFuncWithoutSellerTag(ctx context.Context, region string) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
		sellerStocks := c.filterLocationsByRegion(ctx, item.SellerStocks(), region)
		return sellerStocks, nil
	}
}

func (c *Cb3pfShippingOrderProcessor) filterLocationsByRegion(ctx context.Context, locations group_entity.StockLocations, region string) group_entity.StockLocations {
	var filteredLocations group_entity.StockLocations
	for _, location := range locations {
		sourceRegion := c.getSourceRegion(location.Source)
		localRegion := envvar.GetCID(ctx)
		if region == "" {
			if sourceRegion != localRegion {
				filteredLocations = append(filteredLocations, location)
			}
		} else {
			if sourceRegion == localRegion {
				filteredLocations = append(filteredLocations, location)
			}
		}
	}
	return filteredLocations
}

func (c *Cb3pfShippingOrderProcessor) selectSourceFuncWithSellerTag(
	ctx context.Context,
	is3PFHybrid bool,
	sourceToShopWarehouse map[string]entity.ShopWarehouse,
) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
		if is3PFHybrid {
			var validateStock []group_entity.ItemStockLocation
			var localStockCount int
			for _, location := range item.SellerStocks() {
				if sourceToShopWarehouse[location.Source].AddressID != 0 {
					validateStock = append(validateStock, location)
					if sourceToShopWarehouse[location.Source].IsLocalWarehouse(ctx) {
						localStockCount++
					}
				}
			}

			if len(validateStock) == 0 {
				Logger.CtxLogErrorf(ctx, "3PF hybrid item has no valid stock, item: %d", item.ItemID)
				return nil, fsserr.Err3PFShopNoWH
			}
			if localStockCount > 1 {
				Logger.CtxLogErrorf(ctx, "3PF hybrid item has more than 1 local stock, item: %d", item.ItemID)
				return nil, fsserr.Err3PFShopNoWH
			}

			return validateStock, nil
		}

		if len(item.SellerStocks()) != 1 {
			Logger.CtxLogErrorf(ctx, "3PF only item should only have 1 stock, item: %d", item.ItemID)
			return nil, fsserr.Err3PFShopNoWH
		}

		return item.SellerStocks(), nil
	}
}

func (c *Cb3pfShippingOrderProcessor) sourcePrioritizeFuncWithoutSellerTag(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopInfo group_entity.ShopInfo,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		if len(sources) <= 1 {
			return sources, nil
		}
		buyerAddressGeo, err := c.getBuyerGeoLocation(ctx, buyerInfo, shopInfo.ShopID)
		if err != nil {
			return nil, err
		}
		return c.warehousePriorityService.PrioritizeSellerMultiWarehouse(
			ctx,
			buyerAddressGeo,
			shopInfo.SellerID,
			shopInfo.Warehouses,
			sources,
		), nil
	}
}

// only need the buyer address Geo for multi-warehouse shop
// for SIP affiliated shop, we need the dummy buyer Geo instead of real buyer Geo
func (c *Cb3pfShippingOrderProcessor) getBuyerGeoLocation(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopID uint64,
) (entity.GeoLocation, fsserr.Error) {
	localSIPInfo, err := c.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get local SIP shop info error, err: %v", err)
	}
	var buyerAddressGeo entity.GeoLocation

	if localSIPInfo.IsSIPAffiliated {
		buyerAddressGeo, err = c.addressService.GetDummyBuyerGeoLocation(ctx, localSIPInfo.DummyBuyerID, localSIPInfo.SIPPrimaryRegion)
	} else {
		buyerAddressGeo, err = c.addressService.GetBuyerGeoLocation(ctx, buyerInfo.UserID, buyerInfo.Address.BuyerAddressID)
	}
	if err != nil {
		return entity.GeoLocation{}, err
	}

	return buyerAddressGeo, nil
}

func (c *Cb3pfShippingOrderProcessor) sourcePrioritizeFuncWithSellerTag(
	ctx context.Context,
	is3PFHybrid bool,
	sourceToShopWarehouse map[string]entity.ShopWarehouse,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		if is3PFHybrid {
			// Local warehouse has higher priority
			sort.SliceStable(sources, func(i, j int) bool {
				if sourceToShopWarehouse[sources[i]].IsLocalWarehouse(ctx) && !sourceToShopWarehouse[sources[j]].IsLocalWarehouse(ctx) {
					return true
				}
				if !sourceToShopWarehouse[sources[i]].IsLocalWarehouse(ctx) && sourceToShopWarehouse[sources[j]].IsLocalWarehouse(ctx) {
					return false
				}
				return sources[i] < sources[j]
			})
			return sources, nil
		}

		sort.SliceStable(sources, func(i, j int) bool {
			return sources[i] < sources[j]
		})
		return sources, nil
	}
}

func (c *Cb3pfShippingOrderProcessor) generateMetrics(ctx context.Context, is3PFHybrid bool, count int) {
	if is3PFHybrid {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderType3PFHybrid, count)
	} else {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderType3PFOnly, count)
	}
}
