package processor

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/sbs"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type GroupShipmentOrderProcessor struct {
	sbsService               sbs.Service
	warehousePriorityService warehouse_priority.WarehousePriorityService
	addressService           address.AddrService
	itemOrganizer            organizer.ItemOrganizer
}

func NewGroupShipmentOrderProcessor(
	sbsService sbs.Service,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	addressService address.AddrService,
	itemOrganizer organizer.ItemOrganizer,
) *GroupShipmentOrderProcessor {
	return &GroupShipmentOrderProcessor{
		sbsService:               sbsService,
		warehousePriorityService: warehousePriorityService,
		addressService:           addressService,
		itemOrganizer:            itemOrganizer,
	}
}

func (g *GroupShipmentOrderProcessor) Name() ShippingOrderProcessorName {
	return GroupShipmentProcessorName
}

func (g *GroupShipmentOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return shippingOrder.FulfilmentInfo.Type == constant.FulfilmentTypeShopee || shippingOrder.IsCacheOrder()
}

func (g *GroupShipmentOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, fsserr.Error) {
	return g.Process(ctx, buyerInfo, shippingOrders)
}

func (g *GroupShipmentOrderProcessor) Process(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	// 批量获取SBS店铺和商品信息
	shopIDToSBSInfo, itemIDToModelIDToSBSInfo, err := g.getSBSShopAndItemInfos(ctx, shippingOrders)
	if err != nil {
		return nil, err
	}

	// 更新订单商品的SBS标识
	shippingOrders = g.updateShippingOrderItemIsSBS(shippingOrders, itemIDToModelIDToSBSInfo)

	// 收集所有团购组ID
	shipmentGroupIDs := collection.NewSet[uint32]()
	for _, info := range shopIDToSBSInfo {
		if info.ShipmentGroupID != 0 {
			shipmentGroupIDs.Add(info.ShipmentGroupID)
		}
	}

	// 批量获取团购组信息
	groupIDToShipmentGroup, err := g.sbsService.BatchGetSBSShipmentGroups(ctx, shipmentGroupIDs.ToSlice(), "")
	if err != nil {
		return nil, err
	}

	// 按团购组ID分组订单
	var processedShippingOrders []group_entity.ShippingOrder
	groupShipmentIDToShippingOrders := make(map[uint32][]group_entity.ShippingOrder)
	shopIDSet := collection.NewSet[uint64]()

	for _, order := range shippingOrders {
		shopIDSet.Add(order.ShopInfo().ShopID)
		groupShipmentID := shopIDToSBSInfo[order.ShopInfo().ShopID].ShipmentGroupID
		if groupShipmentID == 0 {
			processedShippingOrders = append(processedShippingOrders, order)
			continue
		}
		groupShipmentIDToShippingOrders[groupShipmentID] = append(groupShipmentIDToShippingOrders[groupShipmentID], order)
	}

	// 批量获取商品组织策略
	shopIDToStrategy := g.warehousePriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDSet.ToSlice(),
		constant.ItemOrganizeStrategyWarehousePriority, // 与FP保持一致，使用WarehousePriority作为默认
	)

	// 处理每个团购组的订单
	for groupShipmentID, orders := range groupShipmentIDToShippingOrders {
		processedOrders, err := g.groupShippingOrders(
			ctx,
			orders,
			buyerInfo.Address,
			groupIDToShipmentGroup[groupShipmentID],
			shopIDToSBSInfo,
			itemIDToModelIDToSBSInfo,
			shopIDToStrategy,
		)
		if err != nil {
			return nil, err
		}
		processedShippingOrders = append(processedShippingOrders, processedOrders...)
	}

	return processedShippingOrders, nil
}

func (g *GroupShipmentOrderProcessor) getSBSShopAndItemInfos(ctx context.Context, shippingOrders []group_entity.ShippingOrder) (
	map[uint64]entity.SBSShopInfo,
	map[uint64]map[uint64]entity.SBSItemModelInfo,
	fsserr.Error,
) {
	shopIDToItemModelIDs := make(map[uint64][]entity.ItemModelID)
	for _, shippingOrder := range shippingOrders {
		shippingOrder.ForEachAllItems(func(item group_entity.ShippingOrderItem) {
			shopID := item.GetShopInfo().ShopID
			shopIDToItemModelIDs[shopID] = append(
				shopIDToItemModelIDs[shopID],
				entity.ItemModelID{
					ItemID:  item.ItemID,
					ModelID: item.ModelID,
				},
			)
		})
	}

	sbsShopInfos, err := g.sbsService.GetSBSShopItemInfos(ctx, shopIDToItemModelIDs)
	if err != nil {
		return nil, nil, err
	}

	shopIDToSBSInfo := make(map[uint64]entity.SBSShopInfo)
	itemIDToModelIDToSBSInfo := make(map[uint64]map[uint64]entity.SBSItemModelInfo)
	for _, shopInfo := range sbsShopInfos {
		shopIDToSBSInfo[shopInfo.ShopID] = shopInfo
		for _, itemModelInfo := range shopInfo.ItemModelInfos {
			if _, ok := itemIDToModelIDToSBSInfo[itemModelInfo.ItemID]; !ok {
				itemIDToModelIDToSBSInfo[itemModelInfo.ItemID] = make(map[uint64]entity.SBSItemModelInfo)
			}
			itemIDToModelIDToSBSInfo[itemModelInfo.ItemID][itemModelInfo.ModelID] = itemModelInfo
		}
	}

	return shopIDToSBSInfo, itemIDToModelIDToSBSInfo, nil
}

func (g *GroupShipmentOrderProcessor) updateShippingOrderItemIsSBS(
	shippingOrders []group_entity.ShippingOrder,
	itemIDToModelIDToSBSInfo map[uint64]map[uint64]entity.SBSItemModelInfo,
) []group_entity.ShippingOrder {
	var updatedShippingOrders []group_entity.ShippingOrder
	for _, order := range shippingOrders {
		for i, item := range order.Items {
			if len(item.PackageItems) > 0 {
				for j, packageItem := range item.PackageItems {
					if sbsInfo, exists := itemIDToModelIDToSBSInfo[packageItem.ItemID][packageItem.ModelID]; exists {
						packageItem.IsSBSItem = sbsInfo.IsSBSItem
					}
					item.PackageItems[j] = packageItem
				}
			} else {
				if sbsInfo, exists := itemIDToModelIDToSBSInfo[item.ItemID][item.ModelID]; exists {
					item.IsSBSItem = sbsInfo.IsSBSItem
				}
			}
			order.Items[i] = item
		}
		updatedShippingOrders = append(updatedShippingOrders, order)
	}
	return updatedShippingOrders
}

func (g *GroupShipmentOrderProcessor) groupShippingOrders(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
	buyerAddress entity.ItemGroupBuyerAddress,
	groupShipmentInfo entity.SBSShipmentGroup,
	shopIDToSBSInfo map[uint64]entity.SBSShopInfo,
	itemIDToModelIDToSBSInfo map[uint64]map[uint64]entity.SBSItemModelInfo,
	shopIDToStrategy map[uint64]constant.ItemOrganizeStrategy,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	// 分离可团购和不可团购的订单
	processedShippingOrders, groupingShippingOrders := g.splitGroupingShippingOrders(
		shippingOrders,
		groupShipmentInfo,
		shopIDToSBSInfo,
		itemIDToModelIDToSBSInfo,
	)

	// 按策略分组
	var minimalParcelShippingOrders, otherShippingOrders []group_entity.ShippingOrder
	for _, order := range groupingShippingOrders {
		strategy := shopIDToStrategy[order.ShopInfo().ShopID]
		if strategy == constant.ItemOrganizeStrategyMinimumParcel {
			minimalParcelShippingOrders = append(minimalParcelShippingOrders, order)
		} else {
			otherShippingOrders = append(otherShippingOrders, order)
		}
	}

	// 处理MinimalParcel策略的订单
	if len(minimalParcelShippingOrders) > 0 {
		reOrganizedOrders, err := g.groupShippingOrdersByMinimalParcel(ctx, minimalParcelShippingOrders, buyerAddress)
		if err != nil {
			return nil, err
		}
		otherShippingOrders = append(otherShippingOrders, reOrganizedOrders...)
	}

	// 处理其他策略的订单
	processedShippingOrders = append(processedShippingOrders, g.groupShippingOrdersBySource(ctx, otherShippingOrders, buyerAddress)...)

	return processedShippingOrders, nil
}

func (g *GroupShipmentOrderProcessor) splitGroupingShippingOrders(
	shippingOrders []group_entity.ShippingOrder,
	groupShipmentInfo entity.SBSShipmentGroup,
	shopIDToSBSInfo map[uint64]entity.SBSShopInfo,
	itemIDToModelIDToSBSInfo map[uint64]map[uint64]entity.SBSItemModelInfo,
) ([]group_entity.ShippingOrder, []group_entity.ShippingOrder) {
	var processedShippingOrders, groupingShippingOrders []group_entity.ShippingOrder
	for _, order := range shippingOrders {
		// 设置团购信息
		if order.IsServiceByShopee() {
			order.GroupShipmentInfo = groupShipmentInfo
		}

		shopSBSInfo := shopIDToSBSInfo[order.ShopInfo().ShopID]
		if !shopSBSInfo.IsSBSShop || groupShipmentInfo.NotGrouping {
			processedShippingOrders = append(processedShippingOrders, order)
			continue
		}

		// 检查Cache订单的特殊情况
		if order.IsCacheOrder() && len(order.Items) > 0 && order.Items[0].ShopeeStocks().TotalStock() == 0 {
			processedShippingOrders = append(processedShippingOrders, order)
			continue
		}

		// 分离支持团购和不支持团购的商品
		var groupingItems, nonGroupItems []group_entity.ShippingOrderItem
		for _, item := range order.Items {
			if g.isItemAbleToGroup(item, itemIDToModelIDToSBSInfo) {
				groupingItems = append(groupingItems, item)
			} else {
				nonGroupItems = append(nonGroupItems, item)
			}
		}

		// 处理不支持团购的商品
		if len(nonGroupItems) > 0 {
			newShippingOrder := order.SplitNew(nonGroupItems)
			processedShippingOrders = append(processedShippingOrders, newShippingOrder)
		}

		// 处理支持团购的商品
		if len(groupingItems) > 0 {
			order.Items = groupingItems
			groupingShippingOrders = append(groupingShippingOrders, order)
		}
	}
	return processedShippingOrders, groupingShippingOrders
}

func (g *GroupShipmentOrderProcessor) groupShippingOrdersByMinimalParcel(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
	buyerAddress entity.ItemGroupBuyerAddress,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	if len(shippingOrders) == 0 {
		return nil, nil
	}

	// 分离Cache订单和普通订单
	var allItems []group_entity.ShippingOrderItem
	var cacheOrders []group_entity.ShippingOrder
	firstNonCacheOrderIdx := -1
	for idx, order := range shippingOrders {
		if order.IsCacheOrder() {
			cacheOrders = append(cacheOrders, order)
		} else {
			allItems = append(allItems, order.Items...)
			if firstNonCacheOrderIdx < 0 {
				firstNonCacheOrderIdx = idx
			}
		}
	}
	if firstNonCacheOrderIdx < 0 {
		return shippingOrders, nil
	}

	// 组织商品到不同仓库
	keyToItems, err := g.itemOrganizer.OrganizeItems(
		ctx,
		allItems,
		g.sourceSelectFunc(),
		g.sourcePrioritizeFunc(ctx, buyerAddress),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	// 尝试合并Cache订单
	var notMergedCacheOrders []group_entity.ShippingOrder
	for _, cacheOrder := range cacheOrders {
		merged := false
		if len(cacheOrder.Items) > 0 {
			item := cacheOrder.Items[0]
			for _, stock := range item.ShopeeStocks() {
				if stock.AvailableStock < item.Quantity {
					continue
				}
				key := organizer.OrganizeItemKey{
					FulfilmentSource: stock.Source,
					FulfilmentType:   stock.FulfilmentType,
				}
				if groupedItems, ok := keyToItems[key]; ok && len(groupedItems) > 0 {
					keyToItems[key] = append(keyToItems[key], item)
					merged = true
					break
				}
			}
		}
		if !merged {
			notMergedCacheOrders = append(notMergedCacheOrders, cacheOrder)
		}
	}

	// 获取仓库地址信息
	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		address, err := g.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
		if err != nil {
			return nil, err
		}
		sourceToAddressID[key.FulfilmentSource] = address.AddressID
	}

	// 创建分组后的订单
	processedShippingOrders := g.splitShippingOrderByOrganizedItems(shippingOrders[firstNonCacheOrderIdx], keyToItems, sourceToAddressID)
	processedShippingOrders = append(processedShippingOrders, notMergedCacheOrders...)

	return processedShippingOrders, nil
}

func (g *GroupShipmentOrderProcessor) groupShippingOrdersBySource(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
	buyerAddress entity.ItemGroupBuyerAddress,
) []group_entity.ShippingOrder {
	// 按源分组订单
	shopeeWHSourceToShippingOrders := make(map[string][]group_entity.ShippingOrder)
	var ordersWithOnlyCacheSource, ordersWithCacheAndShopeeWHSource []group_entity.ShippingOrder

	for _, order := range shippingOrders {
		if order.IsCacheOrder() {
			if len(order.Items) > 0 && order.Items[0].ShopeeStocks().TotalStock() >= order.Items[0].Quantity {
				ordersWithCacheAndShopeeWHSource = append(ordersWithCacheAndShopeeWHSource, order)
			} else {
				ordersWithOnlyCacheSource = append(ordersWithOnlyCacheSource, order)
			}
		} else {
			shopeeWHSourceToShippingOrders[order.FulfilmentInfo.Source] = append(shopeeWHSourceToShippingOrders[order.FulfilmentInfo.Source], order)
		}
	}

	var groupedShippingOrders []group_entity.ShippingOrder
	groupedShippingOrders = append(groupedShippingOrders, ordersWithOnlyCacheSource...)

	// 处理Cache和Shopee仓库混合的订单
	processedShippingOrders, shopeeWHSourceToNeedMergeItems := g.groupShippingOrdersWithCacheAndShopeeWHSource(
		ctx, ordersWithCacheAndShopeeWHSource, shopeeWHSourceToShippingOrders, buyerAddress)
	groupedShippingOrders = append(groupedShippingOrders, processedShippingOrders...)

	// 合并同源的订单
	for shopeeWHSource, orders := range shopeeWHSourceToShippingOrders {
		if len(orders) == 0 {
			continue
		}

		var items []group_entity.ShippingOrderItem
		for _, order := range orders {
			items = append(items, order.Items...)
		}

		if needMergeItems, ok := shopeeWHSourceToNeedMergeItems[shopeeWHSource]; ok {
			items = append(items, needMergeItems...)
		}

		groupedShippingOrders = append(groupedShippingOrders, orders[0].SplitNew(items))
	}

	return groupedShippingOrders
}

func (g *GroupShipmentOrderProcessor) groupShippingOrdersWithCacheAndShopeeWHSource(
	ctx context.Context,
	ordersWithCacheAndShopeeWHSource []group_entity.ShippingOrder,
	shopeeWHSourceToShippingOrders map[string][]group_entity.ShippingOrder,
	buyerAddress entity.ItemGroupBuyerAddress,
) (processedShippingOrders []group_entity.ShippingOrder, shopeeWHSourceToNeedMergeItems map[string][]group_entity.ShippingOrderItem) {
	processedShippingOrders = make([]group_entity.ShippingOrder, 0)
	shopeeWHSourceToNeedMergeItems = make(map[string][]group_entity.ShippingOrderItem)
	if len(ordersWithCacheAndShopeeWHSource) == 0 {
		return processedShippingOrders, shopeeWHSourceToNeedMergeItems
	}

	// 收集所有Cache和Shopee仓库的商品
	var allCacheAndShopeeWHItems []group_entity.ShippingOrderItem
	for _, order := range ordersWithCacheAndShopeeWHSource {
		allCacheAndShopeeWHItems = append(allCacheAndShopeeWHItems, order.Items...)
	}

	// 使用WarehousePriority策略组织商品
	shopeeWHKeyToItems, err := g.itemOrganizer.OrganizeItems(
		ctx, allCacheAndShopeeWHItems, g.sourceSelectFunc(), g.sourcePrioritizeFunc(ctx, buyerAddress),
		constant.ItemOrganizeStrategyWarehousePriority)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GroupShipmentOrderProcessor|fail organize items, err: %v", err)
		// fallback to use cache stock
		return ordersWithCacheAndShopeeWHSource, shopeeWHSourceToNeedMergeItems
	}

	shopeeWHKeyToMultipleItems := make(map[organizer.OrganizeItemKey][]group_entity.ShippingOrderItem)
	useCacheStockItemIdSet := collection.NewSet[uint64]()
	for key, items := range shopeeWHKeyToItems {
		if groupedOrders, ok := shopeeWHSourceToShippingOrders[key.FulfilmentSource]; ok && len(groupedOrders) > 0 {
			// merge with existing group
			shopeeWHSourceToNeedMergeItems[key.FulfilmentSource] = items
		} else if len(items) > 1 {
			// create new group for multiple items
			shopeeWHKeyToMultipleItems[key] = items
		} else if len(items) == 1 {
			// keep using cache stock for single item
			useCacheStockItemIdSet.Add(items[0].ItemID)
		}
		// len(items) == 0, expect not happen
	}

	// 处理多商品组
	if len(shopeeWHKeyToMultipleItems) > 0 {
		sourceToAddressID := make(map[string]uint64)
		for key, items := range shopeeWHKeyToMultipleItems {
			address, err := g.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err == nil {
				sourceToAddressID[key.FulfilmentSource] = address.AddressID
				continue
			}

			// fallback to use cache stock
			logger.CtxLogErrorf(ctx, "GroupShipmentOrderProcessor|fail get warehouse address: %s, err: %v", key.FulfilmentSource, err)
			for _, item := range items {
				useCacheStockItemIdSet.Add(item.ItemID)
			}
			delete(shopeeWHKeyToMultipleItems, key)
		}

		if len(shopeeWHKeyToMultipleItems) > 0 {
			newShippingOrder := ordersWithCacheAndShopeeWHSource[0].SplitNew(nil)
			processedShippingOrders = append(processedShippingOrders, g.splitShippingOrderByOrganizedItems(newShippingOrder, shopeeWHKeyToMultipleItems, sourceToAddressID)...)
		}
	}

	// 处理使用Cache库存的单商品
	if useCacheStockItemIdSet.Size() > 0 {
		for _, order := range ordersWithCacheAndShopeeWHSource {
			if len(order.Items) > 0 && useCacheStockItemIdSet.Contains(order.Items[0].ItemID) {
				processedShippingOrders = append(processedShippingOrders, order)
			}
		}
	}

	return processedShippingOrders, shopeeWHSourceToNeedMergeItems
}

func (g *GroupShipmentOrderProcessor) splitShippingOrderByOrganizedItems(
	baseOrder group_entity.ShippingOrder,
	keyToItems map[organizer.OrganizeItemKey][]group_entity.ShippingOrderItem,
	sourceToAddressID map[string]uint64,
) []group_entity.ShippingOrder {
	return organizer.SplitShippingOrderByOrganizedItems(baseOrder, keyToItems, sourceToAddressID)
}

func (g *GroupShipmentOrderProcessor) isItemAbleToGroup(
	item group_entity.ShippingOrderItem,
	itemIDToModelIDToSBSInfo map[uint64]map[uint64]entity.SBSItemModelInfo,
) bool {
	if len(item.PackageItems) > 0 {
		// 如果任何一个子商品不支持团购，那么整个包装商品都不支持团购
		for _, packageItem := range item.PackageItems {
			if sbsInfo, exists := itemIDToModelIDToSBSInfo[packageItem.ItemID][packageItem.ModelID]; !exists || !sbsInfo.GroupShipment {
				return false
			}
		}
		return true
	}

	sbsInfo, exists := itemIDToModelIDToSBSInfo[item.ItemID][item.ModelID]
	return exists && sbsInfo.GroupShipment
}

func (g *GroupShipmentOrderProcessor) sourceSelectFunc() organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
		return item.ShopeeStocks(), nil
	}
}

func (g *GroupShipmentOrderProcessor) sourcePrioritizeFunc(
	ctx context.Context,
	buyerAddress entity.ItemGroupBuyerAddress,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		return g.warehousePriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddress, sources)
	}
}
