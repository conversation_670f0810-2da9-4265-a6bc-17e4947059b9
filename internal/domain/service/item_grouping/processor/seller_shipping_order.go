package processor

import (
	"context"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type SellerShippingOrderProcessor struct {
	confAccessor             config.ConfAccessor
	itemOrganizer            organizer.ItemOrganizer
	shopService              shop.ShopService
	shopLocalSIPService      shop_local_sip.ShopLocalSIPService
	addrService              address.AddrService
	warehousePriorityService warehouse_priority.WarehousePriorityService
}

func NewSellerShippingOrderProcessor(
	confAccessor config.ConfAccessor,
	itemOrganizer organizer.ItemOrganizer,
	shopService shop.ShopService,
	shopLocalService shop_local_sip.ShopLocalSIPService,
	addrService address.AddrService,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
) *SellerShippingOrderProcessor {
	return &SellerShippingOrderProcessor{
		confAccessor:             confAccessor,
		itemOrganizer:            itemOrganizer,
		shopService:              shopService,
		shopLocalSIPService:      shopLocalService,
		addrService:              addrService,
		warehousePriorityService: warehousePriorityService,
	}
}

func (s *SellerShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return SellerShippingOrderProcessorName
}

func (s *SellerShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		(shippingOrder.ShopInfo().SellerStockFulfilmentCapability || shippingOrder.IsDecoupled)
}

func (s *SellerShippingOrderProcessor) DoProcess(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	var processedOrders []group_entity.ShippingOrder
	for _, order := range shippingOrders {
		splitOrders, err := s.splitShippingOrder(
			ctx,
			buyerInfo,
			order,
		)
		if err != nil {
			return nil, err
		}
		processedOrders = append(processedOrders, splitOrders...)
	}

	return processedOrders, nil
}

func (s *SellerShippingOrderProcessor) splitShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	keyToItems, err := s.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		s.sourceSelectFunc(),
		s.sourcePrioritizeFunc(
			ctx,
			buyerInfo,
			shippingOrder.ShopInfo(),
		),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	splitOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
	s.generateMetrics(ctx, shippingOrder.IsDecoupled, shippingOrder.ShopInfo().SellerWarehouseFlag.IsMultiWarehouse(), len(splitOrders))
	return splitOrders, nil
}

func (s *SellerShippingOrderProcessor) sourceSelectFunc() organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
		if len(item.SellerStocks()) > 1 && !item.GetShopInfo().SellerWarehouseFlag.IsMultiWarehouse() {
			return item.SellerStocks()[:1], nil
		}
		return item.SellerStocks(), nil
	}
}

func (s *SellerShippingOrderProcessor) sourcePrioritizeFunc(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopInfo group_entity.ShopInfo,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		if len(sources) <= 1 {
			return sources, nil
		}
		buyerAddressGeo, err := s.getBuyerGeoLocation(ctx, buyerInfo, shopInfo.ShopID)
		if err != nil {
			return nil, err
		}
		if shopInfo.SellerWarehouseFlag.IsMultiWarehouse() {
			return s.warehousePriorityService.PrioritizeSellerMultiWarehouse(
				ctx,
				buyerAddressGeo,
				shopInfo.SellerID,
				shopInfo.Warehouses,
				sources,
			), nil
		}
		sort.SliceStable(sources, func(i, j int) bool {
			return sources[i] < sources[j]
		})
		return sources, nil
	}
}

func (s *SellerShippingOrderProcessor) getShopStrategy(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
) map[uint64]constant.ItemOrganizeStrategy {
	shopIDSet := collection.NewSet[uint64]()
	for _, order := range shippingOrders {
		shopIDSet.Add(order.ShopInfo().ShopID)
	}
	return s.warehousePriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDSet.ToSlice(),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
}

// only need the buyer address Geo for multi-warehouse shop
// for SIP affiliated shop, we need the dummy buyer Geo instead of real buyer Geo
func (s *SellerShippingOrderProcessor) getBuyerGeoLocation(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopID uint64,
) (entity.GeoLocation, fsserr.Error) {
	localSIPInfo, err := s.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get local SIP shop info error: %v", err)
	}
	var buyerAddressGeo entity.GeoLocation

	if localSIPInfo.IsSIPAffiliated {
		buyerAddressGeo, err = s.addrService.GetDummyBuyerGeoLocation(ctx, localSIPInfo.DummyBuyerID, localSIPInfo.SIPPrimaryRegion)
	} else {
		buyerAddressGeo, err = s.addrService.GetBuyerGeoLocation(ctx, buyerInfo.UserID, buyerInfo.Address.BuyerAddressID)
	}
	if err != nil {
		return entity.GeoLocation{}, err
	}

	return buyerAddressGeo, nil
}

func (s *SellerShippingOrderProcessor) generateMetrics(ctx context.Context, isDecoupled bool, isMultiWarehouse bool, count int) {
	if isDecoupled {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypeSellerDecoupled, count)
	} else if isMultiWarehouse {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypeSellerMultiWarehouse, count)
	} else {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypeSellerNormal, count)
	}
}
