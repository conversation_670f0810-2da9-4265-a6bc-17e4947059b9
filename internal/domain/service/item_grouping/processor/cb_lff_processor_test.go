package processor

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
)

func TestCBLFFShippingOrderProcessor_Name(t *testing.T) {
	processor := &CBLFFShippingOrderProcessor{}
	expected := CBLFFShippingOrderProcessorName
	result := processor.Name()
	assert.Equal(t, expected, result)
}

func TestCBLFFShippingOrderProcessor_IsAbleToProcess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	configAccessor := config.NewMockConfAccessor(ctrl)
	mockWarehousePriorityService := warehouse_priority.NewMockWarehousePriorityService(ctrl)
	mockAddressService := address.NewMockAddrService(ctrl)

	processor := NewCBLFFShippingOrderProcessor(
		configAccessor,
		nil, // itemOrganizer - can be nil for IsAbleToProcess test
		mockWarehousePriorityService,
		mockAddressService,
		nil, // shopLocalSIPService - can be nil for IsAbleToProcess test
	)

	buyerInfo := group_entity.BuyerInfo{
		UserID: 1,
		Address: entity.ItemGroupBuyerAddress{
			District: "SG",
		},
	}

	tests := []struct {
		name           string
		shippingOrder  group_entity.ShippingOrder
		lffShopFlags   []uint64
		expectedResult bool
	}{
		{
			name: "should process when not source selected and in LFF shop flags",
			shippingOrder: group_entity.ShippingOrder{
				FulfilmentInfo: group_entity.OrderFulfilmentInfo{
					Source: "",
					Type:   0, // Not selected
				},
				Items: []group_entity.ShippingOrderItem{
					{
						ShopInfo: group_entity.ShopInfo{
							ShopID:   100,
							ShopFlag: uint64(1), // Mock shop flag
						},
					},
				},
			},
			lffShopFlags:   []uint64{1}, // Include shop flag 1
			expectedResult: true,
		},
		{
			name: "should not process when source already selected",
			shippingOrder: group_entity.ShippingOrder{
				FulfilmentInfo: group_entity.OrderFulfilmentInfo{
					Source: "SGXS1_WH_SG",
					Type:   constant.FulfilmentTypeShopee,
				},
				Items: []group_entity.ShippingOrderItem{
					{
						ShopInfo: group_entity.ShopInfo{
							ShopID:   100,
							ShopFlag: uint64(1),
						},
					},
				},
			},
			lffShopFlags:   []uint64{1},
			expectedResult: false,
		},
		{
			name: "should not process when from multiple shops",
			shippingOrder: group_entity.ShippingOrder{
				FulfilmentInfo: group_entity.OrderFulfilmentInfo{
					Source: "",
					Type:   0,
				},
				Items: []group_entity.ShippingOrderItem{
					{
						ShopInfo: group_entity.ShopInfo{
							ShopID:   100,
							ShopFlag: uint64(1),
						},
					},
					{
						ShopInfo: group_entity.ShopInfo{
							ShopID:   101, // Different shop ID
							ShopFlag: uint64(1),
						},
					},
				},
			},
			lffShopFlags:   []uint64{1},
			expectedResult: false,
		},
		{
			name: "should not process when shop flag not in LFF shop flags",
			shippingOrder: group_entity.ShippingOrder{
				FulfilmentInfo: group_entity.OrderFulfilmentInfo{
					Source: "",
					Type:   0,
				},
				Items: []group_entity.ShippingOrderItem{
					{
						ShopInfo: group_entity.ShopInfo{
							ShopID:   100,
							ShopFlag: uint64(2), // Different shop flag
						},
					},
				},
			},
			lffShopFlags:   []uint64{1}, // Does not include 2
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configAccessor.EXPECT().GetIGSLFFShopFlags(gomock.Any()).Return(tt.lffShopFlags).AnyTimes()

			result := processor.IsAbleToProcess(context.Background(), buyerInfo, tt.shippingOrder)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestCBLFFShippingOrderProcessor_DoProcess_EmptyOrders(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	configAccessor := config.NewMockConfAccessor(ctrl)
	mockWarehousePriorityService := warehouse_priority.NewMockWarehousePriorityService(ctrl)
	mockAddressService := address.NewMockAddrService(ctrl)

	processor := NewCBLFFShippingOrderProcessor(
		configAccessor,
		nil, // itemOrganizer
		mockWarehousePriorityService,
		mockAddressService,
		nil, // shopLocalSIPService
	)

	buyerInfo := group_entity.BuyerInfo{
		UserID: 1,
	}

	// Test with empty orders
	result, err := processor.DoProcess(context.Background(), buyerInfo, []group_entity.ShippingOrder{})

	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}

func TestCBLFFShippingOrderProcessor_DoProcess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	configAccessor := config.NewMockConfAccessor(ctrl)
	mockWarehousePriorityService := warehouse_priority.NewMockWarehousePriorityService(ctrl)
	mockAddressService := address.NewMockAddrService(ctrl)

	processor := NewCBLFFShippingOrderProcessor(
		configAccessor,
		nil, // itemOrganizer
		mockWarehousePriorityService,
		mockAddressService,
		nil, // shopLocalSIPService
	)

	buyerInfo := group_entity.BuyerInfo{
		UserID: 1,
		Address: entity.ItemGroupBuyerAddress{
			District: "SG",
		},
	}

	t.Run("should return empty result when input is empty", func(t *testing.T) {
		result, err := processor.DoProcess(context.Background(), buyerInfo, []group_entity.ShippingOrder{})
		assert.NoError(t, err)
		assert.Empty(t, result)
	})

	// 注意：由于CBLFFShippingOrderProcessor的DoProcess方法依赖多个外部服务（itemOrganizer等），
	// 完整的DoProcess测试需要更复杂的mock设置。目前只测试空输入的情况以确保基本功能正常。
	// 在实际项目中，建议使用完整的依赖注入和mock来测试复杂的业务逻辑。
}
