package processor

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ShopeeShippingOrderProcessor struct {
	configAccessor           config.ConfAccessor
	itemOrganizer            organizer.ItemOrganizer
	itemTagService           item_tag.ItemTagService
	warehousePriorityService warehouse_priority.WarehousePriorityService
	addressService           address.AddrService
}

func NewShopeeShippingOrderProcessor(configAccessor config.ConfAccessor,
	itemOrganizer organizer.ItemOrganizer,
	itemTagService item_tag.ItemTagService,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	addressService address.AddrService,
) *ShopeeShippingOrderProcessor {
	return &ShopeeShippingOrderProcessor{
		configAccessor:           configAccessor,
		itemOrganizer:            itemOrganizer,
		itemTagService:           itemTagService,
		warehousePriorityService: warehousePriorityService,
		addressService:           addressService,
	}
}

func (s *ShopeeShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return ShopeeShippingOrderProcessorName
}

func (s *ShopeeShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		shippingOrder.ShopInfo().ShopeeWarehouseFulfilmentCapability
}

func (s *ShopeeShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, fsserr.Error) {

	shopeeOrders, localSellerOrders, err := s.separateDecoupleOrders(ctx, shippingOrders)
	if err != nil {
		return nil, err
	}

	shopIDSet := collection.NewSet[uint64]()
	for _, order := range shopeeOrders {
		shopIDSet.Add(order.ShopInfo().ShopID)
	}
	shopIDToStrategy := s.warehousePriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDSet.ToSlice(),
		constant.ItemOrganizeStrategyMinimumParcel,
	)

	var processedOrders []group_entity.ShippingOrder
	for _, order := range shopeeOrders {
		splitOrders, err := s.splitSingleShippingOrder(ctx, buyerInfo, order, shopIDToStrategy[order.ShopInfo().ShopID])
		if err != nil {
			return nil, err
		}

		processedOrders = append(processedOrders, splitOrders...)
	}
	processedOrders = append(processedOrders, localSellerOrders...)

	return processedOrders, nil
}

func (s *ShopeeShippingOrderProcessor) separateDecoupleOrders(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, []group_entity.ShippingOrder, fsserr.Error) {

	decoupleConfig := s.configAccessor.GetIGSDynamicConfig(ctx).TransferDecoupleShopLogic
	if !decoupleConfig.Enable {
		return shippingOrders, nil, nil
	}
	if decoupleConfig.TagId == 0 {
		logger.CtxLogErrorf(ctx, "tag id not found in config")
		return nil, nil, fsserr.New(fsserr.DataErr, "tag id not found in config")
	}

	modelIDSet := collection.NewSet[uint64]()
	for _, order := range shippingOrders {
		order.ForEachAllItems(func(item group_entity.ShippingOrderItem) {
			modelIDSet.Add(item.ModelID)
		})
	}

	modelIDToLabelIDs, err := s.itemTagService.BatchGetItemModelLabelIDs(ctx, modelIDSet.ToSlice())
	if err != nil {
		return nil, nil, err
	}

	modelIDToIsDecoupleItem := make(map[uint64]bool)
	for modelID, labelIDs := range modelIDToLabelIDs {
		for _, labelID := range labelIDs {
			if labelID == decoupleConfig.TagId {
				modelIDToIsDecoupleItem[modelID] = true
				break
			}
		}
	}

	var shopeeOrders, decoupleOrders []group_entity.ShippingOrder
	for _, order := range shippingOrders {
		var normalItems, localSellerItems []group_entity.ShippingOrderItem
		for _, item := range order.Items {
			if s.isDecoupleItem(item, modelIDToIsDecoupleItem) {
				localSellerItems = append(localSellerItems, item)
			} else {
				normalItems = append(normalItems, item)
			}
		}

		if len(normalItems) > 0 {
			shopeeOrders = append(shopeeOrders, order.SplitNew(normalItems))
		}
		if len(localSellerItems) > 0 {
			decoupleOrder := order.SplitNew(localSellerItems)
			decoupleOrder.IsDecoupled = true
			decoupleOrders = append(decoupleOrders, decoupleOrder)
		}
	}

	return shopeeOrders, decoupleOrders, nil
}

func (s *ShopeeShippingOrderProcessor) isDecoupleItem(item group_entity.ShippingOrderItem, modelIDToIsDecoupleItem map[uint64]bool) bool {
	if len(item.PackageItems) == 0 {
		return modelIDToIsDecoupleItem[item.ModelID]
	}
	for _, packageItem := range item.PackageItems {
		if modelIDToIsDecoupleItem[packageItem.ModelID] {
			return true
		}
	}
	return false
}

func (s *ShopeeShippingOrderProcessor) splitSingleShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	keyToItems, err := s.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		s.sourceSelectFunc(),
		s.sourcePrioritizeFunc(ctx, buyerInfo.Address),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		addr, err := s.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
		if err != nil {
			return nil, err
		}
		sourceToAddressID[key.FulfilmentSource] = addr.AddressID
	}

	splitOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
	metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypeShopeeNormal, len(splitOrders))
	return splitOrders, nil
}

func (s *ShopeeShippingOrderProcessor) sourceSelectFunc() organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, fsserr.Error) {
		return item.ShopeeStocks(), nil
	}
}

func (s *ShopeeShippingOrderProcessor) sourcePrioritizeFunc(ctx context.Context, buyerAddress entity.ItemGroupBuyerAddress) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, fsserr.Error) {
		prioritizedWarehouseList, err := s.warehousePriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddress, sources)
		if err != nil {
			return nil, err
		}
		return prioritizedWarehouseList, nil
	}
}
