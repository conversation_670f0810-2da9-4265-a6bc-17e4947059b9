package group_entity

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// BuildOOSItemStockInfos builds out of stock item stock infos
func BuildOOSItemStockInfos(item ShippingOrderItem, totalStock uint32) []fsserr.ItemStockInfo {
	var stockInfos []fsserr.ItemStockInfo
	if len(item.PackageItems) > 0 {
		for _, packageItem := range item.PackageItems {
			stockInfos = append(stockInfos, fsserr.ItemStockInfo{
				ItemId:         packageItem.ItemID,
				ModelId:        packageItem.ModelID,
				AvailableStock: totalStock,
			})
		}
		return stockInfos
	}

	stockInfos = append(stockInfos, fsserr.ItemStockInfo{
		ItemId:         item.ItemID,
		ModelId:        item.ModelID,
		AvailableStock: totalStock,
	})
	return stockInfos
}

func GetShippingOrderFulfilmentSource(shippingOrder ShippingOrder, fulfilmentInfo OrderFulfilmentInfo) string {
	if shippingOrder.IsGroupShipment ||
		fulfilmentInfo.Type == constant.FulfilmentTypeShopee || fulfilmentInfo.Type == constant.FulfilmentTypeCacheWarehouse ||
		shippingOrder.ShopInfo().SellerWarehouseFlag.IsMultiWarehouse() ||
		shippingOrder.ShopInfo().SellerWarehouseFlag.Is3PFWhitelist() {
		return fulfilmentInfo.Source
	}

	return ""
}

func GetShippingOrderShopIDToItemIDs(shippingOrder ShippingOrder) map[uint64][]typ.ItemIdType {
	shopIDToItemIDs := make(map[uint64][]typ.ItemIdType)
	shippingOrder.ForEachAllItems(func(item ShippingOrderItem) {
		shopIDToItemIDs[item.GetShopInfo().ShopID] = append(shopIDToItemIDs[item.GetShopInfo().ShopID], item.ItemID)
	})

	return shopIDToItemIDs
}
