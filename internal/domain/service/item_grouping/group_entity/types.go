package group_entity

import (
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
)

type BuyerInfo struct {
	UserID  typ.UserIdType
	Address entity.ItemGroupBuyerAddress
}

type ShippingOrder struct {
	RequireAdvanceBooking bool
	Items                 []ShippingOrderItem

	// These below fields are initialized empty, and should be filled by the processor
	FulfilmentInfo              OrderFulfilmentInfo
	CacheFallbackFulfilmentInfo typ.Optional[OrderFulfilmentInfo]
	IsDecoupled                 bool
	IsGroupShipment             bool
	GroupShipmentInfo           entity.SBSShipmentGroup

	// todo 需要添加
	IsChoiceEligible       bool
	IsTradeInEligible      bool
	EnabledChoiceChannels  []int
	EnabledTradeInChannels []int
}

// SplitNew create a new ShippingOrder with the given items, and the other fields are copied from the original order
func (o ShippingOrder) SplitNew(items []ShippingOrderItem) ShippingOrder {
	newOrder := o
	newOrder.Items = items
	return newOrder
}

func (o ShippingOrder) ForEachAllItems(fn func(item ShippingOrderItem)) {
	for _, item := range o.Items {
		if len(item.PackageItems) == 0 {
			fn(item)
		} else {
			for _, packageItem := range item.PackageItems {
				fn(packageItem)
			}
		}
	}
}

// IsSourceSelected return true if the shipping order already has a fulfilment source and type
func (o ShippingOrder) IsSourceSelected() bool {
	return o.FulfilmentInfo.Type != 0
}

func (o ShippingOrder) GetSourceLocation() string {
	if o.FulfilmentInfo.Type == 0 || len(o.FulfilmentInfo.Source) < 2 {
		return ""
	}
	return strings.ToUpper(o.FulfilmentInfo.Source[:2])
}

// FromMultipleShop Return true if the order has items from multiple shops
func (o ShippingOrder) FromMultipleShop() bool {
	if len(o.Items) == 0 {
		return false
	}
	shopID := o.Items[0].GetShopInfo().ShopID
	for _, item := range o.Items[1:] {
		if item.IsPackageAndFromMultipleShop() {
			return true
		}
		if item.GetShopInfo().ShopID != shopID {
			return true
		}
	}
	return false
}

// ShopInfo return the ShopInfo of the first item in the order
func (o ShippingOrder) ShopInfo() ShopInfo {
	if len(o.Items) == 0 {
		return ShopInfo{}
	}
	return o.Items[0].GetShopInfo()
}

func (o ShippingOrder) IsServiceByShopee() bool {
	for _, item := range o.Items {
		if len(item.PackageItems) > 0 {
			for _, packageItem := range item.PackageItems {
				if packageItem.IsSBSItem {
					return true
				}
			}
			continue
		}
		if item.IsSBSItem {
			return true
		}
	}
	return false
}

// IsManageBySBS reference: https://confluence.shopee.io/pages/viewpage.action?pageId=144393904
func (o ShippingOrder) IsManageBySBS() bool {
	if o.IsGroupShipment || o.FulfilmentInfo.Type == constant.FulfilmentTypeShopee || o.FulfilmentInfo.Type == constant.FulfilmentTypeCacheWarehouse {
		for _, item := range o.Items {
			if item.GetShopInfo().IsPFFWhitelist() {
				return true
			}
		}
	}
	return false
}

func (o ShippingOrder) IsCacheOrder() bool {
	return o.FulfilmentInfo.Type.IsCacheStockFulfilmentType()
}

// https://confluence.shopee.io/x/pJxEiQ
const (
	ShopFlagSIPPrimary = uint64(1) << iota
	ShopFlagSIPAffiliated
	ShopFlagSBS
	ShopFlagLocalRetailCB
	ShopFlagRetail
	ShopFlagFBS
	ShopFlagCB
	ShopFlagChoices
	ShopFlagSellerMWH
	ShopFlag3PF
)

var ShopTagToFlag = map[string]uint64{
	constant.ShopTagSBS:           ShopFlagSBS,
	constant.ShopTagFBS:           ShopFlagFBS,
	constant.ShopTagLocalRetailCB: ShopFlagLocalRetailCB,
	constant.ShopTagLocalRetail:   ShopFlagRetail,
}

type ShopInfo struct {
	ShopID   uint64
	SellerID typ.UserIdType
	// ShopFlag is a bitset of ShopFlag* constants
	ShopFlag                            uint64
	PFFCheckoutImprovementWhitelist     bool
	SellerWarehouseFlag                 entity.ShopWarehouseFlag
	Warehouses                          []entity.ShopWarehouse
	ShopeeWarehouseFulfilmentCapability bool
	SellerStockFulfilmentCapability     bool
	AdvanceBookingFulfilmentCapability  bool
	PFFAllocateBySalesOrdersGroupTag    string
	IsPreSaleVoucherSeller              bool
}

func (s ShopInfo) IsShipFromOversea() bool {
	return s.ShopFlag&ShopFlagCB != 0
}

func (s ShopInfo) IsPFFWhitelist() bool {
	return s.ShopFlag&ShopFlagFBS != 0
}

type ShippingOrderItem struct {
	QueryId                   string
	ShopInfo                  ShopInfo
	ItemID                    typ.ItemIdType
	ModelID                   typ.ModelIdType
	Quantity                  uint32
	ItemType                  uint32
	FulfilmentTypeToLocations map[constant.FulfilmentType]StockLocations
	IsSBSItem                 bool
	BundleDealID              uint64
	ItemGroupID               uint64
	AddOnDealID               uint64
	// The items with SingleSource can not be split to multiple orders
	SingleSource bool
	IsBOMItem    bool
	PackageItems []ShippingOrderItem
}

func (s ShippingOrderItem) GetShopInfo() ShopInfo {
	if len(s.PackageItems) > 0 {
		return s.PackageItems[0].ShopInfo
	}
	return s.ShopInfo
}

func (s ShippingOrderItem) IsPackageItem() bool {
	return len(s.PackageItems) > 0
}

// IsPackageAndFromMultipleShop Return true if it's package item and the items from multiple shops
func (s ShippingOrderItem) IsPackageAndFromMultipleShop() bool {
	if !s.IsPackageItem() {
		return false
	}
	shopID := s.PackageItems[0].GetShopInfo().ShopID
	for _, item := range s.PackageItems[1:] {
		if item.GetShopInfo().ShopID != shopID {
			return true
		}
	}
	return false
}

func (s ShippingOrderItem) GetStockLocations(fulfilmentTypes ...constant.FulfilmentType) StockLocations {
	var stockLocations StockLocations
	for _, fulfilmentType := range fulfilmentTypes {
		stockLocations = append(stockLocations, s.FulfilmentTypeToLocations[fulfilmentType]...)
	}
	return stockLocations
}

func (s ShippingOrderItem) ShopeeStocks() StockLocations {
	return s.GetStockLocations(constant.FulfilmentTypeShopee)
}

func (s ShippingOrderItem) SellerStocks() StockLocations {
	return s.GetStockLocations(constant.FulfilmentTypeSeller)
}

func (s ShippingOrderItem) AdvanceBookingStocks() StockLocations {
	return s.GetStockLocations(constant.FulfilmentTypeCacheSeller)
}

func (s ShippingOrderItem) CacheWarehouseStocks() StockLocations {
	return s.GetStockLocations(constant.FulfilmentTypeCacheWarehouse)
}

func (s ShippingOrderItem) TotalStock() uint32 {
	var stock uint32
	for _, location := range s.FulfilmentTypeToLocations {
		stock += location.TotalStock()
	}
	return stock
}

func (s ShippingOrderItem) Less(t ShippingOrderItem) bool {
	if s.QueryId != t.QueryId {
		return s.QueryId < t.QueryId
	}
	if s.ItemID != t.ItemID {
		return s.ItemID < t.ItemID
	}
	if s.ModelID != t.ModelID {
		return s.ModelID < t.ModelID
	}
	if s.Quantity != t.Quantity {
		return s.Quantity < t.Quantity
	}
	if s.BundleDealID != t.BundleDealID {
		return s.BundleDealID < t.BundleDealID
	}
	if s.AddOnDealID != t.AddOnDealID {
		return s.AddOnDealID < t.AddOnDealID
	}
	if s.ItemGroupID != t.ItemGroupID {
		return s.ItemGroupID < t.ItemGroupID
	}
	if len(s.PackageItems) > 0 && len(t.PackageItems) > 0 {
		return s.PackageItems[0].Less(t.PackageItems[0])
	}

	return len(t.PackageItems) > 0
}

type ItemStockLocation struct {
	Source          string                  `json:"source"`
	FulfilmentType  constant.FulfilmentType `json:"fulfilment_type"`
	AvailableStock  uint32                  `json:"available_stock"`
	EnabledChannels []int                   `json:"enabled_channels"`
}

func (i ItemStockLocation) Region() string {
	if len(i.Source) < 2 {
		return ""
	}
	return strings.ToUpper(i.Source[:2])
}

type StockLocations []ItemStockLocation

func (s StockLocations) TotalStock() uint32 {
	var stock uint32
	for _, location := range s {
		stock += location.AvailableStock
	}
	return stock
}

func (s StockLocations) GetLocation(source string) ItemStockLocation {
	for _, location := range s {
		if location.Source == source {
			return location
		}
	}
	return ItemStockLocation{}
}

type ShippingOrderItemWithSelectableLocations struct {
	ShippingOrderItem
	SelectableLocations StockLocations
}

type ShippingOrderItemWithEnabledChannels struct {
	ShippingOrderItem
	EnabledChannels collection.Set[int]
}

type ShippingOrderItemWithSizeInfo struct {
	ShippingOrderItem
	SizeInfo entity.ItemSizeInfo
}

type ShippingOrderWithEnabledChannels struct {
	ShippingOrder
	EnabledChannels collection.Set[int]
}

type ShippingOrderWithChannelWeightDimensionInfo struct {
	ShippingOrder
	ChannelIDToWeightDimensionInfo map[int]lpslib.ChannelWeightDimensionInfo
}

type WeighDimensionLimitation struct {
	WeightLimitationMax int
	HeightLimitationMax int
	VolumetricFactor    int
	Formula             int
}

type OrderFulfilmentInfo struct {
	Source                    string
	Type                      constant.FulfilmentType
	AddressID                 uint64
	AbleToFallbackSellerStock bool // deprecate later after cache order phase 2
}

const (
	// Sum(item_actual_weight*item_quantity)
	WeightFormula101 = 101

	// actual_weight = Sum(item_actual_weight*item_quantity)
	// volumetric_weight = Max(item length)* Max(item width) * Sum(item height) / volumetric factor
	// result_weight = Max(Order_actual_weight, Order_volumetric_weight)
	WeightFormula3002 = 3002

	// actual_weight = Sum(item_actual_weight*item_quantity)
	// volumetric_weight = Sum((item_length * item_width * item_height / volumetric factor) * item_quantity)
	// result_weight = Max(Order_actual_weight, Order_volumetric_weight)
	WeightFormula3016 = 3016

	// Order_actual_weight = Sum(item_actual_weight*item_quantity) (101)
	// Order_volumetric_weight = Max(item length)* Max(item width) * Sum(item height) / volumetric factor (202)
	// if Order_volumetric_weight <= cap(5kg)
	//     result = Sum(item_actual_weight*item_quantity)（101）
	// else
	//     result = Max(Order_actual_weight, Order_volumetric_weight) (301)
	WeightFormula3017 = 3017

	// item_volumetric_weight = item_length * item_width * item_height / volumetric factor
	// Sum(Max(item_actual_weight, item_volumetric_weight) * item_quantity)
	WeightFormula304 = 304
)

const (
	VolumetricWeightInflation = 1000
)
