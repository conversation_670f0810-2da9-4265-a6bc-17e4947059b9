package group_entity

// GroupingRules 领域实体，表示所有分组规则
type GroupingRules struct {
	BundleRules     []BundleRule
	IsolationRules  []IsolationRule
	ConstraintRules []ConstraintRule
	ChannelRules    []ChannelRule
}

// BundleRule 捆绑规则领域实体
// 用于将指定的商品强制合并到同一包裹
type BundleRule struct {
	RuleID       string
	ItemQueryIDs []string
	Mandatory    bool
}

// IsolationRule 隔离规则领域实体
// 用于将特定商品从普通订单中隔离出来
type IsolationRule struct {
	RuleID        string
	ItemQueryIDs  []string
	Conditions    []RuleCondition
	IsolationType IsolationType
	Mandatory     bool
}

// ConstraintRule 约束规则领域实体
// 用于处理商品级别的约束条件
type ConstraintRule struct {
	RuleID         string
	ItemQueryIDs   []string
	ConstraintType ConstraintType
	Mandatory      bool
}

// ChannelRule 渠道规则领域实体
// 用于基于物流渠道的约束和验证
type ChannelRule struct {
	RuleID       string
	ItemQueryIDs []string
	Conditions   []RuleCondition
	RuleType     ChannelRuleType
	Mandatory    bool
}

// RuleCondition 规则条件领域实体
type RuleCondition struct {
	ConditionType ConditionType
	ChannelIDs    []int64
}

// ========== 枚举类型定义 ==========

// IsolationType 隔离类型枚举
type IsolationType int

const (
	IsolationTypeSingleItem    IsolationType = 1 // 单商品隔离，每个商品独立包裹
	IsolationTypeIsolatedGroup IsolationType = 2 // 分组隔离，符合条件的商品组成独立分组
)

// ConstraintType 约束类型枚举
type ConstraintType int

const (
	ConstraintTypeDisableQuantitySplit ConstraintType = 1 // 禁止数量拆分
	ConstraintTypeSingleSourceOnly     ConstraintType = 2 // 单一库存源
)

// ChannelRuleType 渠道规则类型枚举
type ChannelRuleType int

const (
	ChannelRuleTypeCommonChannelRequired ChannelRuleType = 1 // 需要公共渠道
	ChannelRuleTypeChannelExclusive      ChannelRuleType = 2 // 渠道排他
)

// ConditionType 条件类型枚举
type ConditionType int

const (
	ConditionTypeChannelEnabled ConditionType = 1 // 渠道启用条件
)

// ========== 辅助方法 ==========

// IsEmpty 检查分组规则是否为空
func (gr *GroupingRules) IsEmpty() bool {
	if gr == nil {
		return true
	}
	return len(gr.BundleRules) == 0 &&
		len(gr.IsolationRules) == 0 &&
		len(gr.ConstraintRules) == 0 &&
		len(gr.ChannelRules) == 0
}

// String 方法用于调试和日志记录
func (it IsolationType) String() string {
	switch it {
	case IsolationTypeSingleItem:
		return "single_item"
	case IsolationTypeIsolatedGroup:
		return "isolated_group"
	default:
		return "unknown"
	}
}

func (ct ConstraintType) String() string {
	switch ct {
	case ConstraintTypeDisableQuantitySplit:
		return "disable_quantity_split"
	case ConstraintTypeSingleSourceOnly:
		return "single_source_only"
	default:
		return "unknown"
	}
}

func (crt ChannelRuleType) String() string {
	switch crt {
	case ChannelRuleTypeCommonChannelRequired:
		return "common_channel_required"
	case ChannelRuleTypeChannelExclusive:
		return "channel_exclusive"
	default:
		return "unknown"
	}
}

func (ct ConditionType) String() string {
	switch ct {
	case ConditionTypeChannelEnabled:
		return "channel_enabled"
	default:
		return "unknown"
	}
}
