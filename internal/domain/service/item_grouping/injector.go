package item_grouping

import (
	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/processor"
)

var (
	ItemGrouperProviderSet = wire.NewSet(
		NewItemGrouper,
		wire.Bind(new(ItemGrouper), new(*DefaultItemGrouper)),
	)

	ShippingOrderProcessorProviderSet = wire.NewSet(
		processor.NewAdvanceBookingShippingOrderProcessor,
		processor.NewCBLFFShippingOrderProcessor,
		processor.NewPffShippingOrderProcessor,
		processor.NewCb3pfShippingOrderProcessor,
		processor.NewShopeeShippingOrderProcessor,
		processor.NewSellerShippingOrderProcessor,
		processor.NewWeightDimensionProcessor,
		processor.NewResellShippingOrderProcessor,
		processor.NewGroupShipmentOrderProcessor,
		processor.GroupingRulesProcessorProviderSet,
	)
)
