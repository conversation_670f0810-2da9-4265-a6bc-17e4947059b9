package item_grouping

import (
	"context"
	"testing"

	abconfig "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/processor"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
	"github.com/stretchr/testify/assert"
)

// Helper types for execution order tracking
type executionOrderTracker struct {
	order *[]string
}

// Mock processors for testing execution order
type mockGroupingRulesProcessor struct {
	tracker *executionOrderTracker
}

func (m *mockGroupingRulesProcessor) ApplyRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules *group_entity.GroupingRules) ([]group_entity.ShippingOrder, fsserr.Error) {
	return shippingOrders, nil
}

func (m *mockGroupingRulesProcessor) ApplyBundleRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, bundleRules []group_entity.BundleRule) ([]group_entity.ShippingOrder, fsserr.Error) {
	*m.tracker.order = append(*m.tracker.order, "ApplyBundleRules")
	return shippingOrders, nil
}

func (m *mockGroupingRulesProcessor) ApplyNonBundleRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules *group_entity.GroupingRules) ([]group_entity.ShippingOrder, fsserr.Error) {
	*m.tracker.order = append(*m.tracker.order, "ApplyNonBundleRules")
	return shippingOrders, nil
}

type mockWeightDimensionProcessor struct {
	tracker *executionOrderTracker
}

func (m *mockWeightDimensionProcessor) Name() string {
	return "WeightDimensionProcessor"
}

func (m *mockWeightDimensionProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return true
}

func (m *mockWeightDimensionProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, fsserr.Error) {
	*m.tracker.order = append(*m.tracker.order, "WeightDimensionProcess")
	return shippingOrders, nil
}

// Mock configuration accessor - must implement all methods from ConfAccessor interface
type mockConfAccessor struct {
	weightDimPercentage int
	weightDimWhitelist  []uint64
}

// ApplicationAccessor methods
func (m *mockConfAccessor) GetSpexConfig(ctx context.Context) application_config.SpexConfig {
	return application_config.SpexConfig{}
}

// BusinessAccessor methods
func (m *mockConfAccessor) GetMigrationConfig(ctx context.Context) business_config.MigrationConfig {
	return business_config.MigrationConfig{}
}

func (m *mockConfAccessor) GetIGS3PFShopFlags(ctx context.Context) []uint64 {
	return []uint64{}
}

func (m *mockConfAccessor) GetIGSLFFShopFlags(ctx context.Context) []uint64 {
	return []uint64{}
}

func (m *mockConfAccessor) GetIGSResellModeShopFlags(ctx context.Context) []uint64 {
	return []uint64{}
}

func (m *mockConfAccessor) GetLFFWarehouseRegionPriority(ctx context.Context) []string {
	return []string{}
}

func (m *mockConfAccessor) GetIGSDynamicConfig(ctx context.Context) business_config.IGSDynamicConfig {
	return business_config.IGSDynamicConfig{}
}

func (m *mockConfAccessor) GetIGSShopBusinessModeConfig(ctx context.Context) business_config.IGSShopBusinessModeConfig {
	return business_config.IGSShopBusinessModeConfig{}
}

func (m *mockConfAccessor) GetIsShopeeFoodRegion(ctx context.Context) bool {
	return false
}

func (m *mockConfAccessor) IsEnableWarehouseRegionForAddressAPI(ctx context.Context) bool {
	return false
}

func (m *mockConfAccessor) GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx context.Context) map[string]business_config.GeoLocationConfig {
	return map[string]business_config.GeoLocationConfig{}
}

func (m *mockConfAccessor) GetDefaultBuyerGeoLocationConfig(ctx context.Context) business_config.GeoLocationConfig {
	return business_config.GeoLocationConfig{}
}

func (m *mockConfAccessor) GetABTestingConfig(ctx context.Context) abconfig.Config {
	return abconfig.Config{}
}

func (m *mockConfAccessor) GetEnableCachedSellerTagFlow(ctx context.Context) bool {
	return false
}

func (m *mockConfAccessor) GetBatchGetEntityTagAPISize(ctx context.Context) int {
	return 100
}

func (m *mockConfAccessor) GetAllocateWarehouseBySalesOrderGroups(ctx context.Context) []business_config.AllocateWarehouseBySalesOrdersGroup {
	return []business_config.AllocateWarehouseBySalesOrdersGroup{}
}

func (m *mockConfAccessor) GetSupportSellerMultiWhPFF(ctx context.Context) bool {
	return false
}

func (m *mockConfAccessor) GetSellerMultiWHWithPartialFBSConfig(ctx context.Context) business_config.SellerMultiWhWithPartialFBSConfig {
	return business_config.SellerMultiWhWithPartialFBSConfig{}
}

func (m *mockConfAccessor) GetEnableAllocateWarehouseBySalesOrders(ctx context.Context) bool {
	return false
}

func (m *mockConfAccessor) GetEnable3PFIgnoreSellerTag(ctx context.Context) bool {
	return false
}

func (m *mockConfAccessor) GetShopEnableChannelsAPIBatchSize(ctx context.Context) int {
	return 100
}

func (m *mockConfAccessor) GetProductInfoAPISize(ctx context.Context) int {
	return 100
}

func (m *mockConfAccessor) GetGroupSellerCoverShippingFeeConfig(ctx context.Context) int {
	return 0
}

func (m *mockConfAccessor) GetFilterChannelsForPreorder(ctx context.Context) []int {
	return []int{}
}

func (m *mockConfAccessor) GetCounterfeitItemChannelBlockToggle(ctx context.Context) bool {
	return false
}

func (m *mockConfAccessor) GetCounterfeitItemLabelId(ctx context.Context) uint64 {
	return 0
}

func (m *mockConfAccessor) GetChannelsWithSelectableDeliveryTime(ctx context.Context) []int {
	return []int{}
}

func (m *mockConfAccessor) GetWeightDimMaxNumOfShippingOrders(ctx context.Context) int {
	return 10
}

func (m *mockConfAccessor) GetWeightDimOrderSplitPercentage(ctx context.Context) int {
	return m.weightDimPercentage
}

func (m *mockConfAccessor) GetWeightDimOrderSplitWhitelist(ctx context.Context) []uint64 {
	return m.weightDimWhitelist
}

func (m *mockConfAccessor) GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix(ctx context.Context) bool {
	return false
}

// MutableApplicationAccessor methods
func (m *mockConfAccessor) GetLruCacheConfig(ctx context.Context) mutable_application_config.LruCacheConfig {
	return mutable_application_config.LruCacheConfig{}
}

func (m *mockConfAccessor) GetLayerCacheConfig(ctx context.Context) mutable_application_config.LayerCacheConfig {
	return mutable_application_config.LayerCacheConfig{}
}

func (m *mockConfAccessor) GetLocalCacheConfig(ctx context.Context) mutable_application_config.LocalCacheConfig {
	return mutable_application_config.LocalCacheConfig{}
}

func (m *mockConfAccessor) GetHttpTimeoutConfig(ctx context.Context) mutable_application_config.TimeoutConfig {
	return mutable_application_config.TimeoutConfig{}
}

func (m *mockConfAccessor) GetGrpcTimeoutConfig(ctx context.Context) mutable_application_config.TimeoutConfig {
	return mutable_application_config.TimeoutConfig{}
}

func (m *mockConfAccessor) GetSpexTimeoutConfig(ctx context.Context) mutable_application_config.TimeoutConfig {
	return mutable_application_config.TimeoutConfig{}
}

func (m *mockConfAccessor) GetLPSAPIToken(ctx context.Context) string {
	return ""
}

func (m *mockConfAccessor) GetSBSApiKey(ctx context.Context) string {
	return ""
}

func (m *mockConfAccessor) GetSBSApiSecret(ctx context.Context) string {
	return ""
}

func (m *mockConfAccessor) GetOmsApiSecret(ctx context.Context) string {
	return ""
}

// ServerAccessor methods
func (m *mockConfAccessor) GetReportDegradeConfig(ctx context.Context) server_config.ReportDegradeConfig {
	return server_config.ReportDegradeConfig{}
}

// Init methods
func (m *mockConfAccessor) Init(ctx context.Context) error {
	return nil
}

// Mock processors for testing
type mockShippingOrderProcessor struct {
	name    string
	enabled bool
}

func (m *mockShippingOrderProcessor) Name() string {
	return m.name
}

func (m *mockShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return m.enabled
}

func (m *mockShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, fsserr.Error) {
	return shippingOrders, nil
}

func TestItemGrouper_GroupItems_SeparatesBundleAndOtherRules(t *testing.T) {
	// This test verifies that bundle rules and non-bundle rules are processed correctly
	// by using a minimal setup that avoids dependency complexity

	ctx := context.Background()
	processor := processor.NewGroupingRulesProcessor()

	// Test BundleRules first
	shippingOrders := []group_entity.ShippingOrder{
		createTestShippingOrder([]group_entity.ShippingOrderItem{
			createTestShippingOrderItem("query_1", 123, 456, 1, 1),
			createTestShippingOrderItem("query_2", 123, 456, 2, 1),
		}),
	}

	bundleRules := []group_entity.BundleRule{
		{
			RuleID:       "bundle_rule_1",
			ItemQueryIDs: []string{"query_1", "query_2"},
			Mandatory:    true,
		},
	}

	// Step 1: Apply bundle rules first
	bundledOrders, err := processor.ApplyBundleRules(ctx, shippingOrders, bundleRules)
	assert.Nil(t, err)
	assert.NotNil(t, bundledOrders)

	// Should bundle the two orders into one
	if len(bundledOrders) == 1 {
		// Verify the bundled order contains items from both original orders
		totalItems := 0
		for _, order := range bundledOrders {
			totalItems += len(order.Items)
		}
		assert.Equal(t, 2, totalItems, "Expected bundled order to contain 2 items")
	}

	// Step 2: Apply non-bundle rules after processors (simulated)
	rules := &group_entity.GroupingRules{
		ConstraintRules: []group_entity.ConstraintRule{
			{
				RuleID:         "constraint_rule_1",
				ItemQueryIDs:   []string{"query_1"},
				ConstraintType: group_entity.ConstraintTypeDisableQuantitySplit,
				Mandatory:      true,
			},
		},
	}

	finalOrders, err := processor.ApplyNonBundleRules(ctx, bundledOrders, rules)
	assert.Nil(t, err)
	assert.NotNil(t, finalOrders)

	t.Logf("Successfully processed bundle and constraint rules. Initial: %d, Bundled: %d, Final: %d orders",
		len(shippingOrders), len(bundledOrders), len(finalOrders))
}

// Helper functions
func createTestShippingOrderItem(queryId string, shopID uint64, itemID uint64, modelID uint64, quantity uint32) group_entity.ShippingOrderItem {
	return group_entity.ShippingOrderItem{
		QueryId:  queryId,
		ShopInfo: group_entity.ShopInfo{ShopID: shopID},
		ItemID:   itemID,
		ModelID:  modelID,
		Quantity: quantity,
		FulfilmentTypeToLocations: map[constant.FulfilmentType]group_entity.StockLocations{
			constant.FulfilmentTypeShopee: {
				{
					Source:          "SG01",
					FulfilmentType:  constant.FulfilmentTypeShopee,
					AvailableStock:  100,
					EnabledChannels: []int{1, 2, 3},
				},
			},
		},
	}
}

func createTestShippingOrder(items []group_entity.ShippingOrderItem) group_entity.ShippingOrder {
	return group_entity.ShippingOrder{
		Items: items,
	}
}

func TestDefaultItemGrouper_GroupItems_ProcessingOrder(t *testing.T) {
	// This test focuses on testing the new method signatures without full integration
	// to avoid dependency injection complexity

	ctx := context.Background()
	processor := processor.NewGroupingRulesProcessor()

	// Test orders
	orders := []group_entity.ShippingOrder{createTestShippingOrder([]group_entity.ShippingOrderItem{
		createTestShippingOrderItem("query_1", 123, 456, 1, 1),
	})}

	// Test BundleRules processing
	bundleRules := []group_entity.BundleRule{
		{
			RuleID:       "bundle_rule_1",
			ItemQueryIDs: []string{"query_1"},
			Mandatory:    true,
		},
	}

	bundledOrders, err := processor.ApplyBundleRules(ctx, orders, bundleRules)
	assert.Nil(t, err)
	assert.NotNil(t, bundledOrders)

	// Test Non-BundleRules processing
	rules := &group_entity.GroupingRules{
		ConstraintRules: []group_entity.ConstraintRule{
			{
				RuleID:         "constraint_rule_1",
				ItemQueryIDs:   []string{"query_1"},
				ConstraintType: group_entity.ConstraintTypeDisableQuantitySplit,
				Mandatory:      true,
			},
		},
	}

	finalOrders, err := processor.ApplyNonBundleRules(ctx, bundledOrders, rules)
	assert.Nil(t, err)
	assert.NotNil(t, finalOrders)

	t.Log("Successfully tested the new processing order: BundleRules -> NonBundleRules")
}

// TestDefaultItemGrouper_SingleSourceRuleValidation 测试 Single Source Rule 验证逻辑
