package sbs

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_fulfilment_sbs.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/remote_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type Service interface {
	GetSBSShopItemInfos(ctx context.Context, shopIDToItemModelIDs map[uint64][]entity.ItemModelID) ([]entity.SBSShopInfo, fsserr.Error)
	BatchGetSBSShipmentGroups(ctx context.Context, groupIDs []uint32, lang string) (map[uint32]entity.SBSShipmentGroup, fsserr.Error)
}

type ServiceImpl struct {
	spexClient             spexlib.SpexClient
	sbsShipmentGroupsCache cache.MultiCache[uint32, *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails]
}

func NewServiceImpl(ctx context.Context,
	clients redishelper.GlobalRedisClients,
	spexClient spexlib.SpexClient) (*ServiceImpl, error) {
	defaultClient, err := clients.GetRedisClusterByClusterName(redishelper.Default)
	if err != nil {
		return nil, err
	}
	sbsShipmentGroupsCache, err := mixed_cache.NewPbLruLayerCache[uint32, *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails](
		cache.SBSShipmentGroup,
		defaultClient,
		[]lru.InitOptions{lru.WithLruSize(10000), lru.WithLruTimeout(cache.LongCacheDuration)},
		[]remote_cache.InitOptions[*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails]{
			remote_cache.WithTimeout[*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails](cache.LongCacheDuration),
		},
	)
	return &ServiceImpl{
		sbsShipmentGroupsCache: sbsShipmentGroupsCache,
		spexClient:             spexClient,
	}, nil
}

func (s *ServiceImpl) GetSBSShopItemInfos(
	ctx context.Context,
	shopIDToItemModelIDs map[uint64][]entity.ItemModelID,
) ([]entity.SBSShopInfo, fsserr.Error) {
	sbsShopItemInfos, err := s.getSBSItemInfo(ctx, shopIDToItemModelIDs)
	if err != nil {
		return nil, err
	}

	var shopInfos []entity.SBSShopInfo
	for _, info := range sbsShopItemInfos {
		var itemModelInfos []entity.SBSItemModelInfo
		for _, itemInfo := range info.GetItems() {
			itemModelInfos = append(itemModelInfos, entity.SBSItemModelInfo{
				ItemID:        typ.ItemIdType(itemInfo.GetItemId()),
				ModelID:       typ.ModelIdType(itemInfo.GetModelId()),
				IsSBSItem:     itemInfo.GetIsSbsItem(),
				GroupShipment: itemInfo.GetGroupShipment(),
			})
		}

		shopInfo := entity.SBSShopInfo{
			ShopID:          info.GetShopId(),
			IsSBSShop:       info.GetIsSbsShop(),
			WarehouseID:     info.GetWarehouseId(),
			Service:         info.GetService(),
			ShipmentGroupID: uint32(info.GetShipmentGroupId()),
			ItemModelInfos:  itemModelInfos,
		}

		shopInfos = append(shopInfos, shopInfo)
	}

	return shopInfos, nil
}

func (s *ServiceImpl) getSBSItemInfo(ctx context.Context, shopIDToItemModelIDs map[uint64][]entity.ItemModelID) ([]*marketplace_order_processing_fulfilment_sbs.SbsShopItemInfo, fsserr.Error) {
	var shopItems []*marketplace_order_processing_fulfilment_sbs.ItemParam
	for shopID, itemModelIDs := range shopIDToItemModelIDs {
		var ItemModelParams []*marketplace_order_processing_fulfilment_sbs.ItemModelParam
		for _, itemModelID := range itemModelIDs {
			ItemModelParams = append(ItemModelParams, &marketplace_order_processing_fulfilment_sbs.ItemModelParam{
				ItemId:  proto.Uint64(uint64(itemModelID.ItemID)),
				ModelId: proto.Uint64(uint64(itemModelID.ModelID)),
			})
		}
		shopItems = append(shopItems, &marketplace_order_processing_fulfilment_sbs.ItemParam{
			ShopId:        proto.Uint64(shopID),
			ItemModelList: ItemModelParams,
		})
	}

	response, err := s.spexClient.GetSbsItemInfo(ctx, shopItems)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *ServiceImpl) BatchGetSBSShipmentGroups(ctx context.Context, groupIDs []uint32, lang string) (map[uint32]entity.SBSShipmentGroup, fsserr.Error) {
	sbsShipmentGroups, err := s.getSBSShipmentGroupsUsingCache(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	groupIDToShipmentGroup := make(map[uint32]entity.SBSShipmentGroup)
	for _, group := range sbsShipmentGroups {

		localisedDescription := parseLocalisedDescription(group, lang)
		shipmentGroup := entity.SBSShipmentGroup{
			ID:          group.GetGroupId(),
			Icon:        group.GetIcon(),
			Description: localisedDescription,
			Priority:    group.GetPriority(),
			NotGrouping: group.GetNotGrouping(),
		}
		groupIDToShipmentGroup[shipmentGroup.ID] = shipmentGroup
	}

	return groupIDToShipmentGroup, nil
}

func (s *ServiceImpl) getSBSShipmentGroupsUsingCache(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, fsserr.Error) {
	result, err := cache.MultiLoadManyFromSlice(
		ctx,
		s.sbsShipmentGroupsCache,
		groupIDs,
		s.GetSBSShipmentGroups,
		func(details *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails) uint32 {
			return details.GetGroupId()
		},
		cache.WithKeyConvertor[uint32, *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails](getSBSShipmentGroupsKey),
	)
	if err != nil {
		return nil, fsserr.With(fsserr.DataErr, err)
	}

	return collection.MapValues(result), nil
}

func (s *ServiceImpl) GetSBSShipmentGroups(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, error) {
	caller := concurrency.NewConcurrencySplitCaller[uint32, *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails]()
	resp, err := caller.Call(ctx, groupIDs, 20, s.getSBSShipmentGroups)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *ServiceImpl) getSBSShipmentGroups(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, error) {
	response, err := s.spexClient.GetSBSShipmentGroups(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func parseLocalisedDescription(groupInfoDetails *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, lang string) string {
	// parse Localised Description will fall back to original name if no translations are found
	fallback := groupInfoDetails.GetDescription()
	primaryContent := fallback
	for _, lc := range groupInfoDetails.GetLocalisedDescription() {
		if lc == nil {
			continue
		}

		if lc.GetLang() == lang && strings.TrimSpace(lc.GetLocalisedContent()) != "" {
			return lc.GetLocalisedContent() // early exit
		}

		if lc.GetIsPrimary() && strings.TrimSpace(lc.GetLocalisedContent()) != "" {
			primaryContent = lc.GetLocalisedContent() // replace primary content with valid translation
		}
	}
	return primaryContent
}

func getSBSShipmentGroupsKey(ctx context.Context, groupID uint32) string {
	return common.GenKeyWithRegion(ctx, ":",
		"sbs.group_shipment.v1", strconv.FormatUint(uint64(groupID), 10))
}
