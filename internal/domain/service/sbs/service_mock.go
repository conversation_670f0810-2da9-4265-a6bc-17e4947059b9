// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package sbs is a generated GoMock package.
package sbs

import (
	context "context"
	reflect "reflect"

	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// BatchGetSBSShipmentGroups mocks base method.
func (m *MockService) BatchGetSBSShipmentGroups(ctx context.Context, groupIDs []uint32, lang string) (map[uint32]entity.SBSShipmentGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetSBSShipmentGroups", ctx, groupIDs, lang)
	ret0, _ := ret[0].(map[uint32]entity.SBSShipmentGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSBSShipmentGroups indicates an expected call of BatchGetSBSShipmentGroups.
func (mr *MockServiceMockRecorder) BatchGetSBSShipmentGroups(ctx, groupIDs, lang interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSBSShipmentGroups", reflect.TypeOf((*MockService)(nil).BatchGetSBSShipmentGroups), ctx, groupIDs, lang)
}

// GetSBSShopItemInfos mocks base method.
func (m *MockService) GetSBSShopItemInfos(ctx context.Context, shopIDToItemModelIDs map[uint64][]entity.ItemModelID) ([]entity.SBSShopInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSBSShopItemInfos", ctx, shopIDToItemModelIDs)
	ret0, _ := ret[0].([]entity.SBSShopInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSBSShopItemInfos indicates an expected call of GetSBSShopItemInfos.
func (mr *MockServiceMockRecorder) GetSBSShopItemInfos(ctx, shopIDToItemModelIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSBSShopItemInfos", reflect.TypeOf((*MockService)(nil).GetSBSShopItemInfos), ctx, shopIDToItemModelIDs)
}
