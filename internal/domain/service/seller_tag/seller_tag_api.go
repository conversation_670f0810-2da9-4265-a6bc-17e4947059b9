package seller_tag

import (
	"context"
	"strconv"
	"strings"

	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_tag_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type SellerTagApi interface {
	GetShopSellerTagV2(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error)
	BatchGetShopSellerTags(ctx context.Context, shopIDs []uint64, tagNames []string) ([]*seller_seller_tag_core.EntityTag, error)
	BatchGetShopSellerTagsV2(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error)

	GetShopSellerTagV2WithCache(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error)
	BatchGetShopSellerTagsV2WithCache(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error)
}

type SellerTagApiImpl struct {
	SpexClient        spexlib.SpexClient
	configGetter      config.ConfAccessor
	entityTagLruCache cache.MultiCache[entity.SellerTagQuery, *seller_seller_tag_core.EntityTag]
	tagValueLruCache  cache.MultiCache[entity.SellerTagQuery, *seller_seller_tag_core.TagValue]
}

func NewSellerTagApiImpl(
	spexClient spexlib.SpexClient,
	configGetter config.ConfAccessor,
	clients redishelper.GlobalRedisClients,
) (*SellerTagApiImpl, error) {
	defaultClient, err := clients.GetRedisClusterByClusterName(redishelper.Default)
	if err != nil {
		return nil, err
	}

	entityTagLruCache, err := mixed_cache.NewLruLayerCache[entity.SellerTagQuery, *seller_seller_tag_core.EntityTag](
		cache.EntityTagLruName,
		defaultClient,
		nil,
		nil,
	)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}

	tagValueLruCache, err := mixed_cache.NewLruLayerCache[entity.SellerTagQuery, *seller_seller_tag_core.TagValue](
		cache.TagValueLruName, defaultClient, nil, nil)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}

	return &SellerTagApiImpl{
		SpexClient:        spexClient,
		configGetter:      configGetter,
		entityTagLruCache: entityTagLruCache,
		tagValueLruCache:  tagValueLruCache,
	}, nil
}

func (s *SellerTagApiImpl) GetShopSellerTagV2(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error) {
	entities := make([]*seller_seller_tag_core.EntityTagIDs, 0)
	for _, sID := range shopIDs {
		e := &seller_seller_tag_core.EntityTagIDs{
			Entity: &seller_seller_tag_core.Entity{
				Id:   proto.Uint64(sID),
				Type: proto.Uint32(1),
			},
			TagNames: []string{tagName},
		}
		entities = append(entities, e)
	}
	size := s.configGetter.GetBatchGetEntityTagAPISize(ctx)
	caller := concurrency.NewConcurrencySplitCaller[*seller_seller_tag_core.EntityTagIDs, *seller_seller_tag_core.EntityTag]()
	entityTags, err := caller.Call(ctx, entities, size, func(ctx context.Context, queries []*seller_seller_tag_core.EntityTagIDs) ([]*seller_seller_tag_core.EntityTag, error) {
		req := &seller_seller_tag_core.BatchGetEntityTagRequest{
			EntityTagsList: queries,
		}
		entityTags, err := s.SpexClient.BatchGetEntityTag(ctx, req)
		if err != nil {
			return nil, err
		}
		return entityTags, nil
	})
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	return &seller_seller_tag_core.BatchGetEntityTagResponse{
		EntityTags: entityTags,
		DebugMsg:   nil,
	}, nil
}

func (s *SellerTagApiImpl) GetShopSellerTagV2WithCache(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error) {
	keys := make([]entity.SellerTagQuery, 0, len(shopIDs))
	for _, shopId := range shopIDs {
		keys = append(keys, entity.SellerTagQuery{
			ShopID:  shopId,
			TagName: tagName,
		})
	}

	cacheResult, err := cache.MultiLoadManyFromSlice(
		ctx,
		s.entityTagLruCache,
		keys,
		func(ctx2 context.Context, missing []entity.SellerTagQuery) ([]*seller_seller_tag_core.EntityTag, error) {
			shopIds := make([]uint64, 0, len(missing))
			for _, q := range missing {
				shopIds = append(shopIds, q.ShopID)
			}
			resp, internalErr := s.GetShopSellerTagV2(ctx, shopIds, tagName)
			if internalErr != nil {
				return nil, internalErr
			}
			return resp.GetEntityTags(), nil
		},
		func(tag *seller_seller_tag_core.EntityTag) entity.SellerTagQuery {
			return entity.SellerTagQuery{
				ShopID:  tag.GetEntity().GetId(),
				TagName: tagName,
			}
		},
		cache.WithKeyConvertor[entity.SellerTagQuery, *seller_seller_tag_core.EntityTag](s.getShopSellerTagCacheKey),
	)

	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	return &seller_seller_tag_core.BatchGetEntityTagResponse{
		EntityTags: collection.MapValues(cacheResult),
	}, nil
}

func (s *SellerTagApiImpl) BatchGetShopSellerTags(ctx context.Context, shopIDs []uint64, tagNames []string) ([]*seller_seller_tag_core.EntityTag, error) {
	caller := concurrency.NewConcurrencySplitCaller[uint64, *seller_seller_tag_core.EntityTag]()
	resp, err := caller.Call(ctx, shopIDs, 20, func(ctx context.Context, queries []uint64) ([]*seller_seller_tag_core.EntityTag, error) {
		return s.batchGetShopSellerTags(ctx, queries, tagNames)
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *SellerTagApiImpl) batchGetShopSellerTags(ctx context.Context, shopIDs []uint64, tagNames []string) ([]*seller_seller_tag_core.EntityTag, error) {
	req := &seller_seller_tag_core.BatchGetEntityTagRequest{}
	entityTagIDs := make([]*seller_seller_tag_core.EntityTagIDs, 0)
	for _, shopID := range shopIDs {
		entityTagIDs = append(entityTagIDs, &seller_seller_tag_core.EntityTagIDs{
			Entity: &seller_seller_tag_core.Entity{
				Id:   proto.Uint64(shopID),
				Type: proto.Uint32(uint32(seller_seller_tag_core.Constant_ENTITY_TYPE_SHOP)),
			},
			TagNames: tagNames,
		})
	}
	req.EntityTagsList = entityTagIDs

	entityTags, err := s.SpexClient.BatchGetEntityTag(ctx, req)
	if err != nil {
		return nil, err
	}

	return entityTags, nil
}

func (s *SellerTagApiImpl) BatchGetShopSellerTagsV2(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error) {
	shopIDToTagNames := make(map[uint64][]string)
	for _, query := range queries {
		shopIDToTagNames[query.ShopID] = append(shopIDToTagNames[query.ShopID], query.TagName)
	}

	var entityTagIDs []*seller_seller_tag_core.EntityTagIDs
	for shopID, tagNames := range shopIDToTagNames {
		entityTagIDs = append(entityTagIDs, &seller_seller_tag_core.EntityTagIDs{
			Entity: &seller_seller_tag_core.Entity{
				Id:   proto.Uint64(shopID),
				Type: proto.Uint32(uint32(seller_seller_tag_core.Constant_ENTITY_TYPE_SHOP)),
			},
			TagNames: tagNames,
		})
	}

	caller := concurrency.NewConcurrencySplitCaller[*seller_seller_tag_core.EntityTagIDs, *seller_seller_tag_core.EntityTag]()
	resp, err := caller.Call(ctx, entityTagIDs, 20, s.batchGetShopSellerTagsV2)
	if err != nil {
		return nil, err
	}

	queryToTagValue := make(map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue)
	for _, entityTag := range resp {
		queryToTagValue[entity.SellerTagQuery{
			ShopID:  entityTag.GetEntity().GetId(),
			TagName: strings.ToLower(entityTag.GetTagName()),
		}] = entityTag.GetValue()
	}

	return queryToTagValue, nil
}

func (s *SellerTagApiImpl) batchGetShopSellerTagsV2(ctx context.Context, queries []*seller_seller_tag_core.EntityTagIDs) ([]*seller_seller_tag_core.EntityTag, error) {
	req := &seller_seller_tag_core.BatchGetEntityTagRequest{
		EntityTagsList: queries,
	}
	entityTags, err := s.SpexClient.BatchGetEntityTag(ctx, req)
	if err != nil {
		return nil, err
	}

	return entityTags, nil
}

func (s *SellerTagApiImpl) BatchGetShopSellerTagsV2WithCache(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error) {
	result, err := cache.MultiLoadManyFromMap(
		ctx,
		s.tagValueLruCache,
		queries,
		s.BatchGetShopSellerTagsV2,
		cache.WithKeyConvertor[entity.SellerTagQuery, *seller_seller_tag_core.TagValue](s.getShopSellerTagV2CacheKey),
	)
	return result, err
}

func (s *SellerTagApiImpl) getShopSellerTagCacheKey(ctx context.Context, query entity.SellerTagQuery) string {
	return common.GenKeyWithRegion(ctx, "shop.seller.tag", ":", query.TagName, strconv.FormatUint(query.ShopID, 10))
}

func (s *SellerTagApiImpl) getShopSellerTagV2CacheKey(ctx context.Context, query entity.SellerTagQuery) string {
	return common.GenKeyWithRegion(ctx, "shop.seller.tag.v2", ":", query.TagName, strconv.FormatUint(query.ShopID, 10))
}
