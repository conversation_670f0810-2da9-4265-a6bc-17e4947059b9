package seller_tag

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

type SellerTagService interface {
	GetShopSellerTag(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error)
	GetShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error)
	GetShopIDToChoiceStatus(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error)
	GetShopIDToLocalRetailCBTag(ctx context.Context, shopIDs []uint64) (map[uint64]bool, error)
	BatchGetShopSellerTag(ctx context.Context, shopIDs []uint64, tagNames []string) (map[uint64]map[string]bool, error)
}

type SellerTagServiceImpl struct {
	configAccessor config.ConfAccessor
	sellerTagApi   SellerTagApi
}

func NewSellerTagServiceImpl(configAccessor config.ConfAccessor, sellerTagApi SellerTagApi) *SellerTagServiceImpl {
	return &SellerTagServiceImpl{
		configAccessor: configAccessor,
		sellerTagApi:   sellerTagApi,
	}
}

func (s *SellerTagServiceImpl) GetShopSellerTag(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	result := make(map[uint64]bool)
	resp, err := s.sellerTagApi.GetShopSellerTagV2(ctx, shopIDs, tagName)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get seller tag failed, shopIDs: %s, tagName: %s", Logger.JsonString(shopIDs), tagName)
		return result, err
	}
	for _, entityTag := range resp.GetEntityTags() {
		result[entityTag.GetEntity().GetId()] = entityTag.GetValue().GetBoolV()
	}
	return result, nil
}

func (s *SellerTagServiceImpl) GetShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	return s.getShopSellerTagWithCache(ctx, shopIDs, tagName)
}

func (s *SellerTagServiceImpl) BatchGetShopSellerTag(ctx context.Context, shopIDs []uint64, tagNames []string) (map[uint64]map[string]bool, error) {
	if s.configAccessor.GetEnableCachedSellerTagFlow(ctx) {
		shopIDToTagToValue, err := s.batchGetShopSellerTagWithCache(ctx, shopIDs, tagNames)
		if err != nil {
			return nil, err
		}
		return shopIDToTagToValue, nil
	}

	shopIDToTagToValue := make(map[uint64]map[string]bool)
	tags, err := s.sellerTagApi.BatchGetShopSellerTags(ctx, shopIDs, tagNames)
	if err != nil {
		return nil, err
	}
	for _, tag := range tags {
		shopID := tag.GetEntity().GetId()
		if _, ok := shopIDToTagToValue[shopID]; !ok {
			shopIDToTagToValue[shopID] = make(map[string]bool)
		}
		shopIDToTagToValue[shopID][strings.ToLower(tag.GetTagName())] = tag.GetValue().GetBoolV()
	}
	return shopIDToTagToValue, nil
}

func (s *SellerTagServiceImpl) batchGetShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagNames []string) (map[uint64]map[string]bool, error) {
	var queries []entity.SellerTagQuery
	for _, shopID := range shopIDs {
		for _, tagName := range tagNames {
			queries = append(queries, entity.SellerTagQuery{
				ShopID:  shopID,
				TagName: tagName,
			})
		}
	}

	queryToTagValue, err := s.sellerTagApi.BatchGetShopSellerTagsV2WithCache(ctx, queries)
	if err != nil {
		return nil, err
	}

	result := make(map[uint64]map[string]bool)
	for query, tagValue := range queryToTagValue {
		shopID := query.ShopID
		tagName := query.TagName
		if _, ok := result[shopID]; !ok {
			result[shopID] = make(map[string]bool)
		}
		result[shopID][strings.ToLower(tagName)] = tagValue.GetBoolV()
	}

	return result, nil
}

func (s *SellerTagServiceImpl) GetShopIDToChoiceStatus(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	return s.getShopSellerTagWithCache(ctx, shopIDs, tagName)
}

func (s *SellerTagServiceImpl) GetShopIDToLocalRetailCBTag(ctx context.Context, shopIDs []uint64) (map[uint64]bool, error) {
	return s.getShopSellerTagWithCache(ctx, shopIDs, constant.ShopTagLocalRetailCB)
}

func (s *SellerTagServiceImpl) getShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	result := make(map[uint64]bool)
	resp, err := s.sellerTagApi.GetShopSellerTagV2WithCache(ctx, shopIDs, tagName)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get seller tag failed, shopIDs: %s, tagName: %s", Logger.JsonString(shopIDs), tagName)
		return result, err
	}
	for _, entityTag := range resp.GetEntityTags() {
		result[entityTag.GetEntity().GetId()] = entityTag.GetValue().GetBoolV()
	}
	return result, nil
}
