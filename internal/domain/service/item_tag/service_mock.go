// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package item_tag is a generated GoMock package.
package item_tag

import (
	context "context"
	reflect "reflect"

	typ "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	gomock "github.com/golang/mock/gomock"
)

// MockItemTagService is a mock of ItemTagService interface.
type MockItemTagService struct {
	ctrl     *gomock.Controller
	recorder *MockItemTagServiceMockRecorder
}

// MockItemTagServiceMockRecorder is the mock recorder for MockItemTagService.
type MockItemTagServiceMockRecorder struct {
	mock *MockItemTagService
}

// NewMockItemTagService creates a new mock instance.
func NewMockItemTagService(ctrl *gomock.Controller) *MockItemTagService {
	mock := &MockItemTagService{ctrl: ctrl}
	mock.recorder = &MockItemTagServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockItemTagService) EXPECT() *MockItemTagServiceMockRecorder {
	return m.recorder
}

// BatchCheckItemContainsTag mocks base method.
func (m *MockItemTagService) BatchCheckItemContainsTag(ctx context.Context, itemIDs []typ.ItemIdType, tagID uint64) (map[uint64]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckItemContainsTag", ctx, itemIDs, tagID)
	ret0, _ := ret[0].(map[uint64]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckItemContainsTag indicates an expected call of BatchCheckItemContainsTag.
func (mr *MockItemTagServiceMockRecorder) BatchCheckItemContainsTag(ctx, itemIDs, tagID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckItemContainsTag", reflect.TypeOf((*MockItemTagService)(nil).BatchCheckItemContainsTag), ctx, itemIDs, tagID)
}

// BatchGetItemModelLabelIDs mocks base method.
func (m *MockItemTagService) BatchGetItemModelLabelIDs(ctx context.Context, itemModelIDs []typ.ItemIdType) (map[uint64][]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetItemModelLabelIDs", ctx, itemModelIDs)
	ret0, _ := ret[0].(map[uint64][]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetItemModelLabelIDs indicates an expected call of BatchGetItemModelLabelIDs.
func (mr *MockItemTagServiceMockRecorder) BatchGetItemModelLabelIDs(ctx, itemModelIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetItemModelLabelIDs", reflect.TypeOf((*MockItemTagService)(nil).BatchGetItemModelLabelIDs), ctx, itemModelIDs)
}
