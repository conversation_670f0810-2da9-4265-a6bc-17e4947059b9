package item_tag

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ItemTagService interface {
	BatchGetItemModelLabelIDs(ctx context.Context, itemModelIDs []typ.ItemIdType) (map[uint64][]uint64, fsserr.Error)
	BatchCheckItemContainsTag(ctx context.Context, itemIDs []typ.ItemIdType, tagID uint64) (map[uint64]bool, fsserr.Error)
}

type ItemTagServiceImpl struct {
	api TagApi
}

func NewItemTagServiceImpl(api TagApi) *ItemTagServiceImpl {
	return &ItemTagServiceImpl{api: api}
}

func (i *ItemTagServiceImpl) BatchGetItemModelLabelIDs(ctx context.Context, itemModelIDs []typ.ModelIdType) (map[typ.ModelIdType][]uint64, fsserr.Error) {
	modelIDToModelLabel, err := i.api.BatchGetItemModelIDToLabels(ctx, itemModelIDs)
	if err != nil {
		return nil, fsserr.With(fsserr.DataErr, err)
	}

	modelIDToLabelIDs := make(map[typ.ModelIdType][]uint64)
	for _, modelLabel := range modelIDToModelLabel {
		modelIDToLabelIDs[typ.ModelIdType(modelLabel.GetModelId())] = modelLabel.GetLabelIdList()
	}
	return modelIDToLabelIDs, nil
}

func (i *ItemTagServiceImpl) BatchCheckItemContainsTag(ctx context.Context, itemIDs []typ.ItemIdType, tagID uint64) (map[typ.ItemIdType]bool, fsserr.Error) {
	itemTags, err := i.batchItemTags(ctx, itemIDs, []uint64{tagID})
	if err != nil {
		return nil, fsserr.With(fsserr.DataErr, err)
	}

	hasTag := make(map[typ.ItemIdType]bool, len(itemTags))
	for _, itemTag := range itemTags {
		hasTag[itemTag.ItemID] = itemTag.IsTagged
	}

	return hasTag, nil
}

func (i *ItemTagServiceImpl) batchItemTags(ctx context.Context, itemIDs []typ.ItemIdType, tagIDs []uint64) ([]entity.ItemTag, error) {
	itemTags := make([]entity.ItemTag, len(itemIDs)*len(tagIDs))
	ids := make([]entity.ItemTagID, 0)
	for _, itemID := range itemIDs {
		for _, tagID := range tagIDs {
			itemTagID := entity.ItemTagID{ItemID: itemID, TagID: tagID}
			ids = append(ids, itemTagID)
		}
	}
	idToTag, err := i.api.GetItemLabels(ctx, ids)
	if err != nil {
		return nil, err
	}

	for i, id := range ids {
		itemTags[i] = idToTag[id]
	}

	return itemTags, nil
}
