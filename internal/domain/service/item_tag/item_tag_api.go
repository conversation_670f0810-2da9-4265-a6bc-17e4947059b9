package item_tag

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_itemtagservice_querying_api.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type TagApi interface {
	GetItemLabels(ctx context.Context, itemTagIDs []entity.ItemTagID) (map[entity.ItemTagID]entity.ItemTag, error)
	BatchGetItemModelIDToLabels(ctx context.Context, itemModelIDs []typ.ModelIdType) (map[typ.ModelIdType]*marketplace_listing_itemtagservice_querying_api.ModelLabel, error)
}

type TagApiImpl struct {
	SpexClient spexlib.SpexClient
}

func NewTagApiImpl(spexClient spexlib.SpexClient) *TagApiImpl {
	return &TagApiImpl{
		SpexClient: spexClient,
	}
}

func (t *TagApiImpl) GetItemLabels(ctx context.Context, itemTagIDs []entity.ItemTagID) (map[entity.ItemTagID]entity.ItemTag, error) {
	itemIDSet, tagIDSet := collection.NewSet[uint64](), collection.NewSet[uint64]()
	for _, id := range itemTagIDs {
		itemIDSet.Add(uint64(id.ItemID))
		tagIDSet.Add(id.TagID)
	}

	itemIDs := itemIDSet.ToSlice()
	if len(itemIDs) == 0 {
		return nil, nil
	}

	caller := concurrency.NewConcurrencySplitCaller[uint64, *marketplace_listing_itemtagservice_querying_api.ItemLabel]()
	itemLabels, err := caller.Call(ctx, itemIDs, int(marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest_MAX_ITEM_IDS_LEN),
		func(ctx context.Context, queries []uint64) ([]*marketplace_listing_itemtagservice_querying_api.ItemLabel, error) {
			req := &marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest{
				ItemIds: queries,
			}
			itemLabels, err := t.SpexClient.GetItemLabels(ctx, req)
			if err != nil {
				return nil, err
			}
			return itemLabels, nil
		})
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	itemTagIDSet := map[entity.ItemTagID]struct{}{}
	for _, itemLabel := range itemLabels {
		for _, labelID := range itemLabel.GetLabelIds() {
			if tagIDSet.Contains(labelID) {
				itemTagIDSet[entity.ItemTagID{ItemID: typ.ItemIdType(itemLabel.GetItemId()), TagID: labelID}] = struct{}{}
			}
		}
	}

	resultMap := make(map[entity.ItemTagID]entity.ItemTag)
	for _, id := range itemTagIDs {
		_, appear := itemTagIDSet[id]
		resultMap[id] = entity.ItemTag{
			ItemTagID: id,
			IsTagged:  appear,
		}
	}

	return resultMap, nil
}

func (t *TagApiImpl) BatchGetItemModelIDToLabels(ctx context.Context, itemModelIDs []typ.ModelIdType) (map[typ.ModelIdType]*marketplace_listing_itemtagservice_querying_api.ModelLabel, error) {
	caller := concurrency.NewConcurrencySplitCaller[typ.ModelIdType, *marketplace_listing_itemtagservice_querying_api.ModelLabel]()
	modelLabels, err := caller.Call(ctx, itemModelIDs, 50, t.batchGetItemModelLabels)
	if err != nil {
		return nil, err
	}

	modelIDToLabelInfo := make(map[typ.ModelIdType]*marketplace_listing_itemtagservice_querying_api.ModelLabel)
	for _, modelLabel := range modelLabels {
		modelIDToLabelInfo[typ.ModelIdType(modelLabel.GetModelId())] = modelLabel
	}
	return modelIDToLabelInfo, nil
}

func (t *TagApiImpl) batchGetItemModelLabels(ctx context.Context, itemModelIDs []typ.ModelIdType) ([]*marketplace_listing_itemtagservice_querying_api.ModelLabel, error) {
	request := &marketplace_listing_itemtagservice_querying_api.GetModelLabelsRequest{
		ModelIds: typ.ConvertIntegerSlices[typ.ModelIdType, uint64](itemModelIDs),
	}
	modelLabels, err := t.SpexClient.GetModelLabels(ctx, request)
	if err != nil {
		return nil, err
	}
	return modelLabels, nil
}
