package shop_logistics

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_logistics_shop_channels.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ShopLogisticsInfoApi interface {
	BatchGetShopChannelData(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDisplayChannelData, fsserr.Error)
	GetChannels(ctx context.Context) map[uint64]*marketplace_logistics_shop_channels.Channel
}

type ShopLogisticsInfoApiImpl struct {
	spexClient          spexlib.SpexClient
	configGetter        config.ConfAccessor
	shopChannelCache    cache.MultiCache[uint64, entity.ShopDisplayChannelData]
	mpChannelLocalCache localcache.LocalCache[uint64, *marketplace_logistics_shop_channels.Channel]
}

func NewShopLogisticsInfoApiImpl(
	spexClient spexlib.SpexClient,
	configGetter config.ConfAccessor,
	clients redishelper.GlobalRedisClients,
) (*ShopLogisticsInfoApiImpl, error) {
	defaultClient, err := clients.GetRedisClusterByClusterName(redishelper.Default)
	if err != nil {
		return nil, err
	}

	shopChannelCache, err := mixed_cache.NewLruLayerCache[uint64, entity.ShopDisplayChannelData](
		cache.ShopChannelLruName, defaultClient, nil, nil)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	mpChannelLocalCache, err := localcache.GetCache[uint64, *marketplace_logistics_shop_channels.Channel](cache.MPChannels)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	return &ShopLogisticsInfoApiImpl{
		spexClient:          spexClient,
		configGetter:        configGetter,
		shopChannelCache:    shopChannelCache,
		mpChannelLocalCache: mpChannelLocalCache,
	}, nil
}

func (l *ShopLogisticsInfoApiImpl) BatchGetShopChannelData(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDisplayChannelData, fsserr.Error) {
	resp, err := cache.MultiLoadManyFromMap(
		ctx,
		l.shopChannelCache,
		shopIDs,
		l.batchGetShopChannelData,
		cache.WithKeyConvertor[uint64, entity.ShopDisplayChannelData](l.getShopDisplayChannelDataCacheKey),
	)
	if err != nil {
		return nil, fsserr.With(fsserr.NoAvailableChannelErr, err)
	}

	return resp, nil
}

func (l *ShopLogisticsInfoApiImpl) batchGetShopChannelData(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDisplayChannelData, error) {
	caller := concurrency.NewConcurrencySplitCaller[uint64, *marketplace_logistics_shop_channels.ShopDisplayLogisticsChannels]()
	resp, err := caller.Call(ctx, shopIDs, l.configGetter.GetShopEnableChannelsAPIBatchSize(ctx), l.batchGetShopChannels)
	if err != nil {
		return nil, err
	}
	shopIDToData := buildShopIDToChannelData(resp)
	result := make(map[uint64]entity.ShopDisplayChannelData)
	for _, sID := range shopIDs {
		result[sID] = shopIDToData[sID]
	}

	return result, nil
}

func (l *ShopLogisticsInfoApiImpl) batchGetShopChannels(ctx context.Context, shopIDs []uint64) ([]*marketplace_logistics_shop_channels.ShopDisplayLogisticsChannels, error) {
	req := &marketplace_logistics_shop_channels.BatchGetShopChannelsRequest{
		ShopIdList: make([]int64, 0, len(shopIDs)),
	}
	req.ShopIdList = typ.ConvertIntegerSlices[uint64, int64](shopIDs)

	shopLogisticsChannels, err := l.spexClient.BatchGetShopChannels(ctx, req)
	if err != nil {
		return nil, err
	}
	return shopLogisticsChannels, nil
}

func (l *ShopLogisticsInfoApiImpl) getShopDisplayChannelDataCacheKey(ctx context.Context, shopID uint64) string {
	return common.GenKeyWithRegion(ctx, ":", "shop.display.channel.data", strconv.FormatUint(shopID, 10))
}

func (l *ShopLogisticsInfoApiImpl) GetChannels(ctx context.Context) map[uint64]*marketplace_logistics_shop_channels.Channel {
	return l.mpChannelLocalCache.All(ctx)
}

type WarehouseLogisticsInfoApi interface {
	RegionAllWarehouseDisplayChannelData(ctx context.Context, warehouseCodes []string) map[string]entity.WarehouseDisplayChannelData
}

type WarehouseLogisticsInfoApiImpl struct {
	confAccessor               config.ConfAccessor
	warehouseChannelLocalCache localcache.LocalCache[string, entity.WarehouseDisplayChannelData]
}

func NewWarehouseLogisticsInfoApiImpl(
	confAccessor config.ConfAccessor,
) (*WarehouseLogisticsInfoApiImpl, error) {
	warehouseChannelLocalCache, err := localcache.GetCache[string, entity.WarehouseDisplayChannelData](cache.WarehouseChannels)
	if err != nil {
		return nil, err
	}
	return &WarehouseLogisticsInfoApiImpl{
		confAccessor:               confAccessor,
		warehouseChannelLocalCache: warehouseChannelLocalCache,
	}, nil
}

func (w *WarehouseLogisticsInfoApiImpl) RegionAllWarehouseDisplayChannelData(ctx context.Context, warehouseCodes []string) map[string]entity.WarehouseDisplayChannelData {
	return w.warehouseChannelLocalCache.MGet(ctx, warehouseCodes)
}
