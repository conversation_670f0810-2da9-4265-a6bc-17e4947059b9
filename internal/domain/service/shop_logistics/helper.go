package shop_logistics

import (
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_logistics_shop_channels.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

func buildShopIDToChannelData(resp []*marketplace_logistics_shop_channels.ShopDisplayLogisticsChannels) map[uint64]entity.ShopDisplayChannelData {
	result := make(map[uint64]entity.ShopDisplayChannelData)
	for _, shopLogisticsChannelsData := range resp {
		shopID := uint64(shopLogisticsChannelsData.GetShopId())

		uint64EnabledChannelIDs := shopLogisticsChannelsData.GetEnabled()
		enabledChannels := make([]int, len(uint64EnabledChannelIDs))
		for idx, enabledChannelID := range uint64EnabledChannelIDs {
			enabledChannels[idx] = int(enabledChannelID)
		}

		uint64DisabledChannelIDs := shopLogisticsChannelsData.GetDisabled()
		disabledChannels := make([]int, len(uint64DisabledChannelIDs))
		for idx, disabledChannelID := range uint64DisabledChannelIDs {
			disabledChannels[idx] = int(disabledChannelID)
		}

		shopChanelDisplayData := entity.ShopDisplayChannelData{
			RetCode:  int(shopLogisticsChannelsData.GetErrorCode()),
			Enabled:  enabledChannels,
			Disabled: disabledChannels,
		}

		result[shopID] = shopChanelDisplayData
	}

	return result
}

func buildShopChannel(rawChannel *marketplace_logistics_shop_channels.Channel) entity.ShopChannel {
	return entity.ShopChannel{
		ChannelId:                 rawChannel.GetChannelId(),
		NotSupportCounterfeitItem: rawChannel.GetNotSupportCounterfeitItem(),
	}
}
