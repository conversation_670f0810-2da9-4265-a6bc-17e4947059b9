package shop_logistics

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ShopLogisticsService interface {
	GenerateShopIDToEnabledChannelIDs(ctx context.Context, shopIDs []uint64, fulfillmentInfo entity.FulfillmentInfo) (map[uint64][]int, fsserr.Error)
	GetChannelIdToChannel(ctx context.Context) (map[uint64]entity.ShopChannel, fsserr.Error)
}

type ShopLogisticsServiceImpl struct {
	shopLogisticsApi      ShopLogisticsInfoApi
	warehouseLogisticsAPI WarehouseLogisticsInfoApi
}

func NewShopLogisticsServiceImpl(shopLogisticsApi ShopLogisticsInfoApi, warehouseLogisticsAPI WarehouseLogisticsInfoApi) *ShopLogisticsServiceImpl {
	return &ShopLogisticsServiceImpl{shopLogisticsApi: shopLogisticsApi, warehouseLogisticsAPI: warehouseLogisticsAPI}
}

func (s *ShopLogisticsServiceImpl) ShopEnabledChannelIDs(ctx context.Context, shopIDs []uint64) (map[uint64][]int, fsserr.Error) {
	resp, err := s.shopLogisticsApi.BatchGetShopChannelData(ctx, shopIDs)
	if err != nil {
		return nil, err
	}

	result := make(map[uint64][]int)
	for _, sID := range shopIDs {
		result[sID] = resp[sID].Enabled
	}

	return result, nil
}

func (s *ShopLogisticsServiceImpl) GenerateShopIDToEnabledChannelIDs(ctx context.Context, shopIDs []uint64, fulfillmentInfo entity.FulfillmentInfo) (map[uint64][]int, fsserr.Error) {
	shopIDToChannelIDs := make(map[uint64][]int)

	if fulfillmentInfo.ManagedBySBS {
		// the whole shipping order should use warehouse address
		warehouseCode := fulfillmentInfo.WarehouseCode
		warehouseLevelEnabledChannelIDs, err := s.WarehouseEnabledChannelIDs(ctx, []string{warehouseCode})
		if err != nil {
			return nil, err
		}
		if _, ok := warehouseLevelEnabledChannelIDs[warehouseCode]; !ok {
			return nil, fsserr.New(fsserr.NoAvailableChannelErr, "warehouse code not found in enabled channel IDs").WithExtraData(warehouseCode)
		}
		for _, shopID := range shopIDs {
			shopIDToChannelIDs[shopID] = warehouseLevelEnabledChannelIDs[warehouseCode]
		}
	} else {
		var err fsserr.Error
		shopIDToChannelIDs, err = s.ShopEnabledChannelIDs(ctx, shopIDs)
		if err != nil {
			return nil, err
		}
	}
	return shopIDToChannelIDs, nil
}

func (s *ShopLogisticsServiceImpl) WarehouseEnabledChannelIDs(ctx context.Context, warehouseCodes []string) (map[string][]int, fsserr.Error) {
	resp := s.warehouseLogisticsAPI.RegionAllWarehouseDisplayChannelData(ctx, warehouseCodes)

	res := make(map[string][]int)
	for _, wID := range warehouseCodes {
		res[wID] = resp[wID].Enabled
	}

	return res, nil
}

func (s *ShopLogisticsServiceImpl) GetChannelIdToChannel(ctx context.Context) (map[uint64]entity.ShopChannel, fsserr.Error) {
	rawChannels := s.shopLogisticsApi.GetChannels(ctx)
	channelsMap := make(map[uint64]entity.ShopChannel, len(rawChannels))
	for _, rawChannel := range rawChannels {
		channel := buildShopChannel(rawChannel)
		channelsMap[channel.ChannelId] = channel
	}

	return channelsMap, nil
}
