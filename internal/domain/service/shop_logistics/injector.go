package shop_logistics

import "github.com/google/wire"

var ShopLogisticsServiceProviderSet = wire.NewSet(
	NewShopLogisticsServiceImpl,
	wire.Bind(new(ShopLogisticsService), new(*ShopLogisticsServiceImpl)),
)

var ShopLogisticsApiProviderSet = wire.NewSet(
	NewShopLogisticsInfoApiImpl,
	wire.Bind(new(ShopLogisticsInfoApi), new(*ShopLogisticsInfoApiImpl)),
)

var WarehouseLogisticsApiProviderSet = wire.NewSet(
	NewWarehouseLogisticsInfoApiImpl,
	wire.Bind(new(WarehouseLogisticsInfoApi), new(*WarehouseLogisticsInfoApiImpl)),
)
