// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package shop is a generated GoMock package.
package shop

import (
	context "context"
	reflect "reflect"

	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	fsserr "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
	gomock "github.com/golang/mock/gomock"
)

// MockShopService is a mock of ShopService interface.
type MockShopService struct {
	ctrl     *gomock.Controller
	recorder *MockShopServiceMockRecorder
}

// MockShopServiceMockRecorder is the mock recorder for MockShopService.
type MockShopServiceMockRecorder struct {
	mock *MockShopService
}

// NewMockShopService creates a new mock instance.
func NewMockShopService(ctrl *gomock.Controller) *MockShopService {
	mock := &MockShopService{ctrl: ctrl}
	mock.recorder = &MockShopServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockShopService) EXPECT() *MockShopServiceMockRecorder {
	return m.recorder
}

// BatchGetShopDetail mocks base method.
func (m *MockShopService) BatchGetShopDetail(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDetailInfo, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetShopDetail", ctx, shopIDs)
	ret0, _ := ret[0].(map[uint64]entity.ShopDetailInfo)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// BatchGetShopDetail indicates an expected call of BatchGetShopDetail.
func (mr *MockShopServiceMockRecorder) BatchGetShopDetail(ctx, shopIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetShopDetail", reflect.TypeOf((*MockShopService)(nil).BatchGetShopDetail), ctx, shopIDs)
}

// BatchGetShopWarehouseFlag mocks base method.
func (m *MockShopService) BatchGetShopWarehouseFlag(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopWarehouseFlag, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetShopWarehouseFlag", ctx, shopIDs)
	ret0, _ := ret[0].(map[uint64]entity.ShopWarehouseFlag)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// BatchGetShopWarehouseFlag indicates an expected call of BatchGetShopWarehouseFlag.
func (mr *MockShopServiceMockRecorder) BatchGetShopWarehouseFlag(ctx, shopIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetShopWarehouseFlag", reflect.TypeOf((*MockShopService)(nil).BatchGetShopWarehouseFlag), ctx, shopIDs)
}

// BatchGetShopWarehouses mocks base method.
func (m *MockShopService) BatchGetShopWarehouses(ctx context.Context, shopIDs []uint64) (map[uint64][]entity.ShopWarehouse, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetShopWarehouses", ctx, shopIDs)
	ret0, _ := ret[0].(map[uint64][]entity.ShopWarehouse)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// BatchGetShopWarehouses indicates an expected call of BatchGetShopWarehouses.
func (mr *MockShopServiceMockRecorder) BatchGetShopWarehouses(ctx, shopIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetShopWarehouses", reflect.TypeOf((*MockShopService)(nil).BatchGetShopWarehouses), ctx, shopIDs)
}
