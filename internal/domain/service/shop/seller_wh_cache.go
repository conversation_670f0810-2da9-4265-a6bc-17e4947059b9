package shop

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_address_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
)

const (
	batchCheckSellerWarehouseShopBatchSize uint8 = 10
	batchGetWarehouseFlagByShopBatchSize   uint8 = 30
)

// ShopWarehouseGeoKey 店铺仓库地理位置缓存键
type ShopWarehouseGeoKey struct {
	ShopUserId  uint64
	WhAddressId uint64
}

// NewShopWarehouseGeoKey 创建新的店铺仓库地理位置缓存键
func NewShopWarehouseGeoKey(shopUserId, whAddressId uint64) ShopWarehouseGeoKey {
	return ShopWarehouseGeoKey{
		ShopUserId:  shopUserId,
		WhAddressId: whAddressId,
	}
}

type ShopMultiWHCache interface {
	GetShopWarehouseMap(ctx context.Context, shopIds []uint64) (shopWHMap map[uint64][]entity.ShopWarehouse, err error)

	GetShopWarehouseGeoLocation(ctx context.Context, shopUserId uint64, whAddressIds []uint64) (geoLocationMap map[uint64]entity.GeoLocation, err error)

	// GetShopUserIdMap returns the seller's user_id of each given shop
	GetShopIDToSellerUserId(ctx context.Context, shopIds []uint64) (map[uint64]uint64, error)

	// GetShopIdToIsMultiWHMap returns whether each given shop is whitelisted for seller
	// multi-wh feature or not.
	// This will call a new API which is not battle-tested so use this sparingly for safety.
	GetShopIdToIsMultiWHMap(ctx context.Context, shopIds []uint64) map[uint64]bool

	GetShopIdToWhitelistDetailMap(ctx context.Context, shopIds []uint64) (map[uint64]entity.ShopWhitelistDetail, error)

	GetShopIdToWarehouseListMap(ctx context.Context, shopIds []uint64) (map[uint64][]entity.ShopWarehouse, error)
}

type ShopMultiWHCacheImpl struct {
	confAccessor           config.ConfAccessor
	addrService            address.AddrService
	spexClient             spexlib.SpexClient
	shopWHMapCache         cache.MultiCache[uint64, []entity.ShopWarehouse]
	shopWhiteListCache     cache.MultiCache[uint64, entity.ShopWhitelistDetail]
	shopIdToIsMultiWHCache cache.MultiCache[uint64, []entity.ShopWarehouse]
	shopWarehouseListCache cache.MultiCache[uint64, []entity.ShopWarehouse]
	shopWarehouseGeoCache  cache.MultiCache[ShopWarehouseGeoKey, entity.GeoLocation]
	shopSellerUserIdCache  cache.MultiCache[uint64, uint64]
	shopMultiWHFlagCache   cache.MultiCache[uint64, bool]
}

func NewShopMultiWHCacheImplWithClient(
	confAccessor config.ConfAccessor,
	clients redishelper.GlobalRedisClients,
	addrService address.AddrService,
	spexClient spexlib.SpexClient,
) (*ShopMultiWHCacheImpl, error) {
	defaultClient, err := clients.GetRedisClusterByClusterName(redishelper.Default)
	if err != nil {
		return nil, err
	}

	shopWHMapCache, err := mixed_cache.NewLruLayerCache[uint64, []entity.ShopWarehouse](
		cache.ShopWhPriorityCacheName, defaultClient, nil, nil)
	if err != nil {
		return nil, err
	}

	shopWhiteListCache, err := mixed_cache.NewLruLayerCache[uint64, entity.ShopWhitelistDetail](
		cache.ShopWhitelistDetailCacheName, defaultClient, nil, nil)
	if err != nil {
		return nil, err
	}

	shopIdToIsMultiWHCache, err := mixed_cache.NewLruLayerCache[uint64, []entity.ShopWarehouse](
		cache.ShopWhPriorityCacheName, defaultClient, nil, nil)
	if err != nil {
		return nil, err
	}

	shopWarehouseListCache, err := mixed_cache.NewLruLayerCache[uint64, []entity.ShopWarehouse](
		cache.ShopWarehouseListCacheName, defaultClient, nil, nil)
	if err != nil {
		return nil, err
	}

	shopWarehouseGeoCache, err := mixed_cache.NewLruLayerCache[ShopWarehouseGeoKey, entity.GeoLocation](
		cache.ShopWarehouseGeoCacheName, defaultClient, nil, nil)
	if err != nil {
		return nil, err
	}

	shopSellerUserIdCache, err := mixed_cache.NewNumberLruLayerCache[uint64, uint64](
		cache.ShopSellerUserIdCacheName, defaultClient, nil, nil)
	if err != nil {
		return nil, err
	}
	shopMultiWHFlagCache, err := mixed_cache.NewBoolLruLayerCache[uint64, bool](
		cache.ShopMultiWHFlagCacheName, defaultClient, nil, nil)
	if err != nil {
		return nil, err
	}

	return NewShopMultiWHCacheImpl(
		confAccessor,
		addrService,
		spexClient,
		shopWHMapCache,
		shopWhiteListCache,
		shopIdToIsMultiWHCache,
		shopWarehouseListCache,
		shopWarehouseGeoCache,
		shopSellerUserIdCache,
		shopMultiWHFlagCache,
	), nil
}

func NewShopMultiWHCacheImpl(
	confAccessor config.ConfAccessor,
	addrService address.AddrService,
	spexClient spexlib.SpexClient,
	shopWHMapCache cache.MultiCache[uint64, []entity.ShopWarehouse],
	shopWhiteListCache cache.MultiCache[uint64, entity.ShopWhitelistDetail],
	shopIdToIsMultiWHCache cache.MultiCache[uint64, []entity.ShopWarehouse], // Optional, can be nil if not needed
	shopWarehouseListCache cache.MultiCache[uint64, []entity.ShopWarehouse], // Optional, can be nil if not needed
	shopWarehouseGeoCache cache.MultiCache[ShopWarehouseGeoKey, entity.GeoLocation], // Optional, can be nil if not needed
	shopSellerUserIdCache cache.MultiCache[uint64, uint64], // Optional, can be nil if not needed
	shopMultiWHFlagCache cache.MultiCache[uint64, bool], // Optional, can be nil if not needed
) *ShopMultiWHCacheImpl {
	return &ShopMultiWHCacheImpl{
		confAccessor:           confAccessor,
		addrService:            addrService,
		spexClient:             spexClient,
		shopWHMapCache:         shopWHMapCache,
		shopWhiteListCache:     shopWhiteListCache,
		shopIdToIsMultiWHCache: shopIdToIsMultiWHCache,
		shopWarehouseListCache: shopWarehouseListCache,
		shopWarehouseGeoCache:  shopWarehouseGeoCache,
		shopSellerUserIdCache:  shopSellerUserIdCache,
		shopMultiWHFlagCache:   shopMultiWHFlagCache,
	}
}

func (s *ShopMultiWHCacheImpl) GetShopWarehouseMap(ctx context.Context, shopIds []uint64) (shopWHMap map[uint64][]entity.ShopWarehouse, err error) {
	cacheRet, err := cache.MultiLoadManyFromMap(ctx,
		s.shopWHMapCache,
		shopIds,
		s.getShopWHMapFromSource,
		cache.WithKeyConvertor[uint64, []entity.ShopWarehouse](shopWHMapCacheKey),
	)
	if err != nil {
		return nil, err
	}
	return cacheRet, nil
}

func (s *ShopMultiWHCacheImpl) GetShopWarehouseGeoLocation(ctx context.Context, shopUserId uint64, whAddressIds []uint64) (geoLocationMap map[uint64]entity.GeoLocation, err error) {
	if len(whAddressIds) == 0 {
		return make(map[uint64]entity.GeoLocation), nil
	}

	// 构建缓存键列表
	cacheKeys := make([]ShopWarehouseGeoKey, 0, len(whAddressIds))
	for _, whAddressId := range whAddressIds {
		cacheKeys = append(cacheKeys, NewShopWarehouseGeoKey(shopUserId, whAddressId))
	}

	// 使用缓存机制获取仓库地理位置信息
	cacheRet, err := cache.MultiLoadManyFromMap(ctx,
		s.shopWarehouseGeoCache,
		cacheKeys,
		func(ctx context.Context, missedKeys []ShopWarehouseGeoKey) (map[ShopWarehouseGeoKey]entity.GeoLocation, error) {
			// 提取缺失的地址ID
			missedWhAddressIds := make([]uint64, 0, len(missedKeys))
			for _, key := range missedKeys {
				missedWhAddressIds = append(missedWhAddressIds, key.WhAddressId)
			}

			// 调用数据源获取缺失的地理位置信息
			geoLocationMap, err := s.addrService.BatchGetShopWarehouseGeo(ctx, typ.UserIdType(shopUserId), missedWhAddressIds)
			if err != nil {
				return nil, err
			}

			// 将结果转换为以ShopWarehouseGeoKey为键的map
			result := make(map[ShopWarehouseGeoKey]entity.GeoLocation, len(geoLocationMap))
			for _, key := range missedKeys {
				if geoLocation, exists := geoLocationMap[key.WhAddressId]; exists {
					result[key] = geoLocation
				}
			}
			return result, nil
		},
		cache.WithKeyConvertor[ShopWarehouseGeoKey, entity.GeoLocation](shopWarehouseGeoCacheKey),
	)
	if err != nil {
		return nil, err
	}

	// 将结果转换为以whAddressId为键的map
	geoLocationMap = make(map[uint64]entity.GeoLocation, len(cacheRet))
	for key, geoLocation := range cacheRet {
		geoLocationMap[key.WhAddressId] = geoLocation
	}
	return geoLocationMap, nil
}

func (s *ShopMultiWHCacheImpl) GetShopIDToSellerUserId(ctx context.Context, shopIds []uint64) (map[uint64]uint64, error) {
	if len(shopIds) == 0 {
		return make(map[uint64]uint64), nil
	}

	// 使用缓存机制获取店铺ID到卖家用户ID的映射
	cacheRet, err := cache.MultiLoadManyFromMap(ctx,
		s.shopSellerUserIdCache,
		shopIds,
		s.getShopIDToSellerUserIdFromSource,
		cache.WithKeyConvertor[uint64, uint64](shopSellerUserIdCacheKey),
	)
	if err != nil {
		return nil, err
	}
	return cacheRet, nil
}

func (s *ShopMultiWHCacheImpl) GetShopIdToIsMultiWHMap(ctx context.Context, shopIds []uint64) map[uint64]bool {
	if len(shopIds) == 0 {
		return make(map[uint64]bool)
	}

	// 使用缓存机制获取店铺多重仓库标志
	cacheRet, err := cache.MultiLoadManyFromMap(ctx,
		s.shopMultiWHFlagCache,
		shopIds,
		s.getShopIdToIsMultiWHMapFromSource,
		cache.WithKeyConvertor[uint64, bool](shopMultiWHFlagCacheKey),
	)

	if err != nil {
		// 如果缓存调用失败，返回默认值（所有店铺都不是多重仓库）
		Logger.CtxLogErrorf(ctx, "get shop multi WH flag from cache failed, err: %v", err)
		shopIdToIsMultiWH := make(map[uint64]bool, len(shopIds))
		for _, shopId := range shopIds {
			shopIdToIsMultiWH[shopId] = false
		}
		return shopIdToIsMultiWH
	}
	return cacheRet
}

func (s *ShopMultiWHCacheImpl) GetShopIdToWhitelistDetailMap(ctx context.Context, shopIds []uint64) (map[uint64]entity.ShopWhitelistDetail, error) {
	shopIdToWhitelistDetailFromCache, err := cache.MultiLoadManyFromMap(ctx,
		s.shopWhiteListCache,
		shopIds,
		s.getShopWhitelistDetailFromSource,
		cache.WithKeyConvertor[uint64, entity.ShopWhitelistDetail](shopWhitelistMapCacheKey),
	)
	if err != nil {
		return nil, err
	}
	for _, shopId := range shopIds {
		if _, ok := shopIdToWhitelistDetailFromCache[shopId]; !ok {
			shopIdToWhitelistDetailFromCache[shopId] = entity.NewEmptyShopWhitelistDetail(shopId)
		}
	}
	return shopIdToWhitelistDetailFromCache, nil
}

func (s *ShopMultiWHCacheImpl) GetShopIdToWarehouseListMap(ctx context.Context, shopIds []uint64) (map[uint64][]entity.ShopWarehouse, error) {
	cacheRet, err := cache.MultiLoadManyFromMap(ctx,
		s.shopWarehouseListCache,
		shopIds,
		s.getShopIdToWarehouseListMapFromSource,
		cache.WithKeyConvertor[uint64, []entity.ShopWarehouse](shopWHMapCacheKey),
	)
	if err != nil {
		return nil, err
	}
	return cacheRet, nil
}

func (s *ShopMultiWHCacheImpl) getShopWHMapFromSource(ctx context.Context, shopIds []uint64) (
	shopWHMap map[uint64][]entity.ShopWarehouse,
	err error) {
	shopWHMap = make(map[uint64][]entity.ShopWarehouse, len(shopIds))
	err = common.ForEachBatch(shopIds, batchCheckSellerWarehouseShopBatchSize, func(batch []uint64) error {
		requestShopIds := make([]int64, 0, len(batch))
		for _, shopId := range batch {
			requestShopIds = append(requestShopIds, int64(shopId))
		}
		warehouses, err := s.spexClient.BatchGetShopWarehousesByShopIds(ctx, requestShopIds)
		if err != nil {
			return err
		}

		for _, whInfo := range warehouses {
			shopId := uint64(whInfo.GetShopId())
			shopWH := entity.ShopWarehouse{
				ShopID:     shopId,
				LocationID: whInfo.GetLocationId(),
				AddressID:  uint64(whInfo.GetAddressId()),
			}
			shopWHMap[shopId] = append(shopWHMap[shopId], shopWH)
		}

		return nil
	})
	return shopWHMap, nil
}

func (s *ShopMultiWHCacheImpl) getShopWhitelistDetailFromSource(ctx context.Context, shopIds []uint64) (
	shopIdToWhitelistDetail map[uint64]entity.ShopWhitelistDetail, err error) {

	shopIdToWhitelistDetail = make(map[uint64]entity.ShopWhitelistDetail, len(shopIds))
	err = common.ForEachBatch(
		shopIds, batchGetWarehouseFlagByShopBatchSize, func(batch []uint64) error {
			requestShopIds := make([]int64, 0, len(batch))
			for _, shopId := range batch {
				requestShopIds = append(requestShopIds, int64(shopId))
			}

			warehouseFlagList, err := s.spexClient.BatchGetWarehouseFlagByShop(ctx, requestShopIds)
			if err != nil {
				return err
			}

			for _, entry := range warehouseFlagList {
				shopId := uint64(entry.GetShopId())
				shopIdToWhitelistDetail[shopId] = entity.NewShopWhitelistDetail(
					shopId,
					seller_seller_address_core.Constant_ShopWarehouseFlag(entry.GetWarehouseFlag()),
					entry.GetCanUseWarehouseForPickup(),
				)
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}
	return shopIdToWhitelistDetail, nil
}

func (s *ShopMultiWHCacheImpl) getShopIdToWarehouseListMapFromSource(ctx context.Context, shopIds []uint64) (
	shopIdToWarehouseList map[uint64][]entity.ShopWarehouse,
	err error) {
	shopIdToWarehouseList = make(map[uint64][]entity.ShopWarehouse, len(shopIds))
	err = common.ForEachBatch(shopIds, batchCheckSellerWarehouseShopBatchSize, func(batch []uint64) error {
		requestShopIds := make([]int64, 0, len(batch))
		for _, shopId := range batch {
			requestShopIds = append(requestShopIds, int64(shopId))
		}
		warehouseList, err := s.spexClient.BatchGetWarehouseByShopWithoutPagination(ctx, requestShopIds)
		if err != nil {
			return err
		}

		for _, shopWarehouse := range warehouseList {
			if shopWarehouse == nil {
				continue
			}
			shopID := uint64(shopWarehouse.GetShopId())
			warehouse := entity.ShopWarehouse{
				ShopID:     shopID,
				LocationID: shopWarehouse.GetWarehouse().GetLocationId(),
				AddressID:  uint64(shopWarehouse.GetWarehouse().GetAddressId()),
				Region:     shopWarehouse.GetWarehouse().GetRegion(),
			}
			shopIdToWarehouseList[shopID] = append(shopIdToWarehouseList[shopID], warehouse)
		}

		return nil
	})
	return shopIdToWarehouseList, nil
}

func (s *ShopMultiWHCacheImpl) getShopIDToSellerUserIdFromSource(ctx context.Context, shopIds []uint64) (map[uint64]uint64, error) {
	// 将 uint64 转换为 int64
	shopIDList := make([]int64, 0, len(shopIds))
	for _, shopID := range shopIds {
		shopIDList = append(shopIDList, int64(shopID))
	}

	// 调用 Spex API 获取店铺详情
	shopDetailList, err := s.spexClient.GetShopBatch(ctx, shopIDList)
	if err != nil {
		return nil, err
	}

	// 构建 shopId 到 sellerUserId 的映射
	shopIdToSellerUserId := make(map[uint64]uint64, len(shopIds))
	for _, shop := range shopDetailList {
		if shop == nil {
			continue
		}
		shopID := uint64(shop.GetShopid())
		shopIdToSellerUserId[shopID] = uint64(shop.GetUserid())
	}

	return shopIdToSellerUserId, nil
}

func (s *ShopMultiWHCacheImpl) getShopIdToIsMultiWHMapFromSource(ctx context.Context, shopIds []uint64) (map[uint64]bool, error) {
	// 将 uint64 转换为 int64
	shopIDList := make([]int64, 0, len(shopIds))
	for _, shopID := range shopIds {
		shopIDList = append(shopIDList, int64(shopID))
	}

	// 调用 Spex API 获取仓库标志信息
	warehouseFlagList, err := s.spexClient.BatchGetWarehouseFlagByShop(ctx, shopIDList)
	if err != nil {
		// 如果API调用失败，返回默认值（所有店铺都不是多重仓库）
		Logger.CtxLogErrorf(ctx, "get warehouse flag by shop failed, err: %v", err)
		shopIdToIsMultiWH := make(map[uint64]bool, len(shopIds))
		for _, shopId := range shopIds {
			shopIdToIsMultiWH[shopId] = false
		}
		return shopIdToIsMultiWH, nil
	}

	// 构建 shopId 到 isMultiWH 的映射
	shopIdToIsMultiWH := make(map[uint64]bool, len(shopIds))
	for _, shopFlag := range warehouseFlagList {
		if shopFlag == nil {
			continue
		}
		shopID := uint64(shopFlag.GetShopId())
		// 检查是否为多重仓库标志
		isMultiWH := shopFlag.GetWarehouseFlag() == int32(seller_seller_address_core.Constant_SHOP_WAREHOUSE_LOCAL_DEFAULT)
		shopIdToIsMultiWH[shopID] = isMultiWH
	}

	// 为没有返回结果的店铺设置默认值
	for _, shopId := range shopIds {
		if _, exists := shopIdToIsMultiWH[shopId]; !exists {
			shopIdToIsMultiWH[shopId] = false
		}
	}

	return shopIdToIsMultiWH, nil
}

func shopWHMapCacheKey(ctx context.Context, shopId uint64) string {
	return common.GenKeyWithRegion(ctx, ":", "shop_wh", strconv.FormatUint(shopId, 10))
}

func shopWhitelistMapCacheKey(ctx context.Context, shopId uint64) string {
	return common.GenKeyWithRegion(ctx, ":", "shop_whitelist", strconv.FormatUint(shopId, 10))
}

func shopWarehouseGeoCacheKey(ctx context.Context, key ShopWarehouseGeoKey) string {
	// 生成包含shopUserId和单个whAddressId的缓存键
	return common.GenKeyWithRegion(ctx, ":", "warehouse_geo",
		strconv.FormatUint(key.ShopUserId, 10), strconv.FormatUint(key.WhAddressId, 10))
}

func shopSellerUserIdCacheKey(ctx context.Context, shopId uint64) string {
	return common.GenKeyWithRegion(ctx, ":", "seller_user", strconv.FormatUint(shopId, 10))
}

func shopMultiWHFlagCacheKey(ctx context.Context, shopId uint64) string {
	return common.GenKeyWithRegion(ctx, ":", "shop_multi_wh_flag", strconv.FormatUint(shopId, 10))
}
