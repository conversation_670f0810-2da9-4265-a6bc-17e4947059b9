package item_stock

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/price_checkout_promo.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/checkout_promo"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

// GetStock This method returns the stock info from Checkout Promo API.
// In case all given items are deducted items, no stock data
// will be returned
func (i *StockServiceImpl) GetStock(ctx context.Context, orders []entity.ItemGroupShippingOrder, buyerId typ.UserIdType, isGroupBuy bool) (
	[]*entity.OrderStockInfo, bool, error) {

	request, allItemsDeducted := i.getCheckoutPromoStockRequest(ctx, buyerId, orders, isGroupBuy)
	if allItemsDeducted {
		return nil, allItemsDeducted, nil
	}

	response, err := i.checkoutPromoApi.GetCheckoutOrderStock(ctx, request)
	if err != nil {
		return nil, false, fmt.Errorf("get stock from checkout promo api: %w", err)
	}

	orderStockInfos := make([]*entity.OrderStockInfo, 0, len(orders))
	for orderIdx, responseOrder := range response.OrderStocks {
		// ensure sorting order is the same as in request
		sortedItemStocks, err := sortResponseItemStocks(
			ctx,
			orders[orderIdx].Items,
			responseOrder.ItemStocks,
		)
		if err != nil {
			return nil, false, fmt.Errorf("get stock from checkout promo api: %w", err)
		}
		responseOrder.ItemStocks = sortedItemStocks

		orderItemStockInfos := make([]*entity.OrderItemStockInfo, 0)

		for _, responseItem := range responseOrder.ItemStocks {
			hasOldStockStructure := len(responseItem.StockBreakdownByLocation) == 0
			if hasOldStockStructure {
				orderItemStockInfos = append(
					orderItemStockInfos,
					entity.NewOrderItemStockInfoForOldStructure(
						responseItem.ItemId,
						responseItem.ModelId,
						responseItem.TotalStock,
						responseItem.OrderItemType,
						responseItem.BundleDealId,
						false,
					),
				)
				continue
			}

			stockLocations := make([]*entity.StockLocation, 0)
			for _, location := range responseItem.StockBreakdownByLocation {
				val := &entity.StockLocation{
					LocationID:      location.LocationId,
					AvailableStock:  location.AvailableStock,
					FulfillmentType: location.FulfillmentType,
				}
				if val.FulfillmentType.IsCacheStockFulfilmentType() {
					val.SOCStationID = val.LocationID
				}
				stockLocations = append(stockLocations, val)
			}
			orderItemStockInfos = append(
				orderItemStockInfos, entity.NewOrderItemStockInfo(
					responseItem.ItemId,
					responseItem.ModelId,
					responseItem.TotalStock,
					stockLocations,
					responseItem.OrderItemType,
					responseItem.BundleDealId,
					false,
				),
			)
		}

		orderStockInfo := &entity.OrderStockInfo{Items: orderItemStockInfos}
		orderStockInfos = append(orderStockInfos, orderStockInfo)
	}

	return orderStockInfos, false, nil
}

func (i *StockServiceImpl) getCheckoutPromoStockRequest(
	ctx context.Context,
	buyerId typ.UserIdType,
	orders []entity.ItemGroupShippingOrder,
	isGroupBuy bool,
) (*checkout_promo.GetCheckoutOrderStockRequest, bool) {
	allItemsDeducted := true
	for _, order := range orders {
		for _, item := range order.Items {
			if !item.IsDeductedItem() {
				allItemsDeducted = false
			}
		}
	}

	if allItemsDeducted {
		return nil, true
	}
	request := checkout_promo.NewGetCheckoutOrderStockRequest(buyerId, orders, isGroupBuy)
	return request, false
}

func (i *StockServiceImpl) getRequestCheckoutOrders(buyerUserId uint64, orders []entity.ItemGroupShippingOrder, isGroupBuy bool) []*price_checkout_promo.CheckoutOrder {
	checkoutOrders := make([]*price_checkout_promo.CheckoutOrder, 0, len(orders))
	for _, order := range orders {
		addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap := i.getAddOnDealMaps(order)

		checkoutModels := make([]*price_checkout_promo.CheckoutOrderModel, 0, len(order.Items))
		for _, item := range order.Items {
			chatOfferInfo := i.getChatOfferInfo(item)
			membershipOfferInfo := i.getMembershipOfferInfo(item)
			addOnDeal := i.getAddOnDeal(item, addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap)
			promotionInfo := i.getPromotionInfo(item)

			checkoutModel := &price_checkout_promo.CheckoutOrderModel{
				ShopId:                proto.Int64(int64(order.ShopID)),
				ItemId:                proto.Uint64(uint64(item.ItemId)),
				ModelId:               proto.Uint64(uint64(item.ModelId)),
				Amount:                proto.Int32(int32(item.Quantity)),
				ChatOfferInfo:         chatOfferInfo,
				MembershipOfferInfo:   membershipOfferInfo,
				AddOnDeal:             addOnDeal,
				AssignedPromotionInfo: promotionInfo,
			}
			checkoutModels = append(checkoutModels, checkoutModel)
		}

		checkoutOrder := &price_checkout_promo.CheckoutOrder{
			IsGroupBuy:     proto.Bool(isGroupBuy),
			CheckoutModels: checkoutModels,
		}
		checkoutOrders = append(checkoutOrders, checkoutOrder)
	}
	return checkoutOrders
}

// Sorts the item stocks from `responseOrder` such that it appears in the same sorting order
// as the items in `requestOrder`
func sortResponseItemStocks(
	ctx context.Context,
	requestItems []entity.ItemGroupShippingOrderItem,
	responseItemStocks []*checkout_promo.CheckoutOrderItemStock,
) (sorted []*checkout_promo.CheckoutOrderItemStock, err error) {
	getSku := func(itemId typ.ItemIdType, modelId typ.ModelIdType) string {
		return fmt.Sprintf("%d_%d", itemId, modelId)
	}

	// it is possible to have 2 entries with same item_id + model_id
	// so we need to use slice here
	skuToItemStockMap := make(map[string][]*checkout_promo.CheckoutOrderItemStock)
	for _, itemStock := range responseItemStocks {
		sku := getSku(itemStock.ItemId, itemStock.ModelId)
		skuToItemStockMap[sku] = append(skuToItemStockMap[sku], itemStock)
	}

	sortedItemStocks := make([]*checkout_promo.CheckoutOrderItemStock, 0, len(responseItemStocks))
	for _, item := range requestItems {
		sku := getSku(item.ItemId, item.ModelId)
		itemStocks, ok := skuToItemStockMap[sku]
		if !ok || len(itemStocks) == 0 {
			err = fmt.Errorf("cannot find stock: sort order item stocks")
			Logger.CtxLogErrorf(ctx, "cannot find stock: sort order item stocks, item_id: %d, model_id: %d, err: %v", item.ItemId, item.ModelId, err)
			return nil, err
		}

		itemStock := itemStocks[0]
		skuToItemStockMap[sku] = itemStocks[1:]

		sortedItemStocks = append(sortedItemStocks, itemStock)
	}

	return sortedItemStocks, nil
}
