package item_stock

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/price_checkout_promo.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/checkout_promo"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// StockService defines the interface for stock-related operations.
// 不太确定为什么 GetCheckoutStockInfo 与 GetStock 需要两套实现，但保持与 MP 一致
type StockService interface {
	GetCheckoutStockInfo(ctx context.Context, shopOrders []entity.ItemGroupShippingOrder, buyerID typ.UserIdType, isGroupBuy bool) ([]*price_checkout_promo.CheckoutOrderStockInfo, fsserr.Error)
	GetStock(ctx context.Context, shopOrders []entity.ItemGroupShippingOrder, buyerId typ.UserIdType, isGroupBuy bool) ([]*entity.OrderStockInfo, bool, error)
}

type StockServiceImpl struct {
	spexClient       spexlib.SpexClient
	checkoutPromoApi checkout_promo.CheckoutPromoApi
}

func NewStockServiceImpl(
	spexClient spexlib.SpexClient,
	checkoutPromoApi checkout_promo.CheckoutPromoApi,
) *StockServiceImpl {
	return &StockServiceImpl{
		spexClient:       spexClient,
		checkoutPromoApi: checkoutPromoApi,
	}
}

func (i *StockServiceImpl) GetCheckoutStockInfo(ctx context.Context, shopOrders []entity.ItemGroupShippingOrder, buyerID typ.UserIdType, isGroupBuy bool) ([]*price_checkout_promo.CheckoutOrderStockInfo, fsserr.Error) {
	checkoutOrders := i.buildRequestOrders(shopOrders, isGroupBuy) // 传递 isGroupBuy 参数

	spexRequest := &price_checkout_promo.GetCheckoutStockInfoRequest{
		Region:         proto.String(envvar.GetCID(ctx)),
		UserId:         proto.Uint64(uint64(buyerID)),
		CheckoutOrders: checkoutOrders,
	}

	return i.spexClient.GetCheckoutStockInfo(ctx, spexRequest)
}

func (i *StockServiceImpl) buildRequestOrders(shopOrders []entity.ItemGroupShippingOrder, isGroupBuy bool) []*price_checkout_promo.CheckoutOrder {
	checkoutOrders := make([]*price_checkout_promo.CheckoutOrder, 0, len(shopOrders))
	for _, order := range shopOrders {
		addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap := i.getAddOnDealMaps(order)

		checkoutModels := make([]*price_checkout_promo.CheckoutOrderModel, 0, len(order.Items))
		for _, item := range order.Items {
			chatOfferInfo := i.getChatOfferInfo(item)
			membershipOfferInfo := i.getMembershipOfferInfo(item)
			addOnDeal := i.getAddOnDeal(item, addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap)
			promotionInfo := i.getPromotionInfo(item)

			checkoutModel := &price_checkout_promo.CheckoutOrderModel{
				ShopId:                proto.Int64(int64(order.ShopID)),
				ItemId:                proto.Uint64(uint64(item.ItemId)),
				ModelId:               proto.Uint64(uint64(item.ModelId)),
				Amount:                proto.Int32(int32(item.Quantity)),
				ChatOfferInfo:         chatOfferInfo,
				MembershipOfferInfo:   membershipOfferInfo,
				AddOnDeal:             addOnDeal,
				AssignedPromotionInfo: promotionInfo,
			}
			checkoutModels = append(checkoutModels, checkoutModel)
		}

		checkoutOrder := &price_checkout_promo.CheckoutOrder{
			IsGroupBuy:     proto.Bool(isGroupBuy), // 参考 fulfillment planning 的实现，IsGroupBuy 设置在 CheckoutOrder 级别
			CheckoutModels: checkoutModels,
		}
		checkoutOrders = append(checkoutOrders, checkoutOrder)
	}
	return checkoutOrders
}

func (i *StockServiceImpl) getAddOnDealMaps(order entity.ItemGroupShippingOrder) (
	map[string][]entity.ItemGroupShippingOrderItem,
	map[string]bool,
) {
	addOnDealKeyToItemsMap := make(map[string][]entity.ItemGroupShippingOrderItem)
	addOnDealKeyToHasSubItemMap := make(map[string]bool)
	for _, item := range order.Items {
		addOnDealInfo := item.AddOnDealInfo
		if addOnDealInfo.AddOnDealID == 0 {
			continue
		}
		addOnDealKey := checkout_promo.AodItemKey(addOnDealInfo.GroupID, addOnDealInfo.AddOnDealID)
		SetAddOnDealKeyToItem(addOnDealKeyToItemsMap, addOnDealKey, item)
		if addOnDealInfo.IsAddOnSubItem {
			addOnDealKeyToHasSubItemMap[addOnDealKey] = true
		}
	}
	return addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap
}

func (i *StockServiceImpl) getAddOnDeal(
	item entity.ItemGroupShippingOrderItem,
	addOnDealKeyToItemsMap map[string][]entity.ItemGroupShippingOrderItem,
	addOnDealKeyToHasSubItemMap map[string]bool,
) *price_checkout_promo.AddOnDeal {
	var addOnDeal *price_checkout_promo.AddOnDeal
	if item.AddOnDealInfo.AddOnDealID != 0 {
		addOnDealInfo := item.AddOnDealInfo

		addOnDealKey := checkout_promo.AodItemKey(addOnDealInfo.GroupID, addOnDealInfo.AddOnDealID)
		aodItemList := addOnDealKeyToItemsMap[addOnDealKey]
		hasSubItem, ok := addOnDealKeyToHasSubItemMap[addOnDealKey]

		// only if AOD/PWG have more than 1 items can form the package promotion
		if len(aodItemList) > 1 && (ok && hasSubItem) {
			addOnDeal = &price_checkout_promo.AddOnDeal{
				AddOnDealGroupId: proto.Uint64(addOnDealInfo.GroupID),
				AddOnDealId:      proto.Uint64(addOnDealInfo.AddOnDealID),
				IsAddOnDealSub:   proto.Bool(addOnDealInfo.IsAddOnSubItem),
			}
		}
	}
	return addOnDeal
}

func (i *StockServiceImpl) getChatOfferInfo(item entity.ItemGroupShippingOrderItem) *price_checkout_promo.ChatOfferInfo {
	var chatOfferInfo *price_checkout_promo.ChatOfferInfo
	if item.OfferInfo.OfferID != 0 {
		chatOfferInfo = &price_checkout_promo.ChatOfferInfo{
			ChatOfferId: proto.Uint64(item.OfferInfo.OfferID),
		}
	}
	return chatOfferInfo
}

func (i *StockServiceImpl) getMembershipOfferInfo(item entity.ItemGroupShippingOrderItem) *price_checkout_promo.MembershipOfferInfo {
	var membershipOfferInfo *price_checkout_promo.MembershipOfferInfo
	if item.MembershipOfferInfo.OfferID != 0 {
		membershipOfferInfo = &price_checkout_promo.MembershipOfferInfo{
			MembershipOfferId: proto.Uint64(item.MembershipOfferInfo.OfferID),
		}
	}
	return membershipOfferInfo
}

func (i *StockServiceImpl) getPromotionInfo(item entity.ItemGroupShippingOrderItem) *price_checkout_promo.AssignedPromotionInfo {
	var promotionInfo *price_checkout_promo.AssignedPromotionInfo
	if item.LocalSipMtskuInfo != nil {
		if !item.LocalSipMtskuInfo.HasDeduct {
			promotionInfo = &price_checkout_promo.AssignedPromotionInfo{
				RuleType: proto.Uint32(item.LocalSipMtskuInfo.PPromotionType),
				RuleId:   proto.Uint64(item.LocalSipMtskuInfo.PPromotionId),
			}
		}
	}
	return promotionInfo
}

func SetAddOnDealKeyToItem(itemGroupMap map[string][]entity.ItemGroupShippingOrderItem, key string, value entity.ItemGroupShippingOrderItem) {
	valueList, ok := itemGroupMap[key]
	if !ok {
		itemGroupMap[key] = []entity.ItemGroupShippingOrderItem{value}
	} else {
		valueList = append(valueList, value)
		itemGroupMap[key] = valueList
	}
}
