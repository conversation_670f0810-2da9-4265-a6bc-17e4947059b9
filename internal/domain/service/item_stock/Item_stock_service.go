package item_stock

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/price_checkout_promo.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

type ItemStockService interface {
	// GetShopOrderStocks returns the stock information of the items in the shop orders.
	// isGroupBuy: 是否拼团订单，参考 fulfillment planning 的实现
	GetShopOrderStocks(ctx context.Context, shopOrders []entity.ItemGroupShippingOrder, buyerID typ.UserIdType, isGroupBuy bool) (map[uint64]entity.ShopOrderStockInfo, error)
}

type ItemStockServiceImpl struct {
	stockService StockService
}

func NewItemStockServiceImpl(stockService StockService) *ItemStockServiceImpl {
	return &ItemStockServiceImpl{
		stockService: stockService,
	}
}

func (s *ItemStockServiceImpl) GetShopOrderStocks(
	ctx context.Context,
	shopOrders []entity.ItemGroupShippingOrder,
	buyerID typ.UserIdType,
	isGroupBuy bool,
) (map[uint64]entity.ShopOrderStockInfo, error) {
	// stock releatd information
	deductedOrders, normalOrders := s.splitWithDeducted(shopOrders)

	var allShopOrderStockInfos []entity.ShopOrderStockInfo
	// The request orders from Local SIP should already deducted the stock
	if len(deductedOrders) > 0 {
		shopOrderStockInfos, err := s.getDeductedOrderStockInfo(deductedOrders)
		if err != nil {
			return nil, err
		}
		allShopOrderStockInfos = append(allShopOrderStockInfos, shopOrderStockInfos...)
	}
	if len(normalOrders) > 0 {
		shopOrderStockInfos, err := s.getNormalOrderStockInfo(ctx, normalOrders, buyerID, isGroupBuy)
		if err != nil {
			return nil, err
		}
		allShopOrderStockInfos = append(allShopOrderStockInfos, shopOrderStockInfos...)
	}

	// merge 2 types of stock info
	shopIDToOrderStockInfos := make(map[uint64]entity.ShopOrderStockInfo)
	for _, stockInfo := range allShopOrderStockInfos {
		if _, ok := shopIDToOrderStockInfos[stockInfo.ShopID]; !ok {
			shopIDToOrderStockInfos[stockInfo.ShopID] = stockInfo
		} else {
			itemStocks := append(shopIDToOrderStockInfos[stockInfo.ShopID].ItemStocks, stockInfo.ItemStocks...)
			stockInfo.ItemStocks = itemStocks
			shopIDToOrderStockInfos[stockInfo.ShopID] = stockInfo
		}
	}

	return shopIDToOrderStockInfos, nil
}

func (s *ItemStockServiceImpl) splitWithDeducted(shopOrders []entity.ItemGroupShippingOrder) (
	deductedOrders []entity.ItemGroupShippingOrder,
	normalOrders []entity.ItemGroupShippingOrder,
) {
	for _, order := range shopOrders {
		var normalItems, deductedItems []entity.ItemGroupShippingOrderItem
		for _, item := range order.Items {
			if item.IsDeductedItem() {
				deductedItems = append(deductedItems, item)
			} else {
				normalItems = append(normalItems, item)
			}
		}
		if len(deductedItems) > 0 {
			deductedOrder := order
			deductedOrder.Items = deductedItems
			deductedOrders = append(deductedOrders, deductedOrder)
		}
		if len(normalItems) > 0 {
			normalOrder := order
			normalOrder.Items = normalItems
			normalOrders = append(normalOrders, normalOrder)
		}
	}
	return
}

func (s *ItemStockServiceImpl) getNormalOrderStockInfo(
	ctx context.Context,
	shopOrders []entity.ItemGroupShippingOrder,
	buyerID typ.UserIdType,
	isGroupBuy bool,
) ([]entity.ShopOrderStockInfo, error) {
	resp, err := s.stockService.GetCheckoutStockInfo(ctx, shopOrders, typ.UserIdType(buyerID), isGroupBuy)
	if err != nil {
		return nil, err
	}

	var shopOrderStockInfos []entity.ShopOrderStockInfo
	for i, stockInfo := range resp {
		var itemStocks []entity.ShopOrderItemStock
		for _, info := range stockInfo.GetCheckoutOrderItemStockInfo() {
			for _, modelStockInfo := range info.GetCheckoutOrderModelStockInfo() {
				itemStock, err := s.parseModelStockInfo(ctx, modelStockInfo)
				if err != nil {
					return nil, err
				}
				itemStocks = append(itemStocks, itemStock)
			}
		}

		shopOrderStockInfos = append(shopOrderStockInfos, entity.ShopOrderStockInfo{
			ShopID:     shopOrders[i].ShopID,
			ItemStocks: itemStocks,
		})
	}

	return shopOrderStockInfos, nil
}

func (s *ItemStockServiceImpl) getDeductedOrderStockInfo(shopOrders []entity.ItemGroupShippingOrder) ([]entity.ShopOrderStockInfo, error) {
	var shopOrderStockInfos []entity.ShopOrderStockInfo
	for _, order := range shopOrders {
		var itemStocks []entity.ShopOrderItemStock
		for _, item := range order.Items {
			if item.LocalSipMtskuInfo == nil {
				return nil, fmt.Errorf("deducted item without LocalSipMtskuInfo")
			}
			itemStock := entity.ShopOrderItemStock{
				ItemID:        item.ItemId,
				ModelID:       item.ModelId,
				OrderItemType: uint32(price_checkout_promo.Constant_NORMAL_ORDER_ITEM),
				StockLocations: []entity.StockLocation{
					{
						LocationID:      item.LocalSipMtskuInfo.ProductLocationId,
						FulfillmentType: item.LocalSipMtskuInfo.ProductFulfilmentType,
						AvailableStock:  uint32(item.Quantity),
					},
				},
			}
			itemStocks = append(itemStocks, itemStock)
		}

		shopOrderStockInfos = append(shopOrderStockInfos, entity.ShopOrderStockInfo{
			ShopID:     order.ShopID,
			ItemStocks: itemStocks,
		})
	}

	return shopOrderStockInfos, nil
}

func (s *ItemStockServiceImpl) parseModelStockInfo(
	ctx context.Context,
	modelStockInfo *price_checkout_promo.CheckoutOrderModelStockInfo,
) (entity.ShopOrderItemStock, error) {
	if len(modelStockInfo.GetResults()) > 1 {
		err := fmt.Errorf("more than 1 Stock Result is not supported")
		Logger.CtxLogErrorf(ctx, err.Error())
		return entity.ShopOrderItemStock{}, err
	}

	itemStock := entity.ShopOrderItemStock{
		ItemID:  typ.ItemIdType(modelStockInfo.GetItemId()),
		ModelID: typ.ModelIdType(modelStockInfo.GetModelId()),
	}

	if len(modelStockInfo.GetResults()) == 0 {
		return itemStock, nil
	}

	result := modelStockInfo.GetResults()[0]
	itemStock.OrderItemType = result.GetOrderItemType()
	itemStock.BundleDealID = result.GetBundleDeal().GetBundleDealId()
	if len(result.GetChosenStock().GetSelectedStocksBreakdownByLocation()) == 0 {
		itemStock.StockLocations = append(itemStock.StockLocations, entity.StockLocation{
			AvailableStock:  uint32(result.GetChosenStock().GetSelectedStock()),
			FulfillmentType: constant.FulfilmentTypeSeller,
		})
	} else {
		for _, location := range result.GetChosenStock().GetSelectedStocksBreakdownByLocation() {
			itemStock.StockLocations = append(itemStock.StockLocations, entity.StockLocation{
				LocationID:      location.GetLocationId(),
				AvailableStock:  uint32(location.GetAvailableStock()),
				FulfillmentType: constant.FulfilmentType(location.GetFulfilmentType()),
			})
		}
	}

	return itemStock, nil
}
