package channel

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/localcache"
)

type ChannelService interface {
	// GetChannelByID(ctx context.Context, channelID int) (Channel, error)
	FilterOutNonSellerLogisticsByChannelIDs(ctx context.Context, channelIDs []int) ([]int, error)
}

type ChannelServiceImpl struct {
	channelLocalCache localcache.LocalCache[int, *lpslib.ChannelInfo]
}

func NewChannelService() (*ChannelServiceImpl, error) {
	channelLocalCache, err := localcache.GetCache[int, *lpslib.ChannelInfo](cache.Channels)
	if err != nil {
		return nil, err
	}
	return &ChannelServiceImpl{channelLocalCache: channelLocalCache}, nil
}

func (s *ChannelServiceImpl) FilterOutNonSellerLogisticsByChannelIDs(
	ctx context.Context,
	channelIDs []int,
) ([]int, error) {
	channelIDToChannel := s.channelLocalCache.MGet(ctx, channelIDs)
	return filterOutNonSellerLogistics(channelIDToChannel)
}

func filterOutNonSellerLogistics(channelIDToInfo map[int]*lpslib.ChannelInfo) ([]int, error) {
	var channelIDs []int
	for channelID, channelInfo := range channelIDToInfo {
		if IsSellerLogisticsChannel(channelInfo) {
			channelIDs = append(channelIDs, channelID)
		}
	}

	return channelIDs, nil
}

func IsSellerLogisticsChannel(channelInfo *lpslib.ChannelInfo) bool {
	return constant.LogisticsCapabilityMask(channelInfo.LogisticsCapability)&constant.LcSeller > 0
}
