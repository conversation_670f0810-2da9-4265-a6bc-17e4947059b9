package warehouse_priority

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

func TestWarehousePriorityServiceImpl_getStraightDistance(t *testing.T) {
	service := &WarehousePriorityServiceImpl{}

	// 新加坡和吉隆坡的坐标
	singapore := entity.GeoLocation{Latitude: 1.3521, Longitude: 103.8198}
	kualaLumpur := entity.GeoLocation{Latitude: 3.1390, Longitude: 101.6869}

	distance := service.getStraightDistance(singapore, kualaLumpur)

	// 新加坡到吉隆坡的直线距离大约是314公里
	assert.Greater(t, distance, 300000.0) // 300km
	assert.Less(t, distance, 350000.0)    // 350km
}

func TestWarehousePriorityServiceImpl_getStraightDistance_SameLocation(t *testing.T) {
	service := &WarehousePriorityServiceImpl{}

	// 相同位置
	location := entity.GeoLocation{Latitude: 1.3521, Longitude: 103.8198}

	distance := service.getStraightDistance(location, location)

	// 相同位置的距离应该是0
	assert.Equal(t, 0.0, distance)
}

func TestNoAddressError(t *testing.T) {
	err := &NoAddressError{Message: "test error message"}
	assert.Equal(t, "test error message", err.Error())
}

func TestNoAddressError_Empty(t *testing.T) {
	err := &NoAddressError{}
	assert.Equal(t, "", err.Error())
}
