// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package warehouse_priority is a generated GoMock package.
package warehouse_priority

import (
	context "context"
	reflect "reflect"

	constant "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	typ "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockWarehousePriorityService is a mock of WarehousePriorityService interface.
type MockWarehousePriorityService struct {
	ctrl     *gomock.Controller
	recorder *MockWarehousePriorityServiceMockRecorder
}

// MockWarehousePriorityServiceMockRecorder is the mock recorder for MockWarehousePriorityService.
type MockWarehousePriorityServiceMockRecorder struct {
	mock *MockWarehousePriorityService
}

// NewMockWarehousePriorityService creates a new mock instance.
func NewMockWarehousePriorityService(ctrl *gomock.Controller) *MockWarehousePriorityService {
	mock := &MockWarehousePriorityService{ctrl: ctrl}
	mock.recorder = &MockWarehousePriorityServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWarehousePriorityService) EXPECT() *MockWarehousePriorityServiceMockRecorder {
	return m.recorder
}

// BatchGetItemOrganizeStrategy mocks base method.
func (m *MockWarehousePriorityService) BatchGetItemOrganizeStrategy(ctx context.Context, shopIDs []uint64, defaultStrategy constant.ItemOrganizeStrategy) map[uint64]constant.ItemOrganizeStrategy {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetItemOrganizeStrategy", ctx, shopIDs, defaultStrategy)
	ret0, _ := ret[0].(map[uint64]constant.ItemOrganizeStrategy)
	return ret0
}

// BatchGetItemOrganizeStrategy indicates an expected call of BatchGetItemOrganizeStrategy.
func (mr *MockWarehousePriorityServiceMockRecorder) BatchGetItemOrganizeStrategy(ctx, shopIDs, defaultStrategy interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetItemOrganizeStrategy", reflect.TypeOf((*MockWarehousePriorityService)(nil).BatchGetItemOrganizeStrategy), ctx, shopIDs, defaultStrategy)
}

// CompareShopeeAndSellerWarehousePriorityByESF mocks base method.
func (m *MockWarehousePriorityService) CompareShopeeAndSellerWarehousePriorityByESF(ctx context.Context, shopID uint64, buyerAddress entity.ItemGroupBuyerAddress, warehouseList []string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareShopeeAndSellerWarehousePriorityByESF", ctx, shopID, buyerAddress, warehouseList)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompareShopeeAndSellerWarehousePriorityByESF indicates an expected call of CompareShopeeAndSellerWarehousePriorityByESF.
func (mr *MockWarehousePriorityServiceMockRecorder) CompareShopeeAndSellerWarehousePriorityByESF(ctx, shopID, buyerAddress, warehouseList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareShopeeAndSellerWarehousePriorityByESF", reflect.TypeOf((*MockWarehousePriorityService)(nil).CompareShopeeAndSellerWarehousePriorityByESF), ctx, shopID, buyerAddress, warehouseList)
}

// PrioritizeSellerMultiWarehouse mocks base method.
func (m *MockWarehousePriorityService) PrioritizeSellerMultiWarehouse(ctx context.Context, buyerAddressGeo entity.GeoLocation, sellerID typ.UserIdType, shopWarehouses []entity.ShopWarehouse, warehouseList []string) []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrioritizeSellerMultiWarehouse", ctx, buyerAddressGeo, sellerID, shopWarehouses, warehouseList)
	ret0, _ := ret[0].([]string)
	return ret0
}

// PrioritizeSellerMultiWarehouse indicates an expected call of PrioritizeSellerMultiWarehouse.
func (mr *MockWarehousePriorityServiceMockRecorder) PrioritizeSellerMultiWarehouse(ctx, buyerAddressGeo, sellerID, shopWarehouses, warehouseList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrioritizeSellerMultiWarehouse", reflect.TypeOf((*MockWarehousePriorityService)(nil).PrioritizeSellerMultiWarehouse), ctx, buyerAddressGeo, sellerID, shopWarehouses, warehouseList)
}

// PrioritizeShopeeAndSellerWarehouse mocks base method.
func (m *MockWarehousePriorityService) PrioritizeShopeeAndSellerWarehouse(ctx context.Context, shopID uint64, buyerAddress entity.ItemGroupBuyerAddress, warehouseList []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrioritizeShopeeAndSellerWarehouse", ctx, shopID, buyerAddress, warehouseList)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrioritizeShopeeAndSellerWarehouse indicates an expected call of PrioritizeShopeeAndSellerWarehouse.
func (mr *MockWarehousePriorityServiceMockRecorder) PrioritizeShopeeAndSellerWarehouse(ctx, shopID, buyerAddress, warehouseList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrioritizeShopeeAndSellerWarehouse", reflect.TypeOf((*MockWarehousePriorityService)(nil).PrioritizeShopeeAndSellerWarehouse), ctx, shopID, buyerAddress, warehouseList)
}

// PrioritizeShopeeWarehouse mocks base method.
func (m *MockWarehousePriorityService) PrioritizeShopeeWarehouse(ctx context.Context, buyerAddr entity.ItemGroupBuyerAddress, whSources []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrioritizeShopeeWarehouse", ctx, buyerAddr, whSources)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrioritizeShopeeWarehouse indicates an expected call of PrioritizeShopeeWarehouse.
func (mr *MockWarehousePriorityServiceMockRecorder) PrioritizeShopeeWarehouse(ctx, buyerAddr, whSources interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrioritizeShopeeWarehouse", reflect.TypeOf((*MockWarehousePriorityService)(nil).PrioritizeShopeeWarehouse), ctx, buyerAddr, whSources)
}
