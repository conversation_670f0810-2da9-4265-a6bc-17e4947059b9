package warehouse_priority

import (
	"context"
	"fmt"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type SellerWHPriorityService interface {
	BatchGetWHPriority(ctx context.Context, buyerId typ.UserIdType, buyerAddressId uint64, shopIds []uint64) (map[uint64]entity.WHPriorityInfo, error)
}

type SellerWHPriorityServiceImpl struct {
	shopMultiWhCache shop.ShopMultiWHCache
	addrService      address.AddrService
	localSIPService  shop_local_sip.ShopLocalSIPService
}

func NewSellerWHPriorityService(
	shopMultiWhCache shop.ShopMultiWHCache,
	addrService address.AddrService,
	localSIPService shop_local_sip.ShopLocalSIPService,
) *SellerWHPriorityServiceImpl {
	return &SellerWHPriorityServiceImpl{
		shopMultiWhCache: shopMultiWhCache,
		addrService:      addrService,
		localSIPService:  localSIPService,
	}
}

func (service *SellerWHPriorityServiceImpl) BatchGetWHPriority(
	ctx context.Context,
	buyerId typ.UserIdType,
	buyerAddressId uint64,
	shopIds []uint64,
) (map[uint64]entity.WHPriorityInfo, error) {
	shopIdToWhitelistDetail, err := service.shopMultiWhCache.GetShopIdToWhitelistDetailMap(ctx, shopIds)
	if err != nil {
		//logCtx.Warn(
		//	"get shop whitelist detail failed, fall back to MWH Seller wh priority",
		//	ulog.Error(err),
		//)
		return service.batchGetMWHSellerWHPriority(ctx, buyerId, buyerAddressId, shopIds)
	}
	notWhitelistedShops, whitelistedShops := service.categorizeShopsBy3PFWhitelist(
		shopIdToWhitelistDetail,
	)
	//logCtx.Withs("shop_id", whitelistedShops).Info("3pf whitelisted shops")

	shopIdToWHPriorityMap, err := service.batchGet3PFWHPriority(ctx, whitelistedShops)
	if err != nil {
		return nil, fmt.Errorf("get 3pf wh priority, %w", err)
	}
	//logCtx.Withs("shop_id_to_wh_priority", shopIdToWHPriorityMap).Info("3pf WHs priority")

	if len(notWhitelistedShops) > 0 {
		mwhShopToWHPriorityMap, err := service.batchGetMWHSellerWHPriority(
			ctx,
			buyerId,
			buyerAddressId,
			notWhitelistedShops,
		)
		if err != nil {
			return nil, fmt.Errorf("get mwh shop wh priority, %w", err)
		}

		// merge 2 maps
		for k, v := range mwhShopToWHPriorityMap {
			shopIdToWHPriorityMap[k] = v
		}
	}

	return shopIdToWHPriorityMap, err
}

func (service *SellerWHPriorityServiceImpl) batchGetMWHSellerWHPriority(
	ctx context.Context,
	buyerId typ.UserIdType,
	buyerAddressId uint64,
	shopIds []uint64,
) (priorityInfoMap map[uint64]entity.WHPriorityInfo, err error) {
	shopWHMap, err := service.shopMultiWhCache.GetShopWarehouseMap(ctx, shopIds)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get shop warehouses error: %v", err)
		return nil, err
	}

	manyWHShops, oneWHShops := pickShopsByWHNumber(shopWHMap)

	shopIDToSellerUserId, err := service.shopMultiWhCache.GetShopIDToSellerUserId(ctx, manyWHShops)
	if err != nil {
		return nil, err
	}

	shopIsMultiWHMap := service.shopMultiWhCache.GetShopIdToIsMultiWHMap(ctx, oneWHShops)

	priorityInfoMap = make(map[uint64]entity.WHPriorityInfo, 0)
	for _, shopId := range shopIds {
		shopWHs := shopWHMap[shopId]

		var priorityInfo entity.WHPriorityInfo
		var err error
		switch len(shopWHs) {
		case 0:
			priorityInfo = entity.NewMWHSellerWHPriorityInfo(false, []entity.WHInfo{})
		case 1:
			isMultiWH := shopIsMultiWHMap[shopId]
			//if isMultiWH {
			//	logCtx.Withs("shopId", shopId).Info("multi-wh seller")
			//}
			priorityInfo, err = service.getOneWHPriority(isMultiWH, shopWHs[0])
		default:
			priorityInfo, err = service.getManyWHPriority(ctx, buyerId, buyerAddressId, shopId, shopIDToSellerUserId[shopId], shopWHs)
		}

		if err != nil {
			//logCtx.Withs("shopId", shopId).Error(err.Error())
			return nil, err
		}

		priorityInfoMap[shopId] = priorityInfo
	}

	//logCtx.Withs("shop_id_to_wh_priority", priorityInfoMap).Info("MWH seller WHs priority")
	return priorityInfoMap, nil
}

func (service *SellerWHPriorityServiceImpl) batchGet3PFWHPriority(
	ctx context.Context,
	cb3PFShopIds []uint64,
) (shopIdToWhPriority map[uint64]entity.WHPriorityInfo, err error) {
	//logCtx := logger.WithCtx(ctx).Withs("cb3PFShopIds", cb3PFShopIds)

	if len(cb3PFShopIds) == 0 {
		return make(map[uint64]entity.WHPriorityInfo), nil
	}

	shopIdToWhList, err := service.shopMultiWhCache.GetShopIdToWarehouseListMap(ctx, cb3PFShopIds)
	if err != nil {
		//logCtx.Error("get 3pf shop warehouses", ulog.Error(err))
		return nil, fmt.Errorf("get 3pf shop warehouses, %w", err)
	}

	shopIdToWhPriority = make(map[uint64]entity.WHPriorityInfo)
	for shopId, whList := range shopIdToWhList {
		if len(whList) == 0 {
			//logCtx.Withs("shop_id", shopId).Error("check WH count", ulog.Error(fsserr.Err3PFShopNoWH))
			return nil, fmt.Errorf("check WH count, %w", fsserr.Err3PFShopNoWH)
		}
		shopIdToWhPriority[shopId] = service.get3PFWhPriorityByShop(ctx, whList)
	}
	return shopIdToWhPriority, nil
}

func (service *SellerWHPriorityServiceImpl) getOneWHPriority(
	isMultiWHShop bool,
	warehouse entity.ShopWarehouse,
) (entity.WHPriorityInfo, error) {
	if isMultiWHShop {
		return entity.NewMWHSellerWHPriorityInfo(
			true, []entity.WHInfo{
				{
					LocationId: warehouse.LocationID,
					AddressId:  warehouse.AddressID,
				},
			},
		), nil
	}
	return entity.NewMWHSellerWHPriorityInfo(false, []entity.WHInfo{}), nil
}

func (service *SellerWHPriorityServiceImpl) getManyWHPriority(
	ctx context.Context,
	buyerId typ.UserIdType,
	buyerAddressId uint64,
	shopId uint64,
	shopUserId uint64,
	warehouses []entity.ShopWarehouse,
) (entity.WHPriorityInfo, error) {
	buyerGeoLocation, err := service.getBuyerGeoLocation(ctx, buyerId, buyerAddressId, shopId)
	if err != nil {
		return entity.WHPriorityInfo{}, err
	}

	whGeoLocationMap, err := service.batchGetSellerWHGeoLocation(ctx, shopUserId, warehouses)

	var prioritizedList []entity.WHInfo
	if err != nil {
		//logger.WithCtx(ctx).Warn(
		//	"cannot get seller WH geo-location, fallback to prioritization by location_id",
		//	ulog.Error(err),
		//)
		prioritizedList = prioritizeByLocationId(warehouses)
	} else {
		locationDistanceMap := getStraightDistanceMap(ctx, buyerGeoLocation, whGeoLocationMap)
		prioritizedList = prioritizeByDistance(warehouses, locationDistanceMap)
	}

	return entity.NewMWHSellerWHPriorityInfo(true, prioritizedList), nil
}

func (service *SellerWHPriorityServiceImpl) getBuyerGeoLocation(
	ctx context.Context,
	buyerUserId typ.UserIdType,
	buyerAddressId uint64,
	shopId uint64,
) (entity.GeoLocation, error) {
	if shopId == 0 {
		return service.addrService.GetBuyerGeoLocation(ctx, buyerUserId, buyerAddressId)
	}
	sipInfo, _ := service.localSIPService.GetShopLocalSIPInfo(ctx, shopId)
	if !sipInfo.IsSIPAffiliated {
		return service.addrService.GetBuyerGeoLocation(ctx, buyerUserId, buyerAddressId)
	}
	return service.addrService.GetDummyBuyerGeoLocation(ctx, sipInfo.DummyBuyerID, sipInfo.SIPPrimaryRegion)
}

func (service *SellerWHPriorityServiceImpl) categorizeShopsBy3PFWhitelist(
	shopIdToWhitelistDetail map[uint64]entity.ShopWhitelistDetail,
) ([]uint64, []uint64) {
	notWhitelistedShops := make([]uint64, 0)
	whitelistedShops := make([]uint64, 0)
	for shopId, detail := range shopIdToWhitelistDetail {
		if !detail.Is3PFWhitelistAndCanUseWarehouseForPickup() {
			notWhitelistedShops = append(notWhitelistedShops, shopId)
		} else {
			whitelistedShops = append(whitelistedShops, shopId)
		}
	}
	return notWhitelistedShops, whitelistedShops
}

func (service *SellerWHPriorityServiceImpl) batchGetSellerWHGeoLocation(
	ctx context.Context,
	shopUserId uint64,
	warehouses []entity.ShopWarehouse,
) (whGeoLocationMap map[string]entity.GeoLocation, err error) {
	addresses := make([]uint64, 0)
	addrWHMap := make(map[uint64]entity.ShopWarehouse, 0)
	for _, wh := range warehouses {
		addresses = append(addresses, wh.AddressID)
		addrWHMap[wh.AddressID] = wh
	}

	geoLocationMap, err := service.shopMultiWhCache.GetShopWarehouseGeoLocation(
		ctx,
		shopUserId,
		addresses,
	)
	if err != nil {
		return nil, err
	}

	whGeoLocationMap = make(map[string]entity.GeoLocation, 0)
	for addressId, geoLocation := range geoLocationMap {
		wh := addrWHMap[addressId]
		whGeoLocationMap[wh.LocationID] = geoLocation
	}
	return whGeoLocationMap, nil
}

// local warehouse(s) > CB Warehouse (CN/KR)
// within local warehouses: priority is by alphabetic orders
func (service *SellerWHPriorityServiceImpl) get3PFWhPriorityByShop(ctx context.Context, whList []entity.ShopWarehouse) entity.WHPriorityInfo {
	sort.Slice(
		whList, func(i, j int) bool {
			if whList[i].IsLocalWarehouse(ctx) && !whList[j].IsLocalWarehouse(ctx) {
				return true
			}
			if !whList[i].IsLocalWarehouse(ctx) && whList[j].IsLocalWarehouse(ctx) {
				return false
			}
			return whList[i].LocationID < whList[j].LocationID
		},
	)

	// convert
	whInfos := make([]entity.WHInfo, 0)
	for _, wh := range whList {
		whInfos = append(
			whInfos, entity.WHInfo{
				LocationId: wh.LocationID,
				AddressId:  wh.AddressID,
			},
		)
	}
	return entity.New3PFWHPriorityInfo(true, whInfos)
}

func pickShopsByWHNumber(shopWHMap map[uint64][]entity.ShopWarehouse) (
	manyWHShops []uint64,
	oneWHShops []uint64,
) {
	for shopId, shopWHs := range shopWHMap {
		if len(shopWHs) > 1 {
			manyWHShops = append(manyWHShops, shopId)
		} else {
			oneWHShops = append(oneWHShops, shopId)
		}
	}
	return manyWHShops, oneWHShops
}
