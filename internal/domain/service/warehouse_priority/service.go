package warehouse_priority

//go:generate mockgen --build_flags=--mod=mod -source=service.go -destination=service_mock.go -package=warehouse_priority -mock_names=WarehousePriorityService=MockWarehousePriorityService

import (
	"context"
	"math"
	"sort"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/omslib"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type WarehousePriorityService interface {
	PrioritizeShopeeWarehouse(ctx context.Context, buyerAddr entity.ItemGroupBuyerAddress, whSources []string) ([]string, fsserr.Error)
	PrioritizeShopeeAndSellerWarehouse(ctx context.Context, shopID uint64, buyerAddress entity.ItemGroupBuyerAddress, warehouseList []string) ([]string, fsserr.Error)
	PrioritizeSellerMultiWarehouse(ctx context.Context, buyerAddressGeo entity.GeoLocation, sellerID typ.UserIdType, shopWarehouses []entity.ShopWarehouse, warehouseList []string) []string
	BatchGetItemOrganizeStrategy(ctx context.Context, shopIDs []uint64, defaultStrategy constant.ItemOrganizeStrategy) map[uint64]constant.ItemOrganizeStrategy
	// CompareShopeeAndSellerWarehousePriorityByESF compares the priority of Shopee warehouse and seller warehouse by ESF
	// Return -1 if Shopee warehouse has higher priority, 1 if seller warehouse has higher priority, 0 if they have the same priority
	CompareShopeeAndSellerWarehousePriorityByESF(ctx context.Context, shopID uint64, buyerAddress entity.ItemGroupBuyerAddress, warehouseList []string) (int, error)
}

type WarehousePriorityServiceImpl struct {
	confAccessor     config.ConfAccessor
	omsClient        omslib.OMSClient
	addressService   address.AddrService
	sellerTagService seller_tag.SellerTagService
}

func NewWarehousePriorityService(
	confAccessor config.ConfAccessor,
	omsClient omslib.OMSClient,
	addressService address.AddrService,
	sellerTagService seller_tag.SellerTagService,
) *WarehousePriorityServiceImpl {
	return &WarehousePriorityServiceImpl{
		confAccessor:     confAccessor,
		omsClient:        omsClient,
		addressService:   addressService,
		sellerTagService: sellerTagService,
	}
}

func (s *WarehousePriorityServiceImpl) PrioritizeShopeeWarehouse(
	ctx context.Context,
	buyerAddr entity.ItemGroupBuyerAddress,
	whSources []string,
) ([]string, fsserr.Error) {
	return s.PrioritizeShopeeAndSellerWarehouse(ctx, 0, buyerAddr, whSources)
}

func (s *WarehousePriorityServiceImpl) PrioritizeShopeeAndSellerWarehouse(
	ctx context.Context,
	shopID uint64,
	buyerAddress entity.ItemGroupBuyerAddress,
	warehouseList []string,
) ([]string, fsserr.Error) {
	warehouseToPriority, err := s.getAllWarehousePriority(ctx, shopID, buyerAddress)
	if err != nil {
		return nil, err
	}

	sort.SliceStable(warehouseList, func(i, j int) bool {
		if warehouseToPriority[warehouseList[i]] != warehouseToPriority[warehouseList[j]] {
			return warehouseToPriority[warehouseList[i]] > warehouseToPriority[warehouseList[j]]
		}
		return warehouseList[i] < warehouseList[j]
	})

	return warehouseList, nil
}

func (s *WarehousePriorityServiceImpl) PrioritizeSellerMultiWarehouse(
	ctx context.Context,
	buyerAddressGeo entity.GeoLocation,
	sellerID typ.UserIdType,
	shopWarehouses []entity.ShopWarehouse,
	warehouseList []string,
) []string {
	if len(warehouseList) <= 1 {
		return warehouseList
	}

	warehouseAddressIDs := make([]uint64, 0, len(shopWarehouses))
	for _, wh := range shopWarehouses {
		warehouseAddressIDs = append(warehouseAddressIDs, wh.AddressID)
	}

	addressIDToGeo, err := s.addressService.BatchGetShopWarehouseGeo(ctx, sellerID, warehouseAddressIDs)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get shop warehouse geo failed, err: %v", err)
		sort.Slice(warehouseList, func(i, j int) bool {
			return warehouseList[i] < warehouseList[j]
		})
		return warehouseList
	}

	warehouseToGeo := make(map[string]entity.GeoLocation, len(shopWarehouses))
	for _, wh := range shopWarehouses {
		warehouseToGeo[wh.LocationID] = addressIDToGeo[wh.AddressID]
	}

	warehouseToDistance := make(map[string]float64, len(warehouseToGeo))
	for locationID, geo := range warehouseToGeo {
		warehouseToDistance[locationID] = s.getStraightDistance(buyerAddressGeo, geo)
	}

	Logger.CtxLogInfof(ctx, "warehouse distance calculation, warehouseToDistance: %v", warehouseToDistance)

	sort.SliceStable(warehouseList, func(i, j int) bool {
		if warehouseToDistance[warehouseList[i]] != warehouseToDistance[warehouseList[j]] {
			return warehouseToDistance[warehouseList[i]] < warehouseToDistance[warehouseList[j]]
		}
		return warehouseList[i] < warehouseList[j]
	})
	return warehouseList
}

func (s *WarehousePriorityServiceImpl) getStraightDistance(a entity.GeoLocation, b entity.GeoLocation) float64 {
	aLatInRadian := a.Latitude * math.Pi / 180
	aLongInRadian := a.Longitude * math.Pi / 180
	bLatInRadian := b.Latitude * math.Pi / 180
	bLongInRadian := b.Longitude * math.Pi / 180

	x := (bLongInRadian - aLongInRadian) * math.Cos((aLatInRadian+bLatInRadian)/2)
	y := bLatInRadian - aLatInRadian

	var earthRadius float64 = 6371 * 1000 // in meters
	return math.Sqrt(x*x+y*y) * earthRadius
}

func (s *WarehousePriorityServiceImpl) BatchGetItemOrganizeStrategy(
	ctx context.Context,
	shopIDs []uint64,
	defaultStrategy constant.ItemOrganizeStrategy,
) map[uint64]constant.ItemOrganizeStrategy {
	shopIDToStrategy := make(map[uint64]constant.ItemOrganizeStrategy, len(shopIDs))
	for _, shopID := range shopIDs {
		shopIDToStrategy[shopID] = defaultStrategy
	}

	if s.confAccessor.GetIGSDynamicConfig(ctx).DowngradeToggle.ToggleOffWhSplitStrategyApi {
		Logger.CtxLogInfof(ctx, "downgrade warehouse strategy API, use default strategy: %v", defaultStrategy)
		return shopIDToStrategy
	}

	shopIDToTagValue, err := s.sellerTagService.GetShopSellerTagWithCache(ctx, shopIDs, constant.ShopTagMinParcel)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "failed to get shop seller tag, use default item organize strategy, err: %v", err)
		return shopIDToStrategy
	}

	for shopID, tagValue := range shopIDToTagValue {
		if tagValue {
			shopIDToStrategy[shopID] = constant.ItemOrganizeStrategyMinimumParcel
		} else {
			shopIDToStrategy[shopID] = constant.ItemOrganizeStrategyWarehousePriority
		}
	}
	return shopIDToStrategy
}

func (s *WarehousePriorityServiceImpl) CompareShopeeAndSellerWarehousePriorityByESF(
	ctx context.Context,
	shopID uint64,
	buyerAddress entity.ItemGroupBuyerAddress,
	warehouseList []string,
) (int, error) {
	entities, err := s.omsClient.GetAllWarehousePriorityUsingCache(ctx, omslib.WhsPriorityBatchRequest{
		Country:  envvar.GetCID(ctx),
		State:    buyerAddress.State,
		City:     buyerAddress.City,
		District: buyerAddress.District,
		ShopID:   shopID,
	})
	if err != nil {
		return 0, err
	}

	warehouseCodeToEntity := make(map[string]omslib.WhsPriorityEntity)
	for _, whsPriorityEntity := range entities {
		warehouseCodeToEntity[strings.ToUpper(whsPriorityEntity.WarehouseID)] = whsPriorityEntity
	}

	lowestShopeeWarehouseESF, lowestSellerWarehouseESF := math.MaxFloat64, math.MaxFloat64
	for _, warehouseCode := range warehouseList {
		whsPriorityEntity, ok := warehouseCodeToEntity[strings.ToUpper(warehouseCode)]
		if !ok {
			continue
		}
		if entity.IsSellerWarehouseId(whsPriorityEntity.WarehouseID) {
			lowestSellerWarehouseESF = math.Min(lowestSellerWarehouseESF, whsPriorityEntity.ESF)
		} else {
			lowestShopeeWarehouseESF = math.Min(lowestShopeeWarehouseESF, whsPriorityEntity.ESF)
		}
	}
	if lowestShopeeWarehouseESF < lowestSellerWarehouseESF {
		return -1, nil
	}
	if lowestShopeeWarehouseESF > lowestSellerWarehouseESF {
		return 1, nil
	}
	return 0, nil
}

func (s *WarehousePriorityServiceImpl) getAllWarehousePriority(
	ctx context.Context,
	shopID uint64,
	buyerAddress entity.ItemGroupBuyerAddress,
) (map[string]int, fsserr.Error) {
	defaultWarehousePriority := s.getDefaultPriority(ctx)

	if buyerAddress.State == "" || buyerAddress.City == "" {
		// no address
		if buyerAddress.Required {
			// TODO: 实现 NoAddressError 类型
			Logger.CtxLogErrorf(ctx, "buyer address can not be empty")
			return nil, fsserr.New(fsserr.BuyerAddressNotFoundErr, "buyer address can not be empty")
		} else {
			Logger.CtxLogInfof(ctx, "using default warehouse priority: %v", defaultWarehousePriority)
			return defaultWarehousePriority, nil
		}
	}

	if s.confAccessor.GetIGSDynamicConfig(ctx).DowngradeToggle.ToggleOffOmsWhPriorityApi {
		Logger.CtxLogInfof(ctx, "downgrade oms API, using default warehouse priority: %v", defaultWarehousePriority)
		return defaultWarehousePriority, nil
	}

	// TODO: 实现 API 调用获取仓库优先级
	entities, err := s.omsClient.GetAllWarehousePriorityUsingCache(ctx, omslib.WhsPriorityBatchRequest{
		Country:  envvar.GetCID(ctx),
		State:    buyerAddress.State,
		City:     buyerAddress.City,
		District: buyerAddress.District,
		ShopID:   shopID,
	})
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get warehouse priority failed, using default warehouse priority, err: %v, whPriority: %v", err, defaultWarehousePriority)
		return defaultWarehousePriority, nil
	}

	warehouseToPriority := make(map[string]int, len(entities))
	for _, priorityEntity := range entities {
		warehouseToPriority[priorityEntity.WarehouseID] = priorityEntity.Priority
	}

	return warehouseToPriority, nil
}

func (s *WarehousePriorityServiceImpl) getDefaultPriority(ctx context.Context) map[string]int {
	defaultWhPriority := s.confAccessor.GetIGSDynamicConfig(ctx).DefaultWhPriority
	defaultPriorityMap := make(map[string]int, len(defaultWhPriority))
	// on spex config center: Low index has higher priority, as for OMS response, bigger numbers have higher priority
	for index, wh := range defaultWhPriority {
		defaultPriorityMap[wh] = len(defaultWhPriority) - index
	}
	return defaultPriorityMap
}

// TODO: 定义错误类型
type NoAddressError struct {
	Message string
}

func (e *NoAddressError) Error() string {
	return e.Message
}
