package warehouse_priority

import (
	"context"
	"fmt"
	"math"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// Returns the straight distance between the given buyer location
// and each WH.
func getStraightDistanceMap(
	ctx context.Context,
	buyerLocation entity.GeoLocation,
	whGeoLocationMap map[string]entity.GeoLocation,
) (locationDistanceMap map[string]float64) {
	locationDistanceMap = make(map[string]float64, 0)
	for locationId, geoLocation := range whGeoLocationMap {
		distance := getStraightDistance(
			entity.GeoLocation{
				Latitude:  buyerLocation.Latitude,
				Longitude: buyerLocation.Longitude,
			}, geoLocation,
		)
		locationDistanceMap[locationId] = distance
	}

	//logger.WithCtx(ctx).Withs(
	//	"buyerLocation",
	//	buyerLocation,
	//	"warehouseGeoLocationMap",
	//	whGeoLocationMap,
	//).Info(fmt.Sprintf("distance map: %v", locationDistanceMap))
	return locationDistanceMap
}

// Sort the given slice in-place such that the 1st item has the smallest distance.
func prioritizeByDistance(
	warehouses []entity.ShopWarehouse,
	distanceMap map[string]float64,
) (prioritizedWHs []entity.WHInfo) {
	for _, wh := range warehouses {
		prioritizedWHs = append(
			prioritizedWHs, entity.WHInfo{
				LocationId: wh.LocationID,
				AddressId:  wh.AddressID,
			},
		)
	}
	sort.Slice(
		prioritizedWHs, func(i, j int) bool {
			locationI := prioritizedWHs[i].LocationId
			locationJ := prioritizedWHs[j].LocationId
			return distanceMap[locationI] < distanceMap[locationJ]
		},
	)
	return prioritizedWHs
}

// Sort the given slice in-place such that the 1st item has the smallest
// location_id in alphanumerical order.
func prioritizeByLocationId(warehouses []entity.ShopWarehouse) (prioritizedWHs []entity.WHInfo) {
	for _, wh := range warehouses {
		prioritizedWHs = append(
			prioritizedWHs, entity.WHInfo{
				LocationId: wh.LocationID,
				AddressId:  wh.AddressID,
			},
		)
	}
	sort.Slice(
		prioritizedWHs, func(i, j int) bool {
			return prioritizedWHs[i].LocationId < prioritizedWHs[j].LocationId
		},
	)
	return prioritizedWHs
}

// todo: (minhthai.pham) we should have dedicate geo-location struct for this
//
//	logic
//
// Returns the straight-line distance between 2 coordinates in meters.
// This is computed using an approximation of Haversine formula
// based on equi-rectangular projection of the Earth. This is
// faster to compute but produces a less accurate result. Since we
// only compute small distances, this should be sufficient.
//
// See Also:
// - https://en.wikipedia.org/wiki/Haversine_formula
// - https://www.movable-type.co.uk/scripts/latlong.html
func getStraightDistance(
	a entity.GeoLocation,
	b entity.GeoLocation,
) float64 {
	aLatInRadian := a.Latitude * math.Pi / 180
	aLongInRadian := a.Longitude * math.Pi / 180
	bLatInRadian := b.Latitude * math.Pi / 180
	bLongInRadian := b.Longitude * math.Pi / 180

	x := (bLongInRadian - aLongInRadian) * math.Cos((aLatInRadian+bLatInRadian)/2)
	y := bLatInRadian - aLatInRadian

	const earthRadius float64 = 6371 * 1000 // in meters
	return math.Sqrt(x*x+y*y) * earthRadius
}

// returns error if a location_id is not found in the given WH list
func filterWHsByLocationIds(
	whList []entity.ShopWarehouse,
	locationIds []string,
) ([]entity.ShopWarehouse, error) {
	availWHs := make(map[string]entity.ShopWarehouse, 0)
	for _, wh := range whList {
		availWHs[wh.LocationID] = wh
	}

	warehouses := make([]entity.ShopWarehouse, 0)
	for _, locationId := range locationIds {
		wh, ok := availWHs[locationId]
		if !ok {
			return nil, fmt.Errorf(
				"WH address_id not found for location_id=%s: %w",
				locationId,
				fsserr.ErrSellerWHAddressNotFound,
			)
		}
		warehouses = append(warehouses, wh)
	}

	return warehouses, nil
}
