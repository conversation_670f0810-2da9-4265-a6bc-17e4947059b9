package checkout_promo

import (
	"context"
	"fmt"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_action.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/price_checkout_promo.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

type CheckoutPromoApi interface {
	// GetCheckoutOrderStock returns stock data as a new replacement for
	// order_action_api.OrderActionApi.
	// Besides other errors, in case of data issue (like item is not found in order data),
	// this method returns a special igs_error.StockNotFoundError. This is to mimic the
	// existing behavior of the order action API.
	GetCheckoutOrderStock(ctx context.Context, request *GetCheckoutOrderStockRequest) (
		*GetCheckoutOrderStockResponse,
		error,
	)
}

type CheckoutPromoApiImpl struct {
	spexClient spexlib.SpexClient
}

func NewCheckoutPromoApiImpl(spexClient spexlib.SpexClient) *CheckoutPromoApiImpl {
	return &CheckoutPromoApiImpl{
		spexClient: spexClient,
	}
}

func (impl *CheckoutPromoApiImpl) GetCheckoutOrderStock(
	ctx context.Context,
	request *GetCheckoutOrderStockRequest,
) (*GetCheckoutOrderStockResponse, error) {
	checkoutOrders := impl.getRequestCheckoutOrders(request)

	spexRequest := &price_checkout_promo.GetCheckoutStockInfoRequest{
		Region:         proto.String(strings.ToUpper(envvar.GetCID(ctx))),
		UserId:         proto.Uint64(uint64(request.buyerId)),
		CheckoutOrders: checkoutOrders,
	}

	response, err := impl.spexClient.GetCheckoutStockInfo(ctx, spexRequest)
	if err != nil {
		return nil, err
	}

	return parseGetCheckoutStockInfoResponse(ctx, response)
}

func (impl *CheckoutPromoApiImpl) getRequestCheckoutOrders(request *GetCheckoutOrderStockRequest) []*price_checkout_promo.CheckoutOrder {
	checkoutOrders := make([]*price_checkout_promo.CheckoutOrder, 0, len(request.orders))
	for _, order := range request.orders {
		addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap := impl.getAddOnDealMaps(order)

		checkoutModels := make([]*price_checkout_promo.CheckoutOrderModel, 0, len(order.Items))
		for _, item := range order.Items {
			chatOfferInfo := impl.getChatOfferInfo(item)
			membershipOfferInfo := impl.getMembershipOfferInfo(item)
			addOnDeal := impl.getAddOnDeal(item, addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap)
			promotionInfo := impl.getPromotionInfo(item)

			checkoutModel := &price_checkout_promo.CheckoutOrderModel{
				ShopId:                proto.Int64(int64(order.ShopID)),
				ItemId:                proto.Uint64(uint64(item.ItemId)),
				ModelId:               proto.Uint64(uint64(item.ModelId)),
				Amount:                proto.Int32(int32(item.Quantity)),
				ChatOfferInfo:         chatOfferInfo,
				MembershipOfferInfo:   membershipOfferInfo,
				AddOnDeal:             addOnDeal,
				AssignedPromotionInfo: promotionInfo,
			}
			checkoutModels = append(checkoutModels, checkoutModel)
		}

		checkoutOrder := &price_checkout_promo.CheckoutOrder{
			IsGroupBuy:     proto.Bool(request.isGroupBuy),
			CheckoutModels: checkoutModels,
		}
		checkoutOrders = append(checkoutOrders, checkoutOrder)
	}
	return checkoutOrders
}

func (impl *CheckoutPromoApiImpl) getPromotionInfo(item entity.ItemGroupShippingOrderItem) *price_checkout_promo.AssignedPromotionInfo {
	var promotionInfo *price_checkout_promo.AssignedPromotionInfo
	if item.IsItemInPrimaryOrder() {
		if !item.LocalSipMtskuInfo.HasDeduct {
			promotionInfo = &price_checkout_promo.AssignedPromotionInfo{
				RuleType: proto.Uint32(item.LocalSipMtskuInfo.PPromotionType),
				RuleId:   proto.Uint64(item.LocalSipMtskuInfo.PPromotionId),
			}
		}
	}
	return promotionInfo
}

func (impl *CheckoutPromoApiImpl) getAddOnDeal(
	item entity.ItemGroupShippingOrderItem,
	addOnDealKeyToItemsMap map[string][]entity.ItemGroupShippingOrderItem,
	addOnDealKeyToHasSubItemMap map[string]bool,
) *price_checkout_promo.AddOnDeal {
	var addOnDeal *price_checkout_promo.AddOnDeal
	if item.AddOnDealInfo.AddOnDealID != 0 {
		addOnDealInfo := item.AddOnDealInfo

		addOnDealKey := AodItemKey(addOnDealInfo.GroupID, addOnDealInfo.AddOnDealID)
		aodItemList := addOnDealKeyToItemsMap[addOnDealKey]
		hasSubItem, ok := addOnDealKeyToHasSubItemMap[addOnDealKey]

		// only if AOD/PWG have more than 1 items can form the package promotion
		if len(aodItemList) > 1 && (ok && hasSubItem) {
			addOnDeal = &price_checkout_promo.AddOnDeal{
				AddOnDealGroupId: proto.Uint64(addOnDealInfo.GroupID),
				AddOnDealId:      proto.Uint64(addOnDealInfo.AddOnDealID),
				IsAddOnDealSub:   proto.Bool(addOnDealInfo.IsAddOnSubItem),
			}
		}
	}
	return addOnDeal
}

func (impl *CheckoutPromoApiImpl) getMembershipOfferInfo(item entity.ItemGroupShippingOrderItem) *price_checkout_promo.MembershipOfferInfo {
	var membershipOfferInfo *price_checkout_promo.MembershipOfferInfo
	if item.MembershipOfferInfo.OfferID != 0 {
		membershipOfferInfo = &price_checkout_promo.MembershipOfferInfo{
			MembershipOfferId: proto.Uint64(item.MembershipOfferInfo.OfferID),
		}
	}
	return membershipOfferInfo
}

func (impl *CheckoutPromoApiImpl) getChatOfferInfo(item entity.ItemGroupShippingOrderItem) *price_checkout_promo.ChatOfferInfo {
	var chatOfferInfo *price_checkout_promo.ChatOfferInfo
	if item.OfferInfo.OfferID != 0 {
		chatOfferInfo = &price_checkout_promo.ChatOfferInfo{
			ChatOfferId: proto.Uint64(item.OfferInfo.OfferID),
		}
	}
	return chatOfferInfo
}

func (impl *CheckoutPromoApiImpl) getAddOnDealMaps(order entity.ItemGroupShippingOrder) (
	addOnDealKeyToItemsMap map[string][]entity.ItemGroupShippingOrderItem,
	addOnDealKeyToHasSubItemMap map[string]bool,
) {
	addOnDealKeyToItemsMap = make(map[string][]entity.ItemGroupShippingOrderItem, 0)
	addOnDealKeyToHasSubItemMap = make(map[string]bool, 0)
	for _, item := range order.Items {
		if item.AddOnDealInfo.AddOnDealID != 0 {
			addOnDealInfo := item.AddOnDealInfo
			addOnDealKey := AodItemKey(addOnDealInfo.GroupID, addOnDealInfo.AddOnDealID)
			addOnDealKeyToItemsMap[addOnDealKey] = append(addOnDealKeyToItemsMap[addOnDealKey], item)
			if addOnDealInfo.IsAddOnSubItem {
				addOnDealKeyToHasSubItemMap[addOnDealKey] = true
			}
		}
	}
	return addOnDealKeyToItemsMap, addOnDealKeyToHasSubItemMap
}

func parseGetCheckoutStockInfoResponse(
	ctx context.Context,
	orderStocks []*price_checkout_promo.CheckoutOrderStockInfo,
) (*GetCheckoutOrderStockResponse, error) {
	checkoutOrderStocks := make([]*CheckoutOrderStock, 0)
	for _, orderStock := range orderStocks {
		checkoutOrderStock, err := parseGetCheckoutStockInfoResponseOrder(ctx, orderStock)
		if err != nil {
			return nil, err
		}
		checkoutOrderStocks = append(checkoutOrderStocks, checkoutOrderStock)
	}

	return &GetCheckoutOrderStockResponse{OrderStocks: checkoutOrderStocks}, nil
}

func parseGetCheckoutStockInfoResponseOrder(
	ctx context.Context,
	orderStock *price_checkout_promo.CheckoutOrderStockInfo,
) (*CheckoutOrderStock, error) {
	checkoutOrderItemStocks := make([]*CheckoutOrderItemStock, 0)

	for _, orderItemStockInfo := range orderStock.GetCheckoutOrderItemStockInfo() {
		for _, orderModelStockInfo := range orderItemStockInfo.GetCheckoutOrderModelStockInfo() {
			checkoutOrderItemStock, err := parseGetCheckoutStockInfoResponseOrderModel(
				ctx,
				*orderModelStockInfo,
			)
			if err != nil {
				return nil, err
			}

			checkoutOrderItemStocks = append(
				checkoutOrderItemStocks,
				checkoutOrderItemStock,
			)
		}
	}

	return &CheckoutOrderStock{ItemStocks: checkoutOrderItemStocks}, nil
}

func parseGetCheckoutStockInfoResponseOrderModel(
	ctx context.Context,
	orderModelStock price_checkout_promo.CheckoutOrderModelStockInfo,
) (*CheckoutOrderItemStock, error) {
	var itemType order_action.Constant_OrderItemType
	var bundleDealId uint64
	var totalStock uint32
	stockBreakdowns := make([]*StockBreakdown, 0)

	// by right, there is maximum 1 result
	if len(orderModelStock.GetResults()) > 1 {
		err := fmt.Errorf("more than 1 Stock Result is not supported")
		Logger.CtxLogErrorf(ctx, "%v", err)
		return nil, err
	}

	for _, modelStockResult := range orderModelStock.GetResults() {
		totalStock += uint32(modelStockResult.GetChosenStock().GetSelectedStock())
		// currently, there is supposed to be only
		// 1 Result, so we only need to set the fields
		// below once
		if itemType == 0 {
			itemType = order_action.Constant_OrderItemType(modelStockResult.GetOrderItemType())
		}

		for _, loc := range modelStockResult.GetChosenStock().GetSelectedStocksBreakdownByLocation() {
			stockBreakdowns = append(
				stockBreakdowns, &StockBreakdown{
					LocationId:      loc.GetLocationId(),
					AvailableStock:  uint32(loc.GetAvailableStock()),
					FulfillmentType: constant.FulfilmentType(loc.GetFulfilmentType()),
				},
			)

			if bundleDealId == 0 {
				bundleDealId = modelStockResult.GetBundleDeal().GetBundleDealId()
			}
		}
	}

	return &CheckoutOrderItemStock{
		ItemId:                   typ.ItemIdType(orderModelStock.GetItemId()),
		ModelId:                  typ.ModelIdType(orderModelStock.GetModelId()),
		TotalStock:               totalStock,
		OrderItemType:            itemType,
		StockBreakdownByLocation: stockBreakdowns,
		BundleDealId:             bundleDealId,
	}, nil
}

func AodItemKey(groupID, addOnDealID uint64) string {
	return fmt.Sprintf("%d_%d", groupID, addOnDealID)
}
