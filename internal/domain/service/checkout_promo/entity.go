package checkout_promo

import (
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_action.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

type GetCheckoutOrderStockRequest struct {
	orders     []entity.ItemGroupShippingOrder
	isGroupBuy bool
	buyerId    typ.UserIdType
}

func (r *GetCheckoutOrderStockRequest) IsGroupBuy() bool {
	return r.isGroupBuy
}

func NewGetCheckoutOrderStockRequest(
	buyerId typ.UserIdType,
	orders []entity.ItemGroupShippingOrder,
	isGroupBuy bool,
) *GetCheckoutOrderStockRequest {
	return &GetCheckoutOrderStockRequest{orders: orders, isGroupBuy: isGroupBuy, buyerId: buyerId}
}

type GetCheckoutOrderStockResponse struct {
	OrderStocks []*CheckoutOrderStock
}

type CheckoutOrderStock struct {
	ItemStocks []*CheckoutOrderItemStock
}

type StockBreakdown struct {
	LocationId      string
	AvailableStock  uint32
	FulfillmentType constant.FulfilmentType
}

type CheckoutOrderItemStock struct {
	ItemId                   typ.ItemIdType
	ModelId                  typ.ModelIdType
	TotalStock               uint32
	OrderItemType            order_action.Constant_OrderItemType
	StockBreakdownByLocation []*StockBreakdown
	BundleDealId             uint64
}
