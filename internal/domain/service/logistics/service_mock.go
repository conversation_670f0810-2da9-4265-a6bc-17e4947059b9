// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package logistics is a generated GoMock package.
package logistics

import (
	context "context"
	reflect "reflect"

	lpslib "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
	gomock "github.com/golang/mock/gomock"
)

// MockLogisticsService is a mock of LogisticsService interface.
type MockLogisticsService struct {
	ctrl     *gomock.Controller
	recorder *MockLogisticsServiceMockRecorder
}

// MockLogisticsServiceMockRecorder is the mock recorder for MockLogisticsService.
type MockLogisticsServiceMockRecorder struct {
	mock *MockLogisticsService
}

// NewMockLogisticsService creates a new mock instance.
func NewMockLogisticsService(ctrl *gomock.Controller) *MockLogisticsService {
	mock := &MockLogisticsService{ctrl: ctrl}
	mock.recorder = &MockLogisticsServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLogisticsService) EXPECT() *MockLogisticsServiceMockRecorder {
	return m.recorder
}

// GetChannelWeightDimensionInfo mocks base method.
func (m *MockLogisticsService) GetChannelWeightDimensionInfo(ctx context.Context, orders []lpslib.OrderSplitLogisticsQuery) (map[string]lpslib.ChannelWeightDimensionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelWeightDimensionInfo", ctx, orders)
	ret0, _ := ret[0].(map[string]lpslib.ChannelWeightDimensionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeightDimensionInfo indicates an expected call of GetChannelWeightDimensionInfo.
func (mr *MockLogisticsServiceMockRecorder) GetChannelWeightDimensionInfo(ctx, orders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeightDimensionInfo", reflect.TypeOf((*MockLogisticsService)(nil).GetChannelWeightDimensionInfo), ctx, orders)
}
