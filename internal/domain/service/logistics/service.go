package logistics

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type LogisticsService interface {
	GetChannelWeightDimensionInfo(ctx context.Context, orders []lpslib.OrderSplitLogisticsQuery) (map[string]lpslib.ChannelWeightDimensionInfo, fsserr.Error)
}

type LogisticsServiceImpl struct {
	logisticsInfoApi LogisticsInfoApi
}

func NewLogisticsServiceImpl(infoApi LogisticsInfoApi) *LogisticsServiceImpl {
	return &LogisticsServiceImpl{
		logisticsInfoApi: infoApi,
	}
}

func (l *LogisticsServiceImpl) GetChannelWeightDimensionInfo(ctx context.Context, orders []lpslib.OrderSplitLogisticsQuery) (map[string]lpslib.ChannelWeightDimensionInfo, fsserr.Error) {
	resp, err := l.logisticsInfoApi.GetAllChannelsWeightDim(ctx, orders)
	if err != nil {
		return nil, err
	}

	result := make(map[string]lpslib.ChannelWeightDimensionInfo)
	for _, channelData := range resp {
		result[channelData.UniqueID] = channelData
	}

	return result, nil
}
