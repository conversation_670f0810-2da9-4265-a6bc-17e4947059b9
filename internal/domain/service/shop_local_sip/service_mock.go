// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package shop_local_sip is a generated GoMock package.
package shop_local_sip

import (
	context "context"
	reflect "reflect"

	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	fsserr "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
	gomock "github.com/golang/mock/gomock"
)

// MockShopLocalSIPService is a mock of ShopLocalSIPService interface.
type MockShopLocalSIPService struct {
	ctrl     *gomock.Controller
	recorder *MockShopLocalSIPServiceMockRecorder
}

// MockShopLocalSIPServiceMockRecorder is the mock recorder for MockShopLocalSIPService.
type MockShopLocalSIPServiceMockRecorder struct {
	mock *MockShopLocalSIPService
}

// NewMockShopLocalSIPService creates a new mock instance.
func NewMockShopLocalSIPService(ctrl *gomock.Controller) *MockShopLocalSIPService {
	mock := &MockShopLocalSIPService{ctrl: ctrl}
	mock.recorder = &MockShopLocalSIPServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockShopLocalSIPService) EXPECT() *MockShopLocalSIPServiceMockRecorder {
	return m.recorder
}

// BatchGetShopLocalSIPInfo mocks base method.
func (m *MockShopLocalSIPService) BatchGetShopLocalSIPInfo(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopLocalSIPInfo, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetShopLocalSIPInfo", ctx, shopIDs)
	ret0, _ := ret[0].(map[uint64]entity.ShopLocalSIPInfo)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// BatchGetShopLocalSIPInfo indicates an expected call of BatchGetShopLocalSIPInfo.
func (mr *MockShopLocalSIPServiceMockRecorder) BatchGetShopLocalSIPInfo(ctx, shopIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetShopLocalSIPInfo", reflect.TypeOf((*MockShopLocalSIPService)(nil).BatchGetShopLocalSIPInfo), ctx, shopIDs)
}

// GetShopLocalSIPInfo mocks base method.
func (m *MockShopLocalSIPService) GetShopLocalSIPInfo(ctx context.Context, shopID uint64) (entity.ShopLocalSIPInfo, fsserr.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShopLocalSIPInfo", ctx, shopID)
	ret0, _ := ret[0].(entity.ShopLocalSIPInfo)
	ret1, _ := ret[1].(fsserr.Error)
	return ret0, ret1
}

// GetShopLocalSIPInfo indicates an expected call of GetShopLocalSIPInfo.
func (mr *MockShopLocalSIPServiceMockRecorder) GetShopLocalSIPInfo(ctx, shopID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShopLocalSIPInfo", reflect.TypeOf((*MockShopLocalSIPService)(nil).GetShopLocalSIPInfo), ctx, shopID)
}
