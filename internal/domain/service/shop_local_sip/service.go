package shop_local_sip

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ShopLocalSIPService interface {
	BatchGetShopLocalSIPInfo(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopLocalSIPInfo, fsserr.Error)
	GetShopLocalSIPInfo(ctx context.Context, shopID uint64) (entity.ShopLocalSIPInfo, fsserr.Error)
}

type ShopLocalSIPServiceImpl struct {
	spexClient        spexlib.SpexClient
	confAccessor      config.ConfAccessor
	shopLocalSipCache cache.MultiCache[uint64, entity.ShopLocalSIPInfo]
}

func NewShopLocalSIPServiceWithRedisClients(
	clients redishelper.GlobalRedisClients,
	spexClient spexlib.SpexClient,
	confAccessor config.ConfAccessor,
) (*ShopLocalSIPServiceImpl, error) {
	defaultClient, err := clients.GetRedisClusterByClusterName(redishelper.Default)
	if err != nil {
		return nil, err
	}

	shopLocalSipCache, err := mixed_cache.NewLruLayerCache[uint64, entity.ShopLocalSIPInfo](
		cache.ShopLocalSipCacheName, defaultClient, nil, nil)
	if err != nil {
		Logger.CtxLogErrorf(context.Background(), "failed to create shop local SIP cache: %v", err)
		return nil, err
	}
	return NewShopLocalSIPService(spexClient, confAccessor, shopLocalSipCache), nil
}

func NewShopLocalSIPService(
	spexClient spexlib.SpexClient,
	confAccessor config.ConfAccessor,
	shopLocalSipCache cache.MultiCache[uint64, entity.ShopLocalSIPInfo],
) *ShopLocalSIPServiceImpl {
	return &ShopLocalSIPServiceImpl{
		spexClient:        spexClient,
		confAccessor:      confAccessor,
		shopLocalSipCache: shopLocalSipCache,
	}
}

func (s *ShopLocalSIPServiceImpl) BatchGetShopLocalSIPInfo(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopLocalSIPInfo, fsserr.Error) {
	ret, err := cache.MultiLoadManyFromMap(ctx,
		s.shopLocalSipCache,
		shopIDs,
		s.batchGetShopLocalSIPInfo,
		cache.WithKeyConvertor[uint64, entity.ShopLocalSIPInfo](shopLocalSipInfoKey),
	)
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}
	return ret, nil
}

func (s *ShopLocalSIPServiceImpl) batchGetShopLocalSIPInfo(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopLocalSIPInfo, error) {
	shopIDList := make([]int64, 0, len(shopIDs))
	for _, shopID := range shopIDs {
		shopIDList = append(shopIDList, int64(shopID))
	}

	sipBasicList, err := s.spexClient.BatchGetShopSipBasic(ctx, shopIDList)
	if err != nil {
		return nil, err
	}

	primaryRegionSet := collection.NewSet[string]()
	for _, sipBasic := range sipBasicList {
		if sipBasic.GetIsSipAffiliated() {
			primaryRegionSet.Add(sipBasic.GetSipPrimaryRegion())
		}
	}

	primaryRegionToDummyBuyerID := make(map[string]typ.UserIdType)
	if primaryRegionSet.Size() > 0 {
		for _, region := range primaryRegionSet.ToSlice() {
			dummyBuyerID, err := s.spexClient.GetDummyBuyerId(ctx, region)
			if err != nil {
				return nil, err
			}
			primaryRegionToDummyBuyerID[region] = typ.UserIdType(dummyBuyerID.GetUserId())
		}
	}

	ret := make(map[uint64]entity.ShopLocalSIPInfo, len(shopIDList))
	for _, sipBasic := range sipBasicList {
		var dummyBuyerID typ.UserIdType
		if sipBasic.GetIsSipAffiliated() {
			dummyBuyerID = primaryRegionToDummyBuyerID[sipBasic.GetSipPrimaryRegion()]
		}

		ret[uint64(sipBasic.GetShopId())] = entity.ShopLocalSIPInfo{
			IsSIPAffiliated:  sipBasic.GetIsSipAffiliated(),
			SIPPrimaryRegion: sipBasic.GetSipPrimaryRegion(),
			DummyBuyerID:     dummyBuyerID,
		}
	}

	return ret, nil
}

func (s *ShopLocalSIPServiceImpl) GetShopLocalSIPInfo(ctx context.Context, shopID uint64) (entity.ShopLocalSIPInfo, fsserr.Error) {
	shopInfoMap, err := s.BatchGetShopLocalSIPInfo(ctx, []uint64{shopID})
	if err != nil {
		return entity.ShopLocalSIPInfo{}, fsserr.With(fsserr.SpexError, err)
	}

	sipInfo, exists := shopInfoMap[shopID]
	if !exists {
		return entity.ShopLocalSIPInfo{}, fsserr.New(fsserr.DataErr, "shop ID %d not found in SIP info response", shopID)
	}

	return sipInfo, nil
}

func shopLocalSipInfoKey(ctx context.Context, shopID uint64) string {
	return common.GenKeyWithRegion(ctx, ":", "shop_local_sip", strconv.FormatUint(shopID, 10))
}
