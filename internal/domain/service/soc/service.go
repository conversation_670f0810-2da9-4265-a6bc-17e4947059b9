package soc

//go:generate mockgen --build_flags=--mod=mod -source=service.go -destination=service_mock.go -package=soc -mock_names=SOCService=MockSOCService

import (
	"context"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/remote_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type SOCService interface {
	BatchCheckSOCStationIDServiceability(
		ctx context.Context, socLocations []string, buyerAddress entity.ItemGroupBuyerAddress,
	) (map[string]bool, fsserr.Error)
}

type SOCServiceImpl struct {
	socVersionCache cache.MultiCache[string, string]
	socServiceCache cache.MultiCache[ServiceabilityInfoCacheKey, bool]
}

func NewSOCService(clients redishelper.GlobalRedisClients) (*SOCServiceImpl, error) {
	socServiceCache, err := newSocServiceCache(clients)
	if err != nil {
		return nil, err
	}

	socVersionCache, err := newSocVersionCache(clients)
	if err != nil {
		return nil, err
	}
	return &SOCServiceImpl{
		socVersionCache: socVersionCache,
		socServiceCache: socServiceCache,
	}, nil
}

func newSocVersionCache(clients redishelper.GlobalRedisClients) (cache.MultiCache[string, string], error) {
	client, err := clients.GetRedisClusterByClusterName(redishelper.Static)
	if err != nil {
		return nil, err
	}
	socVersionCache, err := mixed_cache.NewStrLruLayerCache[string, string](
		cache.SocVersionCacheName,
		client,
		[]lru.InitOptions{},
		[]remote_cache.InitOptions[string]{
			remote_cache.WithoutNamespace[string](),
		},
	)
	if err != nil {
		return nil, err
	}
	return socVersionCache, nil
}

func newSocServiceCache(clients redishelper.GlobalRedisClients) (cache.MultiCache[ServiceabilityInfoCacheKey, bool], error) {
	client, err := clients.GetRedisClusterByClusterName(redishelper.Static)
	if err != nil {
		return nil, err
	}
	socServiceCache, err := mixed_cache.NewBoolLruLayerCache[ServiceabilityInfoCacheKey, bool](
		cache.SoCServiceabilityCacheName,
		client,
		[]lru.InitOptions{},
		[]remote_cache.InitOptions[bool]{
			remote_cache.WithoutNamespace[bool](),
		},
	)
	if err != nil {
		return nil, err
	}
	return socServiceCache, nil
}

func (s *SOCServiceImpl) BatchCheckSOCStationIDServiceability(
	ctx context.Context, socLocations []string, buyerAddress entity.ItemGroupBuyerAddress,
) (map[string]bool, fsserr.Error) {
	if buyerAddress.State == "" && buyerAddress.City == "" && buyerAddress.District == "" {
		Logger.CtxLogInfof(ctx, "buyer address is not available, skip SOC check")
		return make(map[string]bool), nil
	}

	versionID, ok := cache.Get(
		ctx,
		s.socVersionCache,
		envvar.GetCID(ctx),
		cache.WithKeyConvertor[string, string](getVersionIDCacheKey),
	)
	if !ok {
		return nil, fsserr.New(fsserr.InvalidSocID, "failed to get soc version")
	}

	keys := make([]ServiceabilityInfoCacheKey, 0, len(socLocations))
	for _, socLocation := range socLocations {
		keys = append(keys, ServiceabilityInfoCacheKey{
			VersionId: versionID,
			SocId:     socLocation,
			State:     buyerAddress.State,
			City:      buyerAddress.City,
			District:  buyerAddress.District,
		})
	}

	cacheResult := cache.MultiGetMany(
		ctx,
		s.socServiceCache,
		keys,
		cache.WithKeyConvertor[ServiceabilityInfoCacheKey, bool](func(ctx2 context.Context, socId ServiceabilityInfoCacheKey) string {
			return socId.Key()
		}),
		cache.WithTimeout[ServiceabilityInfoCacheKey, bool](time.Minute),
	)

	socStationIDToServiceability := make(map[string]bool, len(keys))
	for key, pass := range cacheResult {
		socStationIDToServiceability[key.SocId] = pass
	}
	return socStationIDToServiceability, nil
}

func getVersionIDCacheKey(ctx context.Context, region string) string {
	return strings.ToUpper("site_serviceable_area_location_version:" + region)
}
