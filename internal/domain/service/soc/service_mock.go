// Code generated manually. DO NOT EDIT.
// This is a manual mock for SOCService due to gomock issues with generics

package soc

import (
	"context"
	"reflect"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"github.com/golang/mock/gomock"
)

// MockSOCService is a mock of SOCService interface.
type MockSOCService struct {
	ctrl     *gomock.Controller
	recorder *MockSOCServiceMockRecorder
}

// MockSOCServiceMockRecorder is the mock recorder for MockSOCService.
type MockSOCServiceMockRecorder struct {
	mock *MockSOCService
}

// NewMockSOCService creates a new mock instance.
func NewMockSOCService(ctrl *gomock.Controller) *MockSOCService {
	mock := &MockSOCService{ctrl: ctrl}
	mock.recorder = &MockSOCServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSOCService) EXPECT() *MockSOCServiceMockRecorder {
	return m.recorder
}

// BatchCheckSOCStationIDServiceability mocks base method.
func (m *MockSOCService) BatchCheckSOCStationIDServiceability(ctx context.Context, socLocations []string, buyerAddress entity.ItemGroupBuyerAddress) (map[string]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckSOCStationIDServiceability", ctx, socLocations, buyerAddress)
	ret0, _ := ret[0].(map[string]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckSOCStationIDServiceability indicates an expected call of BatchCheckSOCStationIDServiceability.
func (mr *MockSOCServiceMockRecorder) BatchCheckSOCStationIDServiceability(ctx, socLocations, buyerAddress interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckSOCStationIDServiceability", reflect.TypeOf((*MockSOCService)(nil).BatchCheckSOCStationIDServiceability), ctx, socLocations, buyerAddress)
}
