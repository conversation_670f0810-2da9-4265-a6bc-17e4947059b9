package soc

import (
	"strings"
)

type (
	ServiceabilityInfoCacheKey struct {
		VersionId string
		SocId     string
		State     string
		City      string
		District  string
	}
)

func (key ServiceabilityInfoCacheKey) Key() string {
	return strings.ToUpper(
		strings.Join([]string{
			"site_serviceable_area_location",
			key.VersionId,
			key.SocId,
			key.State,
			key.City,
			key.District,
		}, ":"),
	)
}
