// Code generated by MockGen. DO NOT EDIT.
// Source: sales.go

// Package order is a generated GoMock package.
package order

import (
	context "context"
	reflect "reflect"

	entity "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockSalesOrdersCountService is a mock of SalesOrdersCountService interface.
type MockSalesOrdersCountService struct {
	ctrl     *gomock.Controller
	recorder *MockSalesOrdersCountServiceMockRecorder
}

// MockSalesOrdersCountServiceMockRecorder is the mock recorder for MockSalesOrdersCountService.
type MockSalesOrdersCountServiceMockRecorder struct {
	mock *MockSalesOrdersCountService
}

// NewMockSalesOrdersCountService creates a new mock instance.
func NewMockSalesOrdersCountService(ctrl *gomock.Controller) *MockSalesOrdersCountService {
	mock := &MockSalesOrdersCountService{ctrl: ctrl}
	mock.recorder = &MockSalesOrdersCountServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSalesOrdersCountService) EXPECT() *MockSalesOrdersCountServiceMockRecorder {
	return m.recorder
}

// CountShopSalesOrder mocks base method.
func (m *MockSalesOrdersCountService) CountShopSalesOrder(ctx context.Context, shopID, orderID uint64, oldOrderStatus, newOrderStatus int, detailFlag, createTime int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountShopSalesOrder", ctx, shopID, orderID, oldOrderStatus, newOrderStatus, detailFlag, createTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// CountShopSalesOrder indicates an expected call of CountShopSalesOrder.
func (mr *MockSalesOrdersCountServiceMockRecorder) CountShopSalesOrder(ctx, shopID, orderID, oldOrderStatus, newOrderStatus, detailFlag, createTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountShopSalesOrder", reflect.TypeOf((*MockSalesOrdersCountService)(nil).CountShopSalesOrder), ctx, shopID, orderID, oldOrderStatus, newOrderStatus, detailFlag, createTime)
}

// GetShopSalesOrdersCount mocks base method.
func (m *MockSalesOrdersCountService) GetShopSalesOrdersCount(ctx context.Context, shopID uint64, pastDays int) (entity.SalesOrdersCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShopSalesOrdersCount", ctx, shopID, pastDays)
	ret0, _ := ret[0].(entity.SalesOrdersCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShopSalesOrdersCount indicates an expected call of GetShopSalesOrdersCount.
func (mr *MockSalesOrdersCountServiceMockRecorder) GetShopSalesOrdersCount(ctx, shopID, pastDays interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShopSalesOrdersCount", reflect.TypeOf((*MockSalesOrdersCountService)(nil).GetShopSalesOrdersCount), ctx, shopID, pastDays)
}
