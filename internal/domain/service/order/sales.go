package order

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

type SalesOrdersCountService interface {
	CountShopSalesOrder(
		ctx context.Context,
		shopID uint64,
		orderID uint64,
		oldOrderStatus int,
		newOrderStatus int,
		detailFlag int64,
		createTime int64,
	) error
	GetShopSalesOrdersCount(ctx context.Context, shopID uint64, pastDays int) (entity.SalesOrdersCount, error)
}

func NewSalesOrdersCountServiceImpl(
	cacheStore CacheStore,
	confAccessor config.ConfAccessor,
	sellerTagService seller_tag.SellerTagService,
) *SalesOrdersCountServiceImpl {
	return &SalesOrdersCountServiceImpl{
		cacheStore:       cacheStore,
		confAccessor:     confAccessor,
		sellerTagService: sellerTagService,
	}
}

type SalesOrdersCountServiceImpl struct {
	cacheStore       CacheStore
	confAccessor     config.ConfAccessor
	sellerTagService seller_tag.SellerTagService
}

func (s *SalesOrdersCountServiceImpl) CountShopSalesOrder(ctx context.Context, shopID uint64, orderID uint64, oldOrderStatus int, newOrderStatus int, detailFlag int64, createTime int64) error {
	if newOrderStatus != int(order_order_info.Constant_ORDER_UNPAID) || oldOrderStatus == newOrderStatus {
		return nil
	}

	inGroup, err := s.isShopInAllocateBySalesOrdersGroup(ctx, shopID)
	if err != nil {
		return err
	}
	if !inGroup {
		return nil
	}

	isFulfilledByShopeeOrder := detailFlag&int64(order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_SHOPEE) != 0
	lock, lockVal, err := s.cacheStore.AcquireDistributedLock(ctx, shopID, isFulfilledByShopeeOrder, createTime)
	if err != nil {
		return err
	}
	defer s.cacheStore.ReleaseDistributedLock(ctx, lock, lockVal)
	count, err := s.cacheStore.IncreaseSalesOrderCount(ctx, shopID, isFulfilledByShopeeOrder, createTime, 1)
	if err != nil {
		return err
	}
	Logger.CtxLogInfof(ctx, "count sales order, shopID: %d, orderID: %d, create_time: %d, is_fulfilled_by_shopee: %v, current_count: %d",
		shopID, orderID, createTime, isFulfilledByShopeeOrder, count)

	return nil
}

func (s *SalesOrdersCountServiceImpl) GetShopSalesOrdersCount(ctx context.Context, shopID uint64, pastDays int) (entity.SalesOrdersCount, error) {
	now := time.Now().Unix()
	var salesOrderCount entity.SalesOrdersCount
	for days := pastDays; days >= 0; days-- {
		timestamp := now - 86400*int64(days)
		salesShopeeOrderCount, err := s.cacheStore.GetSalesOrderCount(ctx, shopID, true, timestamp)
		if err != nil && !redishelper.IsNil(err) {
			return entity.SalesOrdersCount{}, err
		}
		salesSellerOrderCount, err := s.cacheStore.GetSalesOrderCount(ctx, shopID, false, timestamp)
		if err != nil && !redishelper.IsNil(err) {
			return entity.SalesOrdersCount{}, err
		}
		salesOrderCount.ShopeeWarehouseOrdersCount += int(salesShopeeOrderCount)
		salesOrderCount.SellerWarehouseOrdersCount += int(salesSellerOrderCount)
	}
	Logger.CtxLogInfof(
		ctx, "get shop sales order count, shop_id: %d, past_days: %d, sales_order_count: %d",
		shopID, pastDays, salesOrderCount)
	return salesOrderCount, nil
}

func (s *SalesOrdersCountServiceImpl) isShopInAllocateBySalesOrdersGroup(ctx context.Context, shopID uint64) (bool, error) {
	var sellerTags []string
	for _, group := range s.confAccessor.GetAllocateWarehouseBySalesOrderGroups(ctx) {
		sellerTags = append(sellerTags, group.SellerTag)
	}
	if len(sellerTags) == 0 {
		return false, nil
	}
	shopIDToSellerTagToValue, err := s.sellerTagService.BatchGetShopSellerTag(ctx, []uint64{shopID}, sellerTags)
	if err != nil {
		return false, err
	}

	for _, tag := range sellerTags {
		if shopIDToSellerTagToValue[shopID][tag] {
			return true, nil
		}
	}
	return false, nil
}
