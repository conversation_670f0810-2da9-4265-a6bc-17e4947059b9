package entity

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type ItemInfo struct {
	ShopID                       int64
	ItemID                       typ.ItemIdType
	RawLogisticsInfo             []byte
	ChannelIDToLogistics         map[int]ItemLogisticsInfo // Channel ID string -> ItemLogisticsInfo
	SizeInfo                     ItemSizeInfo
	IsPreOrder                   bool
	EstimatedDays                int32
	IsVSKU                       bool
	ModelIDToModelDeliveryInfo   map[typ.ModelIdType]ModelDeliveryInfo
	ModelIDToMTSKUInfos          map[typ.ModelIdType][]MTSKUInfo
	LocalAttrModelId             int32
	DangerousGoods               uint32
	DangerousGoodsCategorization int32
	CategoryInfo                 ItemCategoryInfo
	PriceInfo                    ItemPriceInfo
	SellerSKU                    string
	ModelIDToSizeInfo            map[typ.ModelIdType]ItemSizeInfo
	ItemInstallationServiceInfo  ItemInstallationServiceInfo
	IsBOMItem                    bool
}

type ItemInstallationServiceInfo struct {
	IsItemEnrolled          bool
	InstallationChannelInfo []ItemInstallationLogisticsInfo
}

type ItemInstallationLogisticsInfo struct {
	ChannelID                int
	IsMaskingChannelEnrolled bool
}

type ModelDeliveryInfo struct {
	IsPreOrder    bool
	EstimatedDays int32
}

type MTSKUInfo struct {
	ItemID   typ.ItemIdType
	ModelID  typ.ModelIdType
	Quantity uint32
}

type ItemModelID struct {
	ItemID  typ.ItemIdType
	ModelID typ.ModelIdType
}

type ItemModelGroupID struct {
	GroupID int
	ItemID  typ.ItemIdType
	ModelID typ.ModelIdType
}

type ItemModelGroupQueryID struct {
	QueryID string
	GroupID int
	ItemID  typ.ItemIdType
	ModelID typ.ModelIdType
}

type ShopItemModelQtyID struct {
	GroupID  int
	ShopID   uint64
	ItemID   typ.ItemIdType
	ModelID  typ.ModelIdType
	Quantity int
}

type InstallationShopItemModelQtyID struct {
	ItemTotalAmount uint64
	ShopID          uint64
	ItemID          typ.ItemIdType
	ModelID         typ.ModelIdType
	Quantity        int
}

type ItemLogisticsInfo struct {
	ChannelID             int
	DefaultPrice          float64  `json:"default_price"`
	Enabled               bool     `json:"enabled"`
	CoverShippingFee      bool     `json:"cover_shipping_fee"`
	SizeID                *int32   `json:"sizeid"`
	Flag                  uint64   `json:"flag"`
	IsSellerChannel       *bool    `json:"is_seller_channel"`
	SellerLocationList    []string `json:"seller_location_list"`
	IsWmsChannel          bool     `json:"is_wms_channel"`
	WarehouseLocationList []string `json:"wms_location_list"`
}

type ItemSizeInfo struct {
	// Weight unit: g
	Weight uint64
	// Length, Width, Height unit: cm
	Length, Width, Height uint64
	// DimensionProduct: length * width * height unit: cm^3
	DimensionProduct uint64
}

type ItemPriceInfo struct {
	PriceMax, PriceMin int64
}

type ItemCategoryInfo struct {
	LocalCatIDs  []uint32
	GlobalCatIDs []uint32
}

type ShopItemID struct {
	ShopID uint64         `json:"shop_id"`
	ItemID typ.ItemIdType `json:"item_id"`
}

type ShopItemModelID struct {
	ShopID  uint64          `json:"shop_id"`
	ItemID  typ.ItemIdType  `json:"item_id"`
	ModelID typ.ModelIdType `json:"model_id"`
}

type ShopItemLocationGroup struct {
	ShopItemID
	LocationGroupID uint64 `json:"location_group_id"`
}

type Item struct {
	ShopID          uint64          `json:"shop_id"`
	ItemID          typ.ItemIdType  `json:"item_id"`
	ModelID         typ.ModelIdType `json:"model_id"`
	ItemTotalAmount uint64          `json:"item_total_amount"`
	Quantity        int             `json:"quantity"`
	IsPreviewItem   bool            `json:"is_preview_item"`
	IsBOMItem       bool            `json:"is_bom_item"`
}

type ItemGroup struct {
	GroupID      int
	COGS         int64 // both item promotion and rebate are in consideration.
	TotalPrice   int64 // item promotion is in consideration.
	Ordinary     bool  // indicate whether to treat the item group as ordinary item without any promotion consideration.
	IsBundleDeal bool
	Items        []Item
}

type ChannelItemGroup struct {
	ItemGroup
	Shippable        bool
	CoverShippingFee bool
	ShippingErrors   []string
}

type ItemModelWithError struct {
	ItemID  typ.ItemIdType  `json:"itemid"`
	ModelID typ.ModelIdType `json:"modelid"`
	Error   string          `json:"error"`
}

type ChannelItemValidationError struct {
	SKU   []ItemModelWithError `json:"sku"`
	Order string               `json:"order"`
}

type ChannelItemValidationResult struct {
	Shippable    bool                       `json:"shippable"`
	Validity     string                     `json:"validity"`
	DeclareGoods int                        `json:"declare_goods"`
	Errors       ChannelItemValidationError `json:"errors"`
}

type ItemWeightQuantity struct {
	ShopItemID
	Weight   float32 `json:"weight"`
	Quantity int     `json:"quantity"`
}

type ChargeableWeightQuery struct {
	ChannelID  int                  `json:"channel_id"`
	QueryID    uint64               `json:"query_id"`
	IsInflated bool                 `json:"is_inflated"`
	Items      []ItemWeightQuantity `json:"items"`
}

type ChargeableWeightOutcome struct {
	ChannelID        int     `json:"channel_id"`
	QueryID          uint64  `json:"query_id"` // shop ID
	IsInflated       bool    `json:"is_inflated"`
	ChargeableWeight float32 `json:"chargeable_weight"`
	ValidationWeight float32 `json:"validation_weight"`
	Error            string  `json:"error"`
}

type SellerPerformanceMetric struct {
	MetricID int    `json:"metricId"`
	ShopID   uint64 `json:"shopId"`
	Value    *int64 `json:"value,omitempty"`
}

type ItemTagID struct {
	ItemID typ.ItemIdType
	TagID  uint64
}

type ItemTag struct {
	ItemTagID
	IsTagged bool
}
