package entity

import (
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_action.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type ShopOrderStockInfo struct {
	ShopID     uint64
	ItemStocks []ShopOrderItemStock
}

type ShopOrderItemStock struct {
	ItemID         typ.ItemIdType
	ModelID        typ.ModelIdType
	OrderItemType  uint32
	BundleDealID   uint64
	StockLocations []StockLocation
}

type OrderStockInfo struct {
	Items []*OrderItemStockInfo
}

type OrderItemStockInfo struct {
	itemId              typ.ItemIdType
	modelId             typ.ModelIdType
	totalStock          uint32
	stockLocations      []*StockLocation
	orderItemType       order_action.Constant_OrderItemType
	bundleDealId        uint64
	pffInputPriceForced bool
	// if true, no stock location
	hasOldStockStructure bool
}

func (i *OrderItemStockInfo) TotalStock() uint32 {
	return i.totalStock
}

func (i *OrderItemStockInfo) StockLocations() []*StockLocation {
	return i.stockLocations
}

func (i *OrderItemStockInfo) SetStockLocations(stockLocations []*StockLocation) {
	i.stockLocations = stockLocations
}

func (i *OrderItemStockInfo) OrderItemType() order_action.Constant_OrderItemType {
	return i.orderItemType
}

func (i *OrderItemStockInfo) BundleDealId() uint64 {
	return i.bundleDealId
}

func (i *OrderItemStockInfo) PffInputPriceForced() bool {
	return i.pffInputPriceForced
}

func (i *OrderItemStockInfo) HasOldStockStructure() bool {
	return i.hasOldStockStructure
}

func (i *OrderItemStockInfo) ModelId() typ.ModelIdType {
	return i.modelId
}

func (i *OrderItemStockInfo) ItemId() typ.ItemIdType {
	return i.itemId
}

func NewOrderItemStockInfo(
	itemId typ.ItemIdType,
	modelId typ.ModelIdType,
	totalStock uint32,
	stockLocations []*StockLocation,
	orderItemType order_action.Constant_OrderItemType,
	bundleDealId uint64,
	pffInputPriceForced bool,
) *OrderItemStockInfo {
	return &OrderItemStockInfo{
		itemId:              itemId,
		modelId:             modelId,
		totalStock:          totalStock,
		stockLocations:      stockLocations,
		orderItemType:       orderItemType,
		bundleDealId:        bundleDealId,
		pffInputPriceForced: pffInputPriceForced,
	}
}

func NewOrderItemStockInfoForOldStructure(
	itemId typ.ItemIdType,
	modelId typ.ModelIdType,
	totalStock uint32,
	orderItemType order_action.Constant_OrderItemType,
	bundleDealId uint64,
	pffInputPriceForced bool,
) *OrderItemStockInfo {
	return &OrderItemStockInfo{
		itemId:               itemId,
		modelId:              modelId,
		totalStock:           totalStock,
		orderItemType:        orderItemType,
		bundleDealId:         bundleDealId,
		pffInputPriceForced:  pffInputPriceForced,
		hasOldStockStructure: true,
	}
}

type StockLocation struct {
	LocationID      string
	AvailableStock  uint32
	FulfillmentType constant.FulfilmentType
	SOCStationID    string
}
