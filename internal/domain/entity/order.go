package entity

type RequestInfo struct {
	RequestID         string
	QueryID           string
	Timestamp         int64
	ClientPlatform    uint32
	TzOffsetInMinutes *int64
}

type ShippingOrder struct {
	RequestInfo           RequestInfo
	Buyer                 BuyerInfo
	SelectedAddress       *AddressBase // used for non-PDP
	Location              Location     // used for PDP
	Shops                 []ShopInfo
	FilteringChannelIDs   []int // only non-empty slice will be considered.
	SelectedChannelId     int
	RecommendedChannelIds []int
	ItemGroups            []ItemGroup
	NeedEstimatedASF      bool
	AddCodFee             bool
	FulfillmentInfo       FulfillmentInfo
	BuyerZipcode          string
	NeedCOD               bool
	SplitCodFee           bool
	NeedSnapshotID        bool
	RosEligibility        bool
	RosCheckType          int32
	FulfillmentInfos      []FulfillmentInfo
	GroupType             uint32
	VasMetaInfo           VasMetaInfo
}

type VasMetaInfo struct {
	UseRealQuotedPrice bool
	DeviceName         string
}

type FulfillmentInfo struct {
	ManagedBySBS              bool
	WarehouseCode             string
	WarehouseAddressID        uint64
	OrderFulfillmentType      int
	AbleFallbackToSellerStock bool
	LocationId                string
}

type SalesOrdersCount struct {
	ShopeeWarehouseOrdersCount int
	SellerWarehouseOrdersCount int
}
