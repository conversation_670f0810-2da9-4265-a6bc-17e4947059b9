package entity

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type SBSShopInfo struct {
	ShopID          uint64
	IsSBSShop       bool
	WarehouseID     string
	Service         string
	ShipmentGroupID uint32
	ItemModelInfos  []SBSItemModelInfo
}

type SBSItemModelInfo struct {
	ItemID        typ.ItemIdType
	ModelID       typ.ModelIdType
	IsSBSItem     bool
	GroupShipment bool
}

type SBSShipmentGroup struct {
	ID          uint32
	Icon        string
	Description string
	Priority    int32
	NotGrouping bool
}

type LocalisedContent struct {
	Language         string
	IsPrimary        bool
	LocalisedContent string
}
