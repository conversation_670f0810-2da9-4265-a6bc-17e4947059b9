package entity

type Location struct {
	Country   string  `json:"country"`
	State     string  `json:"state"`
	City      string  `json:"city"`
	District  string  `json:"district"`
	Town      string  `json:"town"`
	Longitude float64 `json:"longitude"`
	Latitude  float64 `json:"latitude"`
}

// Use to carry user selected address info from client only to differentiate with full Address below.
type AddressBase struct {
	AddressID   uint64
	AddressType int
}

type Address struct {
	Location
	AddressID    uint64
	UserID       uint64
	AddressType  int
	Address      string
	FullAddress  string
	ZipCode      string
	StoreID      string // For TW convenience store address
	BranchID     uint64 // For Branch address (e.g. SG Ninja Van)
	Mtime        uint64
	Phone        string
	ExtInfo      AddressExtInfo
	DivisionInfo AddressDivisionInfo
	Status       int32
	LastUsedTime uint64
	IsWMS        bool
	IsUnused     bool // SPB-28873, CFS preselect unused CP to call SLS checkout/integration, but not return the address info to OPC
}

type AddressDivisionInfo struct {
	ID        uint64
	ErrCode   uint32
	ParentIDs []uint64
}

type AddressExtInfo struct {
	GeoInfo string
}

type ChannelAddress struct {
	Address
	UseDefaultAddress    bool
	OrderFulfillmentType int
	ChannelID            int
	Status               uint64
}

type GeoLocation struct {
	// Item from 0-5 with 5 = most precise
	Precision uint8
	Latitude  float64
	Longitude float64
}

type AddressWithGeo struct {
	GeoLocation GeoLocation
	State       string
	City        string
	District    string
}
