package entity

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_address_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type ShopDisplayChannelData struct {
	RetCode      int   `json:"retcode"`
	Guarantee    []int `json:"guarantee"`
	NonGuarantee []int `json:"non_guarantee"`
	Enabled      []int `json:"enabled"`
	Disabled     []int `json:"disabled"`
}

type RawWarehouseDisplayChannelData struct {
	WarehouseID      string `json:"whs_id"`
	Region           string `json:"region"`
	ChannelName      string `json:"channel_name"`
	ChannelID        int    `json:"channel_id"`
	ChannelStatus    int    `json:"channel_status"`
	ChannelCodStatus int    `json:"channel_cod_status"`
}

type WarehouseDisplayChannelData struct {
	Enabled    []int
	CodEnabled []int
}

type WarehouseChannel struct {
	ChannelID            int      `json:"channel_id"`
	ChannelStatus        int      `json:"channel_status"`
	IsMask               bool     `json:"is_mask"`
	FulfilmentChannelIDs []uint32 `json:"fulfillment_channel_ids"`
}

type WarehouseChannelMappingInfo struct {
	WarehouseID string             `json:"whs_id"`
	ChannelList []WarehouseChannel `json:"channel_list"`
	Errcode     int
}

type ShopInfo struct {
	ShopID   uint64
	SellerID uint64
}

type ShopChannelLogisticsInfo struct {
	CODEnabled          bool   `json:"cod_enabled"`
	CODToggleVisible    bool   `json:"cod_toggle_visible"`
	CODWhitelistEnabled bool   `json:"cod_whitelist_enabled"`
	Enabled             bool   `json:"enabled"`
	DefaultSizeID       *int32 `json:"default_sizeid"`
}

type ShopLogisticsInfo struct {
	PreferredChannel uint32                              `json:"preferred_channel"`
	Channels         map[string]ShopChannelLogisticsInfo `json:"channels"`
}

type ShopWeightInfo struct {
	ShopID           uint64
	ChargeableWeight float32
}

type ShopAttributeInfo struct {
	ShopID                  uint64
	HasShippingFeeGroup     bool
	ShippingFeeGroupID      int
	HasChannelPriorityGroup bool
	ChannelPriorityGroupID  int
}

type ShopOnboardingRecord struct {
	TaxID             string
	StateRegistration string
}

type ShopFulfilmentChannelInfo struct {
	EnabledFulfilmentChannelIDs []int
}

type ShopDetailInfo struct {
	ShopID             uint64
	UserID             typ.UserIdType
	IsSIPPrimary       bool
	IsSIPAffiliated    bool
	IsShipFromOverseas bool
}

type ShopChannelServiceableUnit struct {
	ShopID    uint64
	SellerID  uint64
	AddressID uint64
	ChannelID int
}

type ShopAllChannelServiceableUnit struct {
	ShopID    uint64
	SellerID  uint64
	AddressID uint64
}

type ShopChannelServiceableQuery struct {
	ShopID      uint64 `json:"shop_id"`
	UserID      uint64 `json:"user_id"`
	AddressID   uint64 `json:"address_id"`
	AddressType int32  `json:"address_type"`
	Sequence    string `json:"sequence"`
}

type ShopChannelServiceableResult struct {
	Sequence     string `json:"sequence"`
	Retcode      int    `json:"retcode"`
	ChannelID    int    `json:"channel_id"`
	CanPickup    int    `json:"can_pickup"`
	CanCodPickup int    `json:"can_cod_pickup"`
}

type ShopWarehouse struct {
	ShopID     uint64
	LocationID string
	AddressID  uint64
	Region     string
}

func (s ShopWarehouse) IsLocalWarehouse(ctx context.Context) bool {
	return strings.EqualFold(s.Region, envvar.GetCID(ctx))
}

type ShopLocalSIPInfo struct {
	IsSIPAffiliated  bool
	SIPPrimaryRegion string
	DummyBuyerID     typ.UserIdType
}

type ShopWarehouseFlag struct {
	WarehouseFlag            int
	CanUseWarehouseForPickup bool
	CanUseWarehouseForReturn bool
}

func (s ShopWarehouseFlag) IsMultiWarehouse() bool {
	return s.WarehouseFlag == int(seller_seller_address_core.Constant_SHOP_WAREHOUSE_LOCAL_DEFAULT)
}

func (s ShopWarehouseFlag) Is3PFWhitelist() bool {
	return (s.WarehouseFlag == int(seller_seller_address_core.Constant_SHOP_WAREHOUSE_3PF_ONLY) ||
		s.WarehouseFlag == int(seller_seller_address_core.Constant_SHOP_WAREHOUSE_3PF_HYBRID)) && s.CanUseWarehouseForPickup
}

// ShopChannel follow marketplace_logistics_shop_channels.Channel
type ShopChannel struct {
	ChannelId                 uint64
	NotSupportCounterfeitItem bool
}

type ShopWhitelistDetail struct {
	shopId uint64
	flag   seller_seller_address_core.Constant_ShopWarehouseFlag
	// this shop can use its own WH as pickup address
	canUseWarehouseForPickup bool
}

func NewShopWhitelistDetail(
	shopId uint64,
	flag seller_seller_address_core.Constant_ShopWarehouseFlag,
	canUseWarehouseForPickup bool,
) ShopWhitelistDetail {
	return ShopWhitelistDetail{
		shopId:                   shopId,
		flag:                     flag,
		canUseWarehouseForPickup: canUseWarehouseForPickup,
	}
}

const (
	WarehouseFlagUnknown seller_seller_address_core.Constant_ShopWarehouseFlag = 0
)

func NewEmptyShopWhitelistDetail(shopId uint64) ShopWhitelistDetail {
	return ShopWhitelistDetail{
		shopId:                   shopId,
		flag:                     WarehouseFlagUnknown,
		canUseWarehouseForPickup: false,
	}
}

func (d ShopWhitelistDetail) ShopId() uint64 {
	return d.shopId
}

func (d ShopWhitelistDetail) Flag() seller_seller_address_core.Constant_ShopWarehouseFlag {
	return d.flag
}

func (d ShopWhitelistDetail) CanUseWarehouseForPickup() bool {
	return d.canUseWarehouseForPickup
}

func (d ShopWhitelistDetail) is3PFWhitelist() bool {
	return d.flag == seller_seller_address_core.Constant_SHOP_WAREHOUSE_3PF_ONLY || d.flag == seller_seller_address_core.Constant_SHOP_WAREHOUSE_3PF_HYBRID
}

// Is3PFWhitelistAndCanUseWarehouseForPickup special logic used in release phase of 3PF feature,
// only applicable for IGS API batch_get_seller_wh_priority.
// During this phase, some shops are whitelisted but cannot use their own WH as pickup address.
// So we need to filter these shops out.
func (d ShopWhitelistDetail) Is3PFWhitelistAndCanUseWarehouseForPickup() bool {
	return d.is3PFWhitelist() && d.canUseWarehouseForPickup
}
