package entity

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
)

func GetSellerWarehouseId(ctx context.Context) string {
	return GetSellerWarehouseIdByRegion(envvar.GetCID(ctx))
}

func GetSellerWarehouseIdByRegion(region string) string {
	return strings.ToUpper(region) + "Z"
}

func IsSellerWarehouseId(warehouseId string) bool {
	return strings.HasSuffix(strings.ToUpper(warehouseId), "Z")
}
