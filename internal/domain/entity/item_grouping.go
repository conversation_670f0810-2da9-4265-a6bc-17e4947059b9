package entity

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type ItemGroupBuyerAddress struct {
	State          string
	City           string
	District       string
	BuyerAddressID uint64
	Required       bool
}

type ItemGroupShippingOrder struct {
	ShopID                          uint64
	PFFWhitelist                    bool
	PFFCheckoutImprovementWhitelist bool
	FulfilledByShopee               bool
	WarehouseLocation               string
	CBOption                        int32
	ServiceByShopee                 bool
	DecoupleShop                    bool
	SBSIcon                         *SbsIcon
	IsShipFromOversea               bool
	ShopWHPriorityInfo              WHPriorityInfo
	Items                           []ItemGroupShippingOrderItem
	UserId                          typ.UserIdType
	RequireAdvanceBookingOrder      bool
}

type ItemGroupShippingOrderItem struct {
	ItemId   typ.ItemIdType
	ModelId  typ.ModelIdType
	Quantity int32
	Stock    int32 // stock value from listing -> oss, it might not equals to sumStock because of floating reserve stock
	SumStock int32 // sum of all stock of fulfilment source
	ItemType int32
	// deprecated
	SlashInfo           SlashOrderItemInfo
	OfferInfo           OfferOrderItemInfo
	MembershipOfferInfo MembershipOfferOrderItemInfo
	AddOnDealInfo       AddOnDealOrderItemInfo
	BundleDealInfo      BundleDealOrderItemInfo
	IsSbsItem           bool
	SbsIcon             SbsIcon
	// condition to satisfy
	//  - item is from a PFF shop;
	//  - item has non-0 tax, (input_price != final_price, input_price+tax=final_price);
	//  - item has wms_stock;
	//  - is in ID region (by now);
	PffInputPriceForced bool
	LocalSipMtskuInfo   *LocalSipMtskuInfo
	HasSellerStock      bool
	ItemFulfillmentList []*ItemFulfillment
}

func (item *ItemGroupShippingOrderItem) IsDeductedItem() bool {
	return item != nil && item.LocalSipMtskuInfo != nil && item.LocalSipMtskuInfo.HasDeduct == true
}

func (item *ItemGroupShippingOrderItem) IsItemInPrimaryOrder() bool {
	return item != nil && item.LocalSipMtskuInfo != nil
}

type SbsIcon struct {
	Link        string
	Description string
}

type WHPriorityInfo struct {
	IsMWHSellerWhitelisted bool
	Is3PFSellerWhitelisted bool
	// first element has the highest priority
	PrioritizedList []WHInfo
}

func (w WHPriorityInfo) GetIsMWHSellerWhitelisted() bool {
	return w.IsMWHSellerWhitelisted
}

func (w WHPriorityInfo) GetIs3PFSellerWhitelisted() bool {
	return w.Is3PFSellerWhitelisted
}

func (w WHPriorityInfo) GetPrioritizedList() []WHInfo {
	return w.PrioritizedList
}

func NewEmptyWHPriorityInfo() WHPriorityInfo {
	return WHPriorityInfo{PrioritizedList: []WHInfo{}}
}

func NewMWHSellerWHPriorityInfo(isWhitelisted bool, whs []WHInfo) WHPriorityInfo {
	return WHPriorityInfo{IsMWHSellerWhitelisted: isWhitelisted, PrioritizedList: whs}
}

func New3PFWHPriorityInfo(isWhitelisted bool, whs []WHInfo) WHPriorityInfo {
	return WHPriorityInfo{Is3PFSellerWhitelisted: isWhitelisted, PrioritizedList: whs}
}

type WHInfo struct {
	LocationId string
	AddressId  uint64
}

type SlashOrderItemInfo struct {
	SlashPriceActivityID uint64
}

type OfferOrderItemInfo struct {
	OfferID uint64
}

type AddOnDealOrderItemInfo struct {
	GroupID        uint64
	AddOnDealID    uint64
	IsAddOnSubItem bool
}

type MembershipOfferOrderItemInfo struct {
	OfferID uint64
}

type LocalSipMtskuInfo struct {
	PPromotionType        uint32
	PPromotionId          uint64
	ProductLocationId     string
	ProductFulfilmentType constant.FulfilmentType
	HasDeduct             bool
}

type ItemFulfillment struct {
	// FulfillmentFlag int32 // deprecated,after PFF phase 2
	Stock int32 // sum of all location stocks under a single fulfilment source
	// only one location so far,after multi-warehouse go live, there will be more than one location
	StockLocation   ItemStockLocationList
	FulfillmentType constant.FulfilmentType // base on "StockLocation" in this entity, it might be wrong if it is old stock structure
}

type ItemStockLocation struct {
	LocationId      string
	AvailableStock  int32
	FulfillmentType constant.FulfilmentType // item.price_stock.StockFulfilmentMainType
}

type ItemStockLocationList []*ItemStockLocation

func (l ItemStockLocationList) Stock() int {
	var sum int
	for _, loc := range l {
		sum += int(loc.AvailableStock)
	}
	return sum
}

type BundleDealOrderItemInfo struct {
	BundleDealID uint64
}

type ItemGroupResult struct {
	GroupedOrders          []ItemGroupOrder
	GroupedOrderGroupInfos []GroupedOrderGroupInfo
}

type ItemGroupOrder struct {
	GroupedOrders    []ItemGroupResultOrder
	FulfillmentInfo  ItemGroupFulfilmentInfo
	FulfillmentInfos []ItemGroupFulfilmentInfo
}

type ItemGroupFulfilmentInfo struct {
	GroupID                   uint32
	AddressID                 uint64
	Source                    string
	ManagedBySBS              bool
	Flag                      uint32
	GroupIcon                 string
	GroupDesc                 string
	Priority                  int32
	ShipmentGroupID           uint32
	GroupShipment             bool
	IsAdvanceBookingOrder     bool
	OrderFulfilmentType       int
	AbleFallbackToSellerStock bool // deprecate later after cache order phase 2
	LocationId                string
}

type ItemGroupResultOrder struct {
	ShopID       uint64
	PFFWhitelist bool
	Items        []ItemGroupResultItem
}

type ItemGroupResultItem struct {
	ItemID      typ.ItemIdType
	ModelID     typ.ModelIdType
	Quantity    uint32
	ItemGroupID uint64
	AddOnDealID uint64
	Source      string
}

type GroupedOrderGroupInfo struct {
	GroupedOrderIDs []uint32
	GroupType       uint32
}

type FeatureFlag uint64

func (f FeatureFlag) EnableChoicePhaseTwo() bool {
	return f&ChoicePhaseTwoFeatureFlag > 0
}

const (
	ChoicePhaseTwoFeatureFlag = 1 << 2
)
