package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/dto"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// MockItemGrouper 用于测试的 mock ItemGrouper
type MockItemGrouper struct {
	skipOrderSplitMergeReceived bool
}

func (m *MockItemGrouper) GroupItems(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
	buyerInfo group_entity.BuyerInfo,
	rules *group_entity.GroupingRules,
	skipSplitByWeightDimension bool,
	skipOrderSplitMerge bool,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	// 记录是否收到了 skipOrderSplitMerge 参数
	m.skipOrderSplitMergeReceived = skipOrderSplitMerge

	// 返回输入的订单（简化处理）
	return shippingOrders, nil
}

func TestScenarioHandling(t *testing.T) {
	tests := []struct {
		name                        string
		scenario                    fss_proto.Constant_Scenario
		expectedSkipOrderSplitMerge bool
	}{
		{
			name:                        "PDP scenario should skip order split/merge",
			scenario:                    fss_proto.Constant_ScenarioPDP,
			expectedSkipOrderSplitMerge: true,
		},
		{
			name:                        "ItemCard scenario should skip order split/merge",
			scenario:                    fss_proto.Constant_ScenarioItemCard,
			expectedSkipOrderSplitMerge: true,
		},
		{
			name:                        "Checkout scenario should not skip order split/merge",
			scenario:                    fss_proto.Constant_ScenarioCheckout,
			expectedSkipOrderSplitMerge: false,
		},
		{
			name:                        "Default scenario should not skip order split/merge",
			scenario:                    fss_proto.Constant_ScenarioDefault,
			expectedSkipOrderSplitMerge: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建 mock ItemGrouper
			mockGrouper := &MockItemGrouper{}

			// 创建测试请求
			req := &fss_proto.GroupItemsRequest{
				BuyerUserId: 12345,
				BuyerAddress: &fss_proto.BuyerAddress{
					State:          "SG",
					City:           "Singapore",
					District:       "Central",
					BuyerAddressId: 67890,
					Required:       true,
				},
				Items: []*fss_proto.SourcingItem{
					{
						QueryId:                    "query_1",
						ShopId:                     111,
						ItemId:                     222,
						ModelId:                    333,
						Quantity:                   1,
						RequireAdvanceBookingOrder: false,
						FulfilmentLocations: []*fss_proto.StockLocation{
							{
								Source:          "SG_WH_001",
								AvailableStock:  100,
								FulfilmentType:  int32(constant.FulfilmentTypeShopee),
								EnabledChannels: []int64{101, 102},
							},
						},
					},
				},
				FeatureOption: &fss_proto.FeatureOption{
					IsGroupBuy:                 false,
					SingleSourceOnly:           false,
					SkipSplitByWeightDimension: false,
				},
				Scenario: tt.scenario,
			}

			// 转换 protobuf 请求为 DTO
			dtoReq, err := dto.ConvertPBToGroupItemsRequest(req)
			require.NoError(t, err)

			// 验证 Scenario 转换是否正确
			expectedScenario := dto.Scenario(tt.scenario)
			assert.Equal(t, expectedScenario, dtoReq.Scenario)

			// 验证 IsPDPOrItemCard 方法
			assert.Equal(t, tt.expectedSkipOrderSplitMerge, dtoReq.Scenario.IsPDPOrItemCard())

			// 直接测试 DTO 转换和逻辑
			ctx := context.Background()

			// 验证 skipOrderSplitMerge 逻辑
			skipOrderSplitMerge := dtoReq.Scenario.IsPDPOrItemCard()
			assert.Equal(t, tt.expectedSkipOrderSplitMerge, skipOrderSplitMerge,
				"skipOrderSplitMerge should be %v for scenario %v", tt.expectedSkipOrderSplitMerge, tt.scenario)

			// 测试 mock ItemGrouper 的调用
			shippingOrders := []group_entity.ShippingOrder{
				{
					Items: []group_entity.ShippingOrderItem{
						{
							QueryId: "query_1",
							ShopInfo: group_entity.ShopInfo{
								ShopID:   111,
								SellerID: typ.UserIdType(999),
							},
							ItemID:   typ.ItemIdType(222),
							ModelID:  typ.ModelIdType(333),
							Quantity: 1,
						},
					},
				},
			}

			buyerInfo := group_entity.BuyerInfo{
				UserID: typ.UserIdType(12345),
			}

			// 调用 mock ItemGrouper 来验证参数传递
			_, err = mockGrouper.GroupItems(ctx, shippingOrders, buyerInfo, nil, false, skipOrderSplitMerge)
			require.NoError(t, err)

			// 验证 skipOrderSplitMerge 参数是否正确传递
			assert.Equal(t, tt.expectedSkipOrderSplitMerge, mockGrouper.skipOrderSplitMergeReceived,
				"skipOrderSplitMerge parameter should be %v for scenario %v", tt.expectedSkipOrderSplitMerge, tt.scenario)
		})
	}
}

func TestScenarioEnumMethods(t *testing.T) {
	tests := []struct {
		scenario                dto.Scenario
		expectedString          string
		expectedIsPDPOrItemCard bool
	}{
		{dto.ScenarioDefault, "default", false},
		{dto.ScenarioItemCard, "item_card", true},
		{dto.ScenarioPDP, "pdp", true},
		{dto.ScenarioCheckout, "checkout", false},
	}

	for _, tt := range tests {
		t.Run(tt.expectedString, func(t *testing.T) {
			assert.Equal(t, tt.expectedString, tt.scenario.String())
			assert.Equal(t, tt.expectedIsPDPOrItemCard, tt.scenario.IsPDPOrItemCard())
		})
	}
}
