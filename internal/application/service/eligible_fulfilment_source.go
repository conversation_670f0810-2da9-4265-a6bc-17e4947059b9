package service

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_action.pb"

	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/dto"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

func (s *ItemGroupingServiceImpl) GetEligibleFulfilmentSourceUseCase(ctx context.Context, req *fss_proto.GetEligibleFulfilmentSourceRequest) ([]*fss_proto.ResponseItemInfo, fsserr.Error) {
	// 转换 protobuf 请求为 DTO
	dtoReq, err := dto.ConvertPBToGetEligibleFulfilmentSourceRequest(req)
	if err != nil {
		return nil, err
	}

	// 参数验证
	groupOrders := convertEligibleSourcesRequestToGroupOrder(dtoReq)
	buyerAddress := convertItemGroupBuyerAddress(dtoReq.BuyerAddress)
	var groupOrderList []entity.ItemGroupShippingOrder
	var err2 error
	groupOrderList, _, err2 = s.getFulfillmentStock(ctx, dtoReq.BuyerUserId, groupOrders, false, buyerAddress)
	if err2 != nil {
		return nil, fsserr.New(fsserr.DataErr, "get fulfillment stock failed: %v", err2)
	}
	itemIDToSOCFulfilmentType := getItemIDToSOCFulfilmentType(dtoReq)
	itemIDToCacheItemInfo := make(map[uint64]dto.CacheItemInfo)
	for _, groupOrder := range groupOrderList {
		for _, item := range groupOrder.Items {
			sources := getCacheOrderEligibleSources(ctx, item, itemIDToSOCFulfilmentType[item.ItemId])
			itemIDToCacheItemInfo[item.ItemId] = dto.CacheItemInfo{
				ItemID:                   item.ItemId,
				ModelID:                  item.ModelId,
				Quantity:                 item.Quantity,
				ShopID:                   groupOrder.ShopID,
				CacheItemEligibleSources: sources,
			}
		}
	}
	s.setFallBackFulfilmentInfo(ctx, itemIDToCacheItemInfo, buyerAddress)
	for _, item := range dtoReq.RequestItemInfos {
		if s.isProvidedItemSOCIDValid(ctx, buyerAddress, item.FilteredSocId) {
			eligibleSources := itemIDToCacheItemInfo[item.ItemId]
			eligibleSources.CacheItemEligibleSources.SOCFulfilmentInfo = dto.FulfilmentInfo{Source: item.FilteredSocId, FulfilmentType: item.OrderStockFulfilmentType}
			itemIDToCacheItemInfo[item.ItemId] = eligibleSources
		}
	}

	responseItemInfos := parseResponseItemInfo(itemIDToCacheItemInfo)
	for _, responseItemInfo := range responseItemInfos {
		if len(responseItemInfo.EligibleFulfilmentSources) == 0 {
			return nil, fsserr.NewOutOfStockError(fsserr.OutOfStockErrorData{
				ItemId:             responseItemInfo.ItemId,
				IsPackagePromotion: false,
			})
		}
	}

	// 转换 DTO 响应为 protobuf 响应
	dtoResp := &dto.GetEligibleFulfilmentSourceResponse{
		ResponseItemInfo: responseItemInfos,
	}

	pbResponse, err := dto.ConvertGetEligibleFulfilmentSourceResponseToPB(dtoResp)
	if err != nil {
		return nil, err
	}

	return pbResponse.ResponseItemInfo, nil
}

func (s *ItemGroupingServiceImpl) getFulfillmentStock(
	ctx context.Context,
	buyerId typ.UserIdType,
	orders []entity.ItemGroupShippingOrder,
	isGroupBuy bool,
	address entity.ItemGroupBuyerAddress,
) ([]entity.ItemGroupShippingOrder, bool, error) {
	orderStockInfos, allItemsDeducted, err := s.stockService.GetStock(ctx, orders, buyerId, isGroupBuy)
	if err != nil {
		return nil, false, fmt.Errorf("calculate stock: %w", err)
	}

	haveMultiWh := false
	if !allItemsDeducted {
		orders, haveMultiWh, err = s.addOrderStockInfosToOrders(ctx, orderStockInfos, orders, address)
		if err != nil {
			return nil, false, fmt.Errorf("add stock info to orders: %w", err)
		}
	}

	orders = constructFulfilmentStockForDeductedOrders(ctx, orders)
	return orders, haveMultiWh, nil
}

// This function assumes that the sorting order of item_id + model_id in
// `orderStockInfo` (from stock service) is the same as in `orders` (from client request).
func (s *ItemGroupingServiceImpl) addOrderStockInfosToOrders(
	ctx context.Context,
	orderStockInfo []*entity.OrderStockInfo,
	orders []entity.ItemGroupShippingOrder,
	address entity.ItemGroupBuyerAddress,
) (ordersWithStock []entity.ItemGroupShippingOrder, haveMultiWH bool, err error) {
	if len(orders) != len(orderStockInfo) {
		Logger.CtxLogErrorf(ctx, "the number of request orders is not equal to the number of response orders!")
		return nil, false, fmt.Errorf(
			"the number of request orders is not equal to the number of response orders, len(requestOrder)=%d, len(responseOrder)=%d",
			len(orders),
			len(orderStockInfo),
		)
	}

	s.filterOutNonRequireSOCLocations(orders, orderStockInfo)
	orderStockInfo, err = s.filterOutUnserviceableSOCLocations(ctx, orderStockInfo, address)

	haveMultiWh := false
	for index, order := range orders {
		// the order of the orders in request is the same as the order in response
		orderStock := orderStockInfo[index]

		skuIdToItemStockMap := make(map[string]*entity.OrderItemStockInfo)
		//  the order of the items in response is the same as the order in request
		for i, itemStock := range orderStock.Items {
			skuId := getSkuId(itemStock.ItemId(), itemStock.ModelId(), i)
			skuIdToItemStockMap[skuId] = itemStock
		}

		itemsNeedOssStock := make([]entity.ItemGroupShippingOrderItem, 0)

		// for deducted items, we don't care about stock
		for _, item := range order.Items {
			if item.IsDeductedItem() {
				continue
			}
			itemsNeedOssStock = append(itemsNeedOssStock, item)
		}

		for i, item := range itemsNeedOssStock {
			skuId := getSkuId(item.ItemId, item.ModelId, i)
			itemStock, ok := skuIdToItemStockMap[skuId]
			if !ok {
				Logger.CtxLogErrorf(ctx, "can not find the corresponding sku in stock response, skuId: %s", skuId)
				return nil, false, errors.New("can not find the corresponding sku in stock response")
			}

			item.Stock = int32(itemStock.TotalStock())
			item.ItemType = int32(itemStock.OrderItemType())
			item.PffInputPriceForced = itemStock.PffInputPriceForced()

			itemFulfillmentList := make([]*entity.ItemFulfillment, 0)
			if len(itemStock.StockLocations()) == 0 {
				item.SumStock = int32(itemStock.TotalStock())
				itemFulfill := &entity.ItemFulfillment{}
				itemFulfill.FulfillmentType, itemFulfill.Stock = getGroupFulfilmentTypeAndStock(
					ctx,
					itemStock.HasOldStockStructure(),
					int32(itemStock.TotalStock()),
					nil,
					item.ItemId,
					order.PFFWhitelist,
				)
				itemFulfillmentList = append(itemFulfillmentList, itemFulfill)
			} else {
				fulfillmentTypeToItemStockLocationsMap := make(map[constant.FulfilmentType][]*entity.ItemStockLocation)
				for _, stockLocation := range itemStock.StockLocations() {
					location := &entity.ItemStockLocation{
						LocationId:      stockLocation.LocationID,
						AvailableStock:  int32(stockLocation.AvailableStock),
						FulfillmentType: stockLocation.FulfillmentType,
					}
					fulfillmentTypeToItemStockLocationsMap[stockLocation.FulfillmentType] = append(
						fulfillmentTypeToItemStockLocationsMap[stockLocation.FulfillmentType],
						location,
					)
				}

				for _, locationList := range fulfillmentTypeToItemStockLocationsMap {
					if len(locationList) > 1 {
						haveMultiWh = true
					}

					itemFulfill := &entity.ItemFulfillment{
						StockLocation: locationList,
					}

					// legacy logic: some fields are computed manually to be sure
					itemFulfill.FulfillmentType, itemFulfill.Stock = getGroupFulfilmentTypeAndStock(
						ctx,
						itemStock.HasOldStockStructure(),
						int32(itemStock.TotalStock()),
						locationList,
						item.ItemId,
						order.PFFWhitelist,
					)
					itemFulfillmentList = append(itemFulfillmentList, itemFulfill)
					item.SumStock += itemFulfill.Stock
				}
			}
			item.ItemFulfillmentList = itemFulfillmentList
			var hasSellerStock bool
			for _, fulfillment := range itemFulfillmentList {
				if fulfillment.FulfillmentType == constant.FulfilmentTypeCacheSeller {
					for _, location := range fulfillment.StockLocation {
						if location.LocationId == strings.ToUpper(entity.GetSellerWarehouseId(ctx)) && location.AvailableStock > 0 {
							hasSellerStock = true
							break
						}
					}
					if hasSellerStock {
						break
					}
				}
			}
			item.HasSellerStock = hasSellerStock

			if itemStock.BundleDealId() > 0 {
				item.BundleDealInfo = entity.BundleDealOrderItemInfo{
					BundleDealID: itemStock.BundleDealId(),
				}
			}
		}
	}
	return orders, haveMultiWh, nil
}

func (s *ItemGroupingServiceImpl) filterOutNonRequireSOCLocations(orders []entity.ItemGroupShippingOrder, orderStockInfos []*entity.OrderStockInfo) {
	for i, order := range orders {
		orderStock := orderStockInfos[i]
		if !order.RequireAdvanceBookingOrder {
			for _, item := range orderStock.Items {
				var stockLocations []*entity.StockLocation
				for _, location := range item.StockLocations() {
					if location.FulfillmentType.IsCacheStockFulfilmentType() && !order.RequireAdvanceBookingOrder {
						continue
					}
					stockLocations = append(stockLocations, location)
				}
				item.SetStockLocations(stockLocations)
			}
		}
	}
}

func (s *ItemGroupingServiceImpl) filterOutUnserviceableSOCLocations(
	ctx context.Context,
	orderStockInfos []*entity.OrderStockInfo,
	address entity.ItemGroupBuyerAddress,
) ([]*entity.OrderStockInfo, error) {
	socStationIDSet := collection.NewSet[string]()
	for _, order := range orderStockInfos {
		for _, item := range order.Items {
			originStockLocations := item.StockLocations()
			for _, location := range originStockLocations {
				if location.FulfillmentType.IsCacheStockFulfilmentType() {
					socStationIDSet.Add(location.SOCStationID)
				}
			}
		}
	}
	socStationIDList := socStationIDSet.ToSlice()
	if len(socStationIDList) == 0 {
		return orderStockInfos, nil
	}

	socStationIDToServiceability, err := s.socService.BatchCheckSOCStationIDServiceability(
		ctx,
		socStationIDList,
		address,
	)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "ServiceabilityInfoCache|BatchGetAddressInfoBySOCStationIDsError: %v", err)
	}

	for i, order := range orderStockInfos {
		for j, item := range order.Items {
			// cache order phase 2: prioritise to use cache_warehouse > cache_seller
			// sort the original stock locations list by fulfilment_type DESC and location_id ASC
			originStockLocations := item.StockLocations()
			sort.Slice(originStockLocations, func(i, j int) bool {
				loc1, loc2 := originStockLocations[i], originStockLocations[j]
				if loc1.AvailableStock == 0 {
					return false
				}
				if loc2.AvailableStock == 0 {
					return true
				}
				if loc1.FulfillmentType != loc2.FulfillmentType {
					return loc1.FulfillmentType > loc2.FulfillmentType
				}
				return strings.Compare(loc1.LocationID, loc2.LocationID) < 0
			})

			filteredStockLocations := make([]*entity.StockLocation, 0, len(originStockLocations))
			cacheStockAdded := false
			for _, location := range originStockLocations {
				if !location.FulfillmentType.IsCacheStockFulfilmentType() {
					filteredStockLocations = append(filteredStockLocations, location)
				} else if !cacheStockAdded {
					if err != nil || socStationIDToServiceability == nil {
						continue
					}
					if socStationIDToServiceability[location.LocationID] {
						filteredStockLocations = append(filteredStockLocations, location)
						cacheStockAdded = true
					}
				}
			}
			item.SetStockLocations(filteredStockLocations)
			order.Items[j] = item
		}
		orderStockInfos[i] = order
	}
	return orderStockInfos, nil
}

func (s *ItemGroupingServiceImpl) isProvidedItemSOCIDValid(
	ctx context.Context,
	address entity.ItemGroupBuyerAddress,
	socID string,
) bool {
	ret, err := s.socService.BatchCheckSOCStationIDServiceability(ctx, []string{socID}, address)
	if err != nil {
		return false
	}
	if len(ret) == 0 {
		return false
	}
	return ret[socID]
}

func (s *ItemGroupingServiceImpl) setFallBackFulfilmentInfo(ctx context.Context, itemIDToCacheItemInfo map[uint64]dto.CacheItemInfo, buyerAddress entity.ItemGroupBuyerAddress) {
	shopIDs := make([]uint64, 0)
	for _, infos := range itemIDToCacheItemInfo {
		shopIDs = append(shopIDs, infos.ShopID)
	}
	shopIDSet := collection.NewSetFromSlice(shopIDs)
	shopIDToFBSStatus := s.getShopToFBSStatus(ctx, shopIDSet.ToSlice())

	for itemID, cacheItemInfo := range itemIDToCacheItemInfo {
		if cacheItemInfo.CacheItemEligibleSources.SOCFulfilmentInfo.FulfilmentType == constant.FulfilmentTypeCacheSeller {
			cacheItemInfo.CacheItemEligibleSources.SelectedFallbackFulfilmentInfo = s.getSellerCacheOrderFallbackLocation(ctx, cacheItemInfo.CacheItemEligibleSources.CandidateFallbackFulfilmentInfos)
			itemIDToCacheItemInfo[itemID] = cacheItemInfo
		} else {
			if shopIDToFBSStatus[cacheItemInfo.ShopID] == true {
				cacheItemInfo.CacheItemEligibleSources.SelectedFallbackFulfilmentInfo = s.getShopeeCacheOrderFallbackLocationID(ctx, cacheItemInfo.CacheItemEligibleSources.CandidateFallbackFulfilmentInfos, buyerAddress)
				itemIDToCacheItemInfo[itemID] = cacheItemInfo
			} else {
				itemIDToCacheItemInfo[itemID] = cacheItemInfo
			}
		}
	}
}

func (s *ItemGroupingServiceImpl) getShopToFBSStatus(ctx context.Context, shopIDs []uint64) map[uint64]bool {
	shopIDToFBSTagStatus, err := s.sellerTagService.GetShopSellerTag(ctx, shopIDs, constant.ShopTagFBS)
	if err != nil {
		Logger.CtxLogDebugf(ctx, "getShopToFBSStatus|GetShopSellerTag failed, defaulting to all shops to non PFF FBS Tagged shops")
	}
	shopIDToSBSTagStatus, err := s.sellerTagService.GetShopSellerTag(ctx, shopIDs, constant.ShopTagSBS)
	if err != nil {
		Logger.CtxLogDebugf(ctx, "getShopToFBSStatus|GetShopSellerTag failed, defaulting to all shops to non FBS Tagged shops")
	}

	shopIDToFBSStatus := make(map[uint64]bool, len(shopIDs))
	for _, shopID := range shopIDs {
		if shopIDToFBSTagStatus[shopID] || shopIDToSBSTagStatus[shopID] {
			shopIDToFBSStatus[shopID] = true
		} else {
			shopIDToFBSStatus[shopID] = false
		}
	}

	return shopIDToFBSStatus
}

func (s *ItemGroupingServiceImpl) getSellerCacheOrderFallbackLocation(ctx context.Context, fulfilmentInfos []dto.FulfilmentInfo) dto.FulfilmentInfo {
	return fulfilmentInfos[0]
}

func (s *ItemGroupingServiceImpl) getShopeeCacheOrderFallbackLocationID(ctx context.Context, fulfilmentInfos []dto.FulfilmentInfo, address entity.ItemGroupBuyerAddress) dto.FulfilmentInfo {
	shopeeWarehouseIDs := make([]string, len(fulfilmentInfos))
	for i, fulfilmentInfo := range fulfilmentInfos {
		shopeeWarehouseIDs[i] = fulfilmentInfo.Source
	}

	shopeeWarehouseIDs, _ = s.warehousePriorityService.PrioritizeShopeeWarehouse(ctx, address, shopeeWarehouseIDs)
	if len(shopeeWarehouseIDs) != 0 {
		return dto.FulfilmentInfo{
			Source:         shopeeWarehouseIDs[0],
			FulfilmentType: constant.FulfilmentTypeShopee,
		}
	}

	return dto.FulfilmentInfo{
		Source:         "",
		FulfilmentType: constant.FulfilmentTypeShopee,
	}
}

func getGroupFulfilmentTypeAndStock(
	ctx context.Context,
	hasOldStockStructure bool,
	totalStock int32,
	stockLocations []*entity.ItemStockLocation,
	itemId uint64,
	isPffShop bool,
) (fulfilmentType constant.FulfilmentType, sumStock int32) {
	if hasOldStockStructure {
		// old item stock structure. here should be a single source only. fulfilment source stock = total item stock
		if isPffShop {
			return constant.FulfilmentTypeCacheSeller, totalStock
		} else {
			return constant.FulfilmentTypeUnknown, totalStock
		}
	}

	fulfilmentType = constant.FulfilmentTypeUnknown
	sumStock = 0
	for _, location := range stockLocations {
		if fulfilmentType != constant.FulfilmentTypeUnknown && fulfilmentType != location.FulfillmentType {
			// by right, all stocks under this item should have same fulfilment type
			Logger.CtxLogErrorf(ctx,
				"here are multiple fulfilment types under a single fulfilment group, item_id: %d, fulfilment_type: %d, location_fulfilment_type: %d",
				itemId, fulfilmentType, location.FulfillmentType)
		}
		fulfilmentType = location.FulfillmentType
		sumStock += int32(location.AvailableStock)
	}
	return fulfilmentType, sumStock
}

func convertEligibleSourcesRequestToGroupOrder(eligibleSourcesRequest *dto.GetEligibleFulfilmentSourceRequest) []entity.ItemGroupShippingOrder {
	newGroupOrderList := make([]entity.ItemGroupShippingOrder, 0)
	for _, requestItemInfo := range eligibleSourcesRequest.RequestItemInfos {
		newGroupOrder := entity.ItemGroupShippingOrder{
			ShopID:                     requestItemInfo.ShopId,
			Items:                      convertRequestItemToGroupOrderItem(requestItemInfo),
			RequireAdvanceBookingOrder: true,
		}

		newGroupOrderList = append(newGroupOrderList, newGroupOrder)
	}

	return newGroupOrderList
}

func convertRequestItemToGroupOrderItem(requestItemInfo *dto.RequestItemInfo) []entity.ItemGroupShippingOrderItem {
	groupItems := make([]entity.ItemGroupShippingOrderItem, 0)
	newGroupItem := convertSingleRequestItemToGroupOrder(requestItemInfo)
	groupItems = append(groupItems, newGroupItem)

	return groupItems
}

func convertSingleRequestItemToGroupOrder(requestItemInfo *dto.RequestItemInfo) entity.ItemGroupShippingOrderItem {
	newGroupItem := entity.ItemGroupShippingOrderItem{
		ItemId:   requestItemInfo.ItemId,
		ModelId:  requestItemInfo.ModelId,
		Quantity: int32(requestItemInfo.Quantity),
	}
	return newGroupItem
}

func convertItemGroupBuyerAddress(address *dto.BuyerAddress) entity.ItemGroupBuyerAddress {
	if address == nil {
		return entity.ItemGroupBuyerAddress{}
	}
	return entity.ItemGroupBuyerAddress{
		State:          address.State,
		City:           address.City,
		District:       address.District,
		BuyerAddressID: address.BuyerAddressId,
		Required:       address.Required,
	}
}

func constructFulfilmentStockForDeductedOrders(
	ctx context.Context,
	orders []entity.ItemGroupShippingOrder,
) []entity.ItemGroupShippingOrder {
	for _, order := range orders {
		deductedItems := make([]entity.ItemGroupShippingOrderItem, 0)
		for _, item := range order.Items {
			if !item.IsDeductedItem() {
				continue
			}
			itemFulfillment := entity.ItemFulfillment{
				Stock: item.Quantity,
				StockLocation: []*entity.ItemStockLocation{
					{
						LocationId:      item.LocalSipMtskuInfo.ProductLocationId,
						AvailableStock:  item.Quantity,
						FulfillmentType: item.LocalSipMtskuInfo.ProductFulfilmentType,
					},
				},
				FulfillmentType: item.LocalSipMtskuInfo.ProductFulfilmentType,
			}
			item.ItemFulfillmentList = []*entity.ItemFulfillment{&itemFulfillment}
			item.Stock = item.Quantity
			item.SumStock = item.Quantity
			item.ItemType = int32(order_action.Constant_NORMAL_ORDER_ITEM)
			item.PffInputPriceForced = false
			deductedItems = append(deductedItems, item)
		}
		order.CBOption = 0
		Logger.CtxLogDebugf(ctx, "order_deducted_items, group_id: %d, deductedItems: %s",
			order.ShopID, Logger.JsonStringForDebugLog(ctx, deductedItems))
	}
	return orders
}

func getItemIDToSOCFulfilmentType(requestInfo *dto.GetEligibleFulfilmentSourceRequest) map[uint64]constant.FulfilmentType {
	itemIDToSOCFulfilmentType := make(map[uint64]constant.FulfilmentType)
	for _, itemInfo := range requestInfo.RequestItemInfos {
		itemIDToSOCFulfilmentType[itemInfo.ItemId] = itemInfo.OrderStockFulfilmentType
	}

	return itemIDToSOCFulfilmentType
}

func getCacheOrderEligibleSources(ctx context.Context, groupItem entity.ItemGroupShippingOrderItem, socFulfilmentType constant.FulfilmentType) dto.CacheItemEligibleSources {
	var eligibleSources dto.CacheItemEligibleSources
	if socFulfilmentType == constant.FulfilmentTypeCacheSeller {
		eligibleSources = getSellerCacheOrderEligibleSources(ctx, groupItem)
	} else {
		eligibleSources = getShopeeCacheOrderEligibleSources(groupItem)
	}

	return eligibleSources
}

func getSellerCacheOrderEligibleSources(ctx context.Context, groupItem entity.ItemGroupShippingOrderItem) dto.CacheItemEligibleSources {
	SOCLocation := GetSellerSOCLocation(groupItem)
	sellerLocation := GetSellerLocation(ctx, groupItem)

	return dto.CacheItemEligibleSources{
		CandidateFallbackFulfilmentInfos: []dto.FulfilmentInfo{
			{
				Source:         sellerLocation,
				FulfilmentType: constant.FulfilmentTypeSeller,
			},
		},
		SOCFulfilmentInfo: dto.FulfilmentInfo{
			Source:         SOCLocation,
			FulfilmentType: constant.FulfilmentTypeCacheSeller,
		},
	}
}

func getShopeeCacheOrderEligibleSources(groupItem entity.ItemGroupShippingOrderItem) dto.CacheItemEligibleSources {
	SOCLocation := GetShopeeSOCLocation(groupItem)
	shopeeWarehouseLocations := GetShopeeWarehouseLocations(groupItem)

	return dto.CacheItemEligibleSources{
		CandidateFallbackFulfilmentInfos: shopeeWarehouseLocations,
		SOCFulfilmentInfo: dto.FulfilmentInfo{
			Source:         SOCLocation,
			FulfilmentType: constant.FulfilmentTypeCacheWarehouse,
		},
	}
}

func GetSellerSOCLocation(groupItem entity.ItemGroupShippingOrderItem) string {
	for _, itemFulfilment := range groupItem.ItemFulfillmentList {
		socLocationID := GetEligibleSellerSOCLocationID(itemFulfilment, groupItem.Quantity)
		if socLocationID != "" {
			return socLocationID
		}
	}

	return ""
}

func GetEligibleSellerSOCLocationID(itemFulfillment *entity.ItemFulfillment, requiredQuantity int32) string {
	if itemFulfillment.FulfillmentType.Eligible(constant.FulfilmentTypeSeller) {
		for _, stockLocation := range itemFulfillment.StockLocation {
			if stockLocation.AvailableStock >= requiredQuantity {
				return stockLocation.LocationId
			}
		}
	}
	return ""
}

func GetSellerLocation(ctx context.Context, groupItem entity.ItemGroupShippingOrderItem) string {
	sellerStockLocation := entity.GetSellerWarehouseId(ctx)
	hasEligibleSellerStock := false
	for _, itemFulfilment := range groupItem.ItemFulfillmentList {
		if HasEligibleSellerLocalStock(itemFulfilment, sellerStockLocation, groupItem.Quantity) {
			hasEligibleSellerStock = true
			break
		}
	}

	if hasEligibleSellerStock {
		return sellerStockLocation
	}

	return ""
}

func HasEligibleSellerLocalStock(itemFulfillment *entity.ItemFulfillment, requiredLocation string, requiredQuantity int32) bool {
	if itemFulfillment.FulfillmentType.Eligible(constant.FulfilmentTypeSeller) {
		for _, stockLocation := range itemFulfillment.StockLocation {
			if stockLocation.LocationId == requiredLocation {
				if stockLocation.AvailableStock >= requiredQuantity {
					return true
				}
			}
		}
	}
	return false
}

func GetShopeeSOCLocation(groupItem entity.ItemGroupShippingOrderItem) string {
	for _, itemFulfilment := range groupItem.ItemFulfillmentList {
		socLocationID := GetEligibleShopeeSOCLocationID(itemFulfilment, groupItem.Quantity)
		if socLocationID != "" {
			return socLocationID
		}
	}

	return ""
}

func GetEligibleShopeeSOCLocationID(itemFulfillment *entity.ItemFulfillment, requiredQuantity int32) string {
	if itemFulfillment.FulfillmentType.Eligible(constant.FulfilmentTypeCacheWarehouse) {
		for _, stockLocation := range itemFulfillment.StockLocation {
			if stockLocation.AvailableStock >= requiredQuantity {
				return stockLocation.LocationId
			}
		}
	}

	return ""
}

func GetShopeeWarehouseLocations(groupItem entity.ItemGroupShippingOrderItem) []dto.FulfilmentInfo {
	eligibleLocations := make([]dto.FulfilmentInfo, 0)
	for _, itemFulfilment := range groupItem.ItemFulfillmentList {
		for _, stockLocation := range itemFulfilment.StockLocation {
			if itemFulfilment.FulfillmentType == constant.FulfilmentTypeShopee {
				if stockLocation.AvailableStock >= groupItem.Quantity {
					eligibleLocations = append(eligibleLocations, dto.FulfilmentInfo{
						Source:         stockLocation.LocationId,
						FulfilmentType: constant.FulfilmentTypeShopee,
					})
				}
			}
		}
	}
	return eligibleLocations
}

func parseResponseItemInfo(itemTDToCacheItemInfos map[uint64]dto.CacheItemInfo) []*dto.ResponseItemInfo {
	responseItemInfosList := make([]*dto.ResponseItemInfo, 0)
	for _, cacheItemInfo := range itemTDToCacheItemInfos {
		newResponseItemInfo := &dto.ResponseItemInfo{
			ShopId:                    cacheItemInfo.ShopID,
			ItemId:                    cacheItemInfo.ItemID,
			ModelId:                   cacheItemInfo.ModelID,
			Quantity:                  uint32(cacheItemInfo.Quantity),
			EligibleFulfilmentSources: parseFulfilmentInfo(cacheItemInfo.CacheItemEligibleSources),
		}
		responseItemInfosList = append(responseItemInfosList, newResponseItemInfo)
	}

	return responseItemInfosList
}

func parseFulfilmentInfo(eligibleSources dto.CacheItemEligibleSources) []dto.FulfilmentInfo {
	newFulfilmentInfo := make([]dto.FulfilmentInfo, 0)
	if eligibleSources.SOCFulfilmentInfo.Source != "" {
		newFulfilmentInfo = append(newFulfilmentInfo, eligibleSources.SOCFulfilmentInfo)
	}
	if eligibleSources.SelectedFallbackFulfilmentInfo.Source != "" {
		newFulfilmentInfo = append(newFulfilmentInfo, eligibleSources.SelectedFallbackFulfilmentInfo)
	}

	return newFulfilmentInfo
}

func getSkuId(itemId typ.ItemIdType, modelId typ.ModelIdType, index int) string {
	return fmt.Sprintf("%d_%d_%d", itemId, modelId, index)
}
