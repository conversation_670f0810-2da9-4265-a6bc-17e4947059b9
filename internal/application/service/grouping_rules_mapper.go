package service

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/dto"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
)

// convertGroupingRulesToDomainEntity 将DTO分组规则转换为领域实体
// 这是应用层和领域层之间的转换边界
func convertGroupingRulesToDomainEntity(dtoRules *dto.GroupingRules) *group_entity.GroupingRules {
	if dtoRules == nil {
		return nil
	}

	return &group_entity.GroupingRules{
		BundleRules:     convertBundleRules(dtoRules.BundleRules),
		IsolationRules:  convertIsolationRules(dtoRules.IsolationRules),
		ConstraintRules: convertConstraintRules(dtoRules.ConstraintRules),
		ChannelRules:    convertChannelRules(dtoRules.ChannelRules),
	}
}

// ========== 内部转换函数 ==========

func convertBundleRules(dtoRules []*dto.BundleRule) []group_entity.BundleRule {
	if len(dtoRules) == 0 {
		return nil
	}

	rules := make([]group_entity.BundleRule, 0, len(dtoRules))
	for _, rule := range dtoRules {
		if rule != nil { // 跳过nil元素
			rules = append(rules, group_entity.BundleRule{
				RuleID:       rule.RuleId,
				ItemQueryIDs: rule.ItemQueryIds,
				Mandatory:    rule.Mandatory,
			})
		}
	}
	return rules
}

func convertIsolationRules(dtoRules []*dto.IsolationRule) []group_entity.IsolationRule {
	if len(dtoRules) == 0 {
		return nil
	}

	rules := make([]group_entity.IsolationRule, 0, len(dtoRules))
	for _, rule := range dtoRules {
		if rule != nil {
			rules = append(rules, group_entity.IsolationRule{
				RuleID:        rule.RuleId,
				ItemQueryIDs:  rule.ItemQueryIds,
				Conditions:    convertRuleConditions(rule.Conditions),
				IsolationType: convertIsolationType(rule.IsolationType),
				Mandatory:     rule.Mandatory,
			})
		}
	}
	return rules
}

func convertConstraintRules(dtoRules []*dto.ConstraintRule) []group_entity.ConstraintRule {
	if len(dtoRules) == 0 {
		return nil
	}

	rules := make([]group_entity.ConstraintRule, 0, len(dtoRules))
	for _, rule := range dtoRules {
		if rule != nil {
			rules = append(rules, group_entity.ConstraintRule{
				RuleID:         rule.RuleId,
				ItemQueryIDs:   rule.ItemQueryIds,
				ConstraintType: convertConstraintType(rule.ConstraintType),
				Mandatory:      rule.Mandatory,
			})
		}
	}
	return rules
}

func convertChannelRules(dtoRules []*dto.ChannelRule) []group_entity.ChannelRule {
	if len(dtoRules) == 0 {
		return nil
	}

	rules := make([]group_entity.ChannelRule, 0, len(dtoRules))
	for _, rule := range dtoRules {
		if rule != nil {
			rules = append(rules, group_entity.ChannelRule{
				RuleID:       rule.RuleId,
				ItemQueryIDs: rule.ItemQueryIds,
				Conditions:   convertRuleConditions(rule.Conditions),
				RuleType:     convertChannelRuleType(rule.RuleType),
				Mandatory:    rule.Mandatory,
			})
		}
	}
	return rules
}

func convertRuleConditions(dtoConditions []*dto.RuleCondition) []group_entity.RuleCondition {
	if len(dtoConditions) == 0 {
		return nil
	}

	conditions := make([]group_entity.RuleCondition, 0, len(dtoConditions))
	for _, condition := range dtoConditions {
		if condition != nil {
			conditions = append(conditions, group_entity.RuleCondition{
				ConditionType: convertConditionType(condition.ConditionType),
				ChannelIDs:    condition.ChannelIds,
			})
		}
	}
	return conditions
}

// ========== 枚举类型转换函数 ==========

func convertIsolationType(dtoType dto.IsolationType) group_entity.IsolationType {
	switch dtoType {
	case dto.IsolationTypeSingleItem:
		return group_entity.IsolationTypeSingleItem
	case dto.IsolationTypeIsolatedGroup:
		return group_entity.IsolationTypeIsolatedGroup
	default:
		return group_entity.IsolationTypeSingleItem
	}
}

func convertConstraintType(dtoType dto.ConstraintType) group_entity.ConstraintType {
	switch dtoType {
	case dto.ConstraintTypeDisableQuantitySplit:
		return group_entity.ConstraintTypeDisableQuantitySplit
	case dto.ConstraintTypeSingleSourceOnly:
		return group_entity.ConstraintTypeSingleSourceOnly
	default:
		return group_entity.ConstraintTypeDisableQuantitySplit
	}
}

func convertChannelRuleType(dtoType dto.ChannelRuleType) group_entity.ChannelRuleType {
	switch dtoType {
	case dto.ChannelRuleTypeCommonChannelRequired:
		return group_entity.ChannelRuleTypeCommonChannelRequired
	case dto.ChannelRuleTypeChannelExclusive:
		return group_entity.ChannelRuleTypeChannelExclusive
	default:
		return group_entity.ChannelRuleTypeCommonChannelRequired
	}
}

func convertConditionType(dtoType dto.ConditionType) group_entity.ConditionType {
	switch dtoType {
	case dto.ConditionTypeChannelEnabled:
		return group_entity.ConditionTypeChannelEnabled
	default:
		return group_entity.ConditionTypeChannelEnabled
	}
}
