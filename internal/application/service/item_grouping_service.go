package service

import (
	"context"
	"fmt"

	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/dto"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_stock"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/soc"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ItemGroupingService interface {
	GroupItemsUseCase(context.Context, *fss_proto.GroupItemsRequest) ([]*fss_proto.ShippingGroup, fsserr.Error)
	BatchGetSellerWhPriorityUseCase(context.Context, *fss_proto.BatchGetSellerWhPriorityRequest) ([]*fss_proto.SellerWhPriority, fsserr.Error)
	GetEligibleFulfilmentSourceUseCase(context.Context, *fss_proto.GetEligibleFulfilmentSourceRequest) ([]*fss_proto.ResponseItemInfo, fsserr.Error)
}

type ItemGroupingServiceImpl struct {
	itemGrouper              item_grouping.ItemGrouper
	shopService              shop.ShopService
	itemStockService         item_stock.ItemStockService
	stockService             item_stock.StockService
	sellerTagService         seller_tag.SellerTagService
	sellerWHPriorityService  warehouse_priority.SellerWHPriorityService
	warehousePriorityService warehouse_priority.WarehousePriorityService
	socService               soc.SOCService
	addressService           address.AddrService
	configAccessor           config.ConfAccessor
}

func NewItemGroupingService(
	itemGrouper item_grouping.ItemGrouper,
	shopService shop.ShopService,
	itemStockService item_stock.ItemStockService,
	stockService item_stock.StockService,
	sellerTagService seller_tag.SellerTagService,
	sellerWHPriorityService warehouse_priority.SellerWHPriorityService,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	socService soc.SOCService,
	addressService address.AddrService,
	configAccessor config.ConfAccessor,
) *ItemGroupingServiceImpl {
	return &ItemGroupingServiceImpl{
		itemGrouper:              itemGrouper,
		shopService:              shopService,
		itemStockService:         itemStockService,
		stockService:             stockService,
		sellerTagService:         sellerTagService,
		sellerWHPriorityService:  sellerWHPriorityService,
		warehousePriorityService: warehousePriorityService,
		socService:               socService,
		addressService:           addressService,
		configAccessor:           configAccessor,
	}
}

func (s *ItemGroupingServiceImpl) GroupItemsUseCase(ctx context.Context, req *fss_proto.GroupItemsRequest) ([]*fss_proto.ShippingGroup, fsserr.Error) {
	// 参数验证
	if req == nil {
		return nil, fsserr.New(fsserr.ParamErr, "request cannot be nil")
	}

	if len(req.Items) == 0 {
		return []*fss_proto.ShippingGroup{}, nil
	}

	// 转换 protobuf 请求为 DTO
	dtoReq, err := dto.ConvertPBToGroupItemsRequest(req)
	if err != nil {
		return nil, err
	}

	// 转换请求 DTO 为领域实体
	shippingOrders, err := s.convertRequestToShippingOrders(ctx, dtoReq)
	if err != nil {
		return nil, err
	}
	if len(shippingOrders) == 0 {
		return []*fss_proto.ShippingGroup{}, nil
	}

	// 转换DTO规则为领域实体
	domainRules := convertGroupingRulesToDomainEntity(dtoReq.GroupingRules)

	// 构建买家信息 - 从请求中获取并完善地址信息
	buyerAddress := s.buildBuyerAddress(ctx, typ.UserIdType(req.BuyerUserId), entity.ItemGroupBuyerAddress{
		State:          req.BuyerAddress.State,
		City:           req.BuyerAddress.City,
		District:       req.BuyerAddress.District,
		BuyerAddressID: req.BuyerAddress.BuyerAddressId,
		Required:       req.BuyerAddress.Required,
	})

	buyerInfo := group_entity.BuyerInfo{
		UserID:  typ.UserIdType(req.BuyerUserId),
		Address: buyerAddress,
	}

	// 调用领域层进行订单分组处理（包含拆合单逻辑）
	// 从 FeatureOption 中获取 SkipSplitByWeightDimension 标志
	skipSplitByWeightDimension := false
	if req.FeatureOption != nil {
		skipSplitByWeightDimension = req.FeatureOption.SkipSplitByWeightDimension
	}

	// 判断是否为PDP/ItemCard场景，如果是则跳过订单拆合单逻辑
	skipOrderSplitMerge := dtoReq.Scenario.IsPDPOrItemCard()

	processedOrders, err := s.itemGrouper.GroupItems(ctx, shippingOrders, buyerInfo, domainRules, skipSplitByWeightDimension, skipOrderSplitMerge)
	if err != nil {
		return nil, err
	}

	// 转换领域实体为响应 DTO
	dtoResponse := s.convertShippingOrdersToResponse(ctx, req.QueryId, processedOrders, typ.UserIdType(req.BuyerUserId))

	// 转换 DTO 响应为 protobuf 响应
	pbResponse, err := dto.ConvertGroupItemsResponseToPB(dtoResponse)
	if err != nil {
		return nil, err
	}

	return pbResponse.ShippingGroups, nil
}

func (s *ItemGroupingServiceImpl) BatchGetSellerWhPriorityUseCase(ctx context.Context, req *fss_proto.BatchGetSellerWhPriorityRequest) ([]*fss_proto.SellerWhPriority, fsserr.Error) {
	// 转换 protobuf 请求为 DTO
	dtoReq, err := dto.ConvertPBToBatchGetSellerWhPriorityRequest(req)
	if err != nil {
		return nil, err
	}

	shopIds := collection.RemoveDuplicate(dtoReq.ShopIds)
	var priorityInfoMap map[uint64]entity.WHPriorityInfo
	var err2 error
	priorityInfoMap, err2 = s.sellerWHPriorityService.BatchGetWHPriority(
		ctx,
		dtoReq.BuyerUserId,
		dtoReq.BuyerAddressId,
		shopIds,
	)
	if err2 != nil {
		//logCtx.Error("call seller wh priority service", ulog.Error(err))
		return nil, fsserr.New(fsserr.DataErr, "call seller wh priority service: %v", err2)
	}

	response := convertToBatchGetSellerWHPriorityResponse(priorityInfoMap)

	// 转换 DTO 响应为 protobuf 响应
	pbResponse, err := dto.ConvertBatchGetSellerWhPriorityResponseToPB(response)
	if err != nil {
		return nil, err
	}

	return pbResponse.Priorities, nil
}

// convertEligibleSourcesRequestToShippingOrders 将 GetEligibleFulfilmentSourceRequest 转换为 ShippingOrder 列表
func (s *ItemGroupingServiceImpl) convertEligibleSourcesRequestToShippingOrders(ctx context.Context, req *dto.GetEligibleFulfilmentSourceRequest) ([]group_entity.ShippingOrder, fsserr.Error) {
	if req == nil || len(req.RequestItemInfos) == 0 {
		return nil, nil
	}

	shippingOrders := make([]group_entity.ShippingOrder, 0, len(req.RequestItemInfos))
	for _, itemInfo := range req.RequestItemInfos {
		if itemInfo == nil {
			continue
		}

		shippingOrder, err := s.buildEligibleSourceShippingOrder(ctx, itemInfo, req.BuyerUserId)
		if err != nil {
			return nil, err
		}

		shippingOrders = append(shippingOrders, shippingOrder)
	}

	return shippingOrders, nil
}

// buildEligibleSourceShippingOrder 根据单个 RequestItemInfo 构建 ShippingOrder
func (s *ItemGroupingServiceImpl) buildEligibleSourceShippingOrder(
	ctx context.Context, itemInfo *dto.RequestItemInfo, buyerUserId typ.UserIdType,
) (group_entity.ShippingOrder, fsserr.Error) {
	// 构建 ShopInfo
	shopInfoMap, err := s.buildShopInfos(ctx, []uint64{itemInfo.ShopId})
	if err != nil {
		return group_entity.ShippingOrder{}, fsserr.New(fsserr.DataErr, "failed to build shop infos")
	}

	shopInfo, exists := shopInfoMap[itemInfo.ShopId]
	if !exists {
		// 如果获取不到完整的ShopInfo，使用基础信息
		shopInfo = group_entity.ShopInfo{
			ShopID: itemInfo.ShopId,
		}
	}

	// 获取 ItemType 和库存信息
	itemType, stockInfoMap, err := s.getItemTypeAndStockInfoForEligibleSource(ctx, itemInfo, buyerUserId)
	if err != nil {
		// 如果获取失败，使用默认值
		itemType = 0
	}

	// 构建 ShippingOrderItem
	shippingOrderItem := group_entity.ShippingOrderItem{
		QueryId:                   generateQueryId(itemInfo), // 生成唯一的 QueryId
		ShopInfo:                  shopInfo,
		ItemID:                    itemInfo.ItemId,
		ModelID:                   itemInfo.ModelId,
		Quantity:                  itemInfo.Quantity,
		ItemType:                  itemType,
		FulfilmentTypeToLocations: s.convertStockInfoToFulfilmentTypeToLocations(stockInfoMap, itemInfo),
	}

	shippingOrder := group_entity.ShippingOrder{
		RequireAdvanceBooking: true, // 对于 eligible source 查询，总是需要 advance booking
		Items:                 []group_entity.ShippingOrderItem{shippingOrderItem},
	}

	return shippingOrder, nil
}

// getItemTypeAndStockInfoForEligibleSource 为 eligible source 查询获取 ItemType 和库存信息
func (s *ItemGroupingServiceImpl) getItemTypeAndStockInfoForEligibleSource(ctx context.Context, itemInfo *dto.RequestItemInfo, buyerID typ.UserIdType) (uint32, map[uint64]entity.ShopOrderStockInfo, fsserr.Error) {
	// 构建 ShopOrder 用于查询库存信息
	shopOrder := entity.ItemGroupShippingOrder{
		ShopID: itemInfo.ShopId,
		Items: []entity.ItemGroupShippingOrderItem{
			{
				ItemId:   itemInfo.ItemId,
				ModelId:  itemInfo.ModelId,
				Quantity: int32(itemInfo.Quantity),
			},
		},
	}

	// 调用库存服务获取库存信息（包含 OrderItemType）
	stockInfoMap, err := s.itemStockService.GetShopOrderStocks(ctx, []entity.ItemGroupShippingOrder{shopOrder}, buyerID, false)
	if err != nil {
		return 0, nil, fsserr.With(fsserr.ServerErr, err)
	}

	stockInfo, exists := stockInfoMap[itemInfo.ShopId]
	if !exists || len(stockInfo.ItemStocks) == 0 {
		return 0, stockInfoMap, fsserr.New(fsserr.DataErr, "no stock info found for shop %d item %d model %d", itemInfo.ShopId, itemInfo.ItemId, itemInfo.ModelId)
	}

	// 查找对应的 ItemStock
	for _, itemStock := range stockInfo.ItemStocks {
		if itemStock.ItemID == itemInfo.ItemId && itemStock.ModelID == itemInfo.ModelId {
			return itemStock.OrderItemType, stockInfoMap, nil
		}
	}

	return 0, stockInfoMap, fsserr.New(fsserr.DataErr, "no matching item stock found for item %d model %d", itemInfo.ItemId, itemInfo.ModelId)
}

// convertStockInfoToFulfilmentTypeToLocations 将库存信息转换为 FulfilmentTypeToLocations
func (s *ItemGroupingServiceImpl) convertStockInfoToFulfilmentTypeToLocations(stockInfoMap map[uint64]entity.ShopOrderStockInfo, itemInfo *dto.RequestItemInfo) map[constant.FulfilmentType]group_entity.StockLocations {
	fulfilmentTypeToLocations := make(map[constant.FulfilmentType]group_entity.StockLocations)

	stockInfo, exists := stockInfoMap[itemInfo.ShopId]
	if !exists {
		return fulfilmentTypeToLocations
	}

	// 查找对应的 ItemStock
	for _, itemStock := range stockInfo.ItemStocks {
		if itemStock.ItemID == itemInfo.ItemId && itemStock.ModelID == itemInfo.ModelId {
			// 转换库存位置信息
			for _, stockLocation := range itemStock.StockLocations {
				fulfilmentType := constant.FulfilmentType(stockLocation.FulfillmentType)
				location := group_entity.ItemStockLocation{
					Source:          stockLocation.LocationID,
					FulfilmentType:  fulfilmentType,
					AvailableStock:  stockLocation.AvailableStock,
					EnabledChannels: []int{}, // 默认空，可以根据需要填充
				}
				fulfilmentTypeToLocations[fulfilmentType] = append(fulfilmentTypeToLocations[fulfilmentType], location)
			}
			break
		}
	}

	return fulfilmentTypeToLocations
}

// generateQueryId 为 eligible source 查询生成唯一的 QueryId
func generateQueryId(itemInfo *dto.RequestItemInfo) string {
	return fmt.Sprintf("eligible_source_%d_%d_%d", itemInfo.ShopId, itemInfo.ItemId, itemInfo.ModelId)
}

// buildBuyerAddress 构建完整的买家地址信息
// 参考 fulfillment planning 的实现，如果地址信息不完整且有 BuyerAddressID，则通过地址服务获取完整信息
func (s *ItemGroupingServiceImpl) buildBuyerAddress(
	ctx context.Context,
	buyerUserID typ.UserIdType,
	buyerAddress entity.ItemGroupBuyerAddress,
) entity.ItemGroupBuyerAddress {
	// 如果地址信息不是必需的，直接返回
	if !buyerAddress.Required {
		return buyerAddress
	}

	// 检查地址信息是否不完整（State 或 City 为空）
	isLocationEmpty := len(buyerAddress.State) == 0 || len(buyerAddress.City) == 0
	if isLocationEmpty && buyerAddress.BuyerAddressID > 0 {
		// 通过地址服务获取完整的地址信息
		addr, err := s.addressService.GetAddressWithGeo(ctx, buyerUserID, buyerAddress.BuyerAddressID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "cannot get buyer address with geo, buyer_id: %d, address_id: %d, err: %v",
				buyerUserID, buyerAddress.BuyerAddressID, err)
			return buyerAddress
		}

		// 更新地址信息
		buyerAddress.State = addr.State
		buyerAddress.City = addr.City
		buyerAddress.District = addr.District
	}

	return buyerAddress
}

func convertToBatchGetSellerWHPriorityResponse(
	priorityInfoMap map[uint64]entity.WHPriorityInfo,
) *dto.BatchGetSellerWhPriorityResponse {
	response := &dto.BatchGetSellerWhPriorityResponse{}

	for shopId, whInfo := range priorityInfoMap {
		locations := make(
			[]*dto.SellerWHLocation,
			0,
		)
		if whInfo.GetIsMWHSellerWhitelisted() || whInfo.GetIs3PFSellerWhitelisted() {
			for _, wh := range whInfo.GetPrioritizedList() {
				locations = append(
					locations, &dto.SellerWHLocation{
						LocationId: wh.LocationId,
						AddressId:  wh.AddressId,
					},
				)
			}
		}
		priorityInfo := &dto.PriorityInfo{
			ShopId:    shopId,
			Locations: locations,
		}
		response.Priorities = append(response.Priorities, priorityInfo)
	}

	return response
}
