package dto

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type (
	BatchGetSellerWhPriorityRequest struct {
		BuyerUserId    typ.UserIdType `json:"buyer_user_id"`
		BuyerAddressId uint64         `json:"buyer_address_id"`
		ShopIds        []uint64       `json:"shop_ids"`
	}

	BatchGetSellerWhPriorityResponse struct {
		Priorities []*PriorityInfo `protobuf:"bytes,2,rep,name=priorities" json:"priorities"`
	}

	PriorityInfo struct {
		ShopId uint64 `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id"`
		// Sorted list, first item has the highest priority. See WH Priority section above.
		// If seller is not whitelisted, this is empty.
		Locations []*SellerWHLocation `protobuf:"bytes,2,rep,name=locations" json:"locations"`
	}

	SellerWHLocation struct {
		LocationId string `protobuf:"bytes,1,opt,name=location_id,json=locationId" json:"location_id"`
		AddressId  uint64 `protobuf:"varint,2,opt,name=address_id,json=addressId" json:"address_id"`
	}
)

func (b *BatchGetSellerWhPriorityRequest) Validate() fsserr.Error {
	if b.BuyerUserId == 0 {
		return fsserr.New(fsserr.ParamErr, "buyer_user_id is 0")
	}
	if b.BuyerAddressId == 0 {
		return fsserr.New(fsserr.ParamErr, "buyer_address_id is 0")
	}
	if len(b.ShopIds) == 0 || len(b.ShopIds) > 10 {
		return fsserr.New(fsserr.ParamErr, "there must be 1 to 10 shop_ids")
	}
	for _, shopId := range b.ShopIds {
		if shopId == 0 {
			return fsserr.New(fsserr.ParamErr, "shop_id is 0")
		}
	}
	return nil
}
