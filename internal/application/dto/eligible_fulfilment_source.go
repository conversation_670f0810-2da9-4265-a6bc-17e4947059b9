package dto

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type GetEligibleFulfilmentSourceRequest struct {
	BuyerUserId      typ.UserIdType     `json:"buyer_user_id" validate:"required"`
	BuyerAddress     *BuyerAddress      `json:"buyer_address"`
	RequestItemInfos []*RequestItemInfo `json:"request_item_infos" validate:"required,min=1,dive,required"`
}

type GetEligibleFulfilmentSourceResponse struct {
	ResponseItemInfo []*ResponseItemInfo `json:"response_item_info"`
}

type RequestItemInfo struct {
	ShopId                   uint64                  `json:"shop_id" validate:"required"`
	ItemId                   typ.ItemIdType          `json:"item_id" validate:"required"`
	ModelId                  typ.ModelIdType         `json:"model_id" validate:"required"`
	Quantity                 uint32                  `json:"quantity" validate:"required,min=1"`
	FilteredSocId            string                  `json:"filtered_soc_id"`
	OrderStockFulfilmentType constant.FulfilmentType `json:"order_stock_fulfilment_type"`
}

type ResponseItemInfo struct {
	ShopId                    uint64           `json:"shop_id"`
	ItemId                    typ.ItemIdType   `json:"item_id"`
	ModelId                   typ.ModelIdType  `json:"model_id"`
	Quantity                  uint32           `json:"quantity"`
	EligibleFulfilmentSources []FulfilmentInfo `json:"eligible_fulfilment_sources"`
}

// Validate validates the GetEligibleFulfilmentSourceRequest
func (r *GetEligibleFulfilmentSourceRequest) Validate() fsserr.Error {
	if r.BuyerUserId == 0 {
		return fsserr.New(fsserr.ParamErr, "buyer_user_id is required")
	}

	if len(r.RequestItemInfos) == 0 {
		return fsserr.New(fsserr.ParamErr, "request_item_infos cannot be empty")
	}

	for i, itemInfo := range r.RequestItemInfos {
		if itemInfo == nil {
			return fsserr.New(fsserr.ParamErr, "request_item_infos[%d] is nil", i)
		}

		if itemInfo.ShopId == 0 {
			return fsserr.New(fsserr.ParamErr, "request_item_infos[%d].shop_id is required", i)
		}

		if itemInfo.ItemId == 0 {
			return fsserr.New(fsserr.ParamErr, "request_item_infos[%d].item_id is required", i)
		}

		if itemInfo.ModelId == 0 {
			return fsserr.New(fsserr.ParamErr, "request_item_infos[%d].model_id is required", i)
		}

		if itemInfo.Quantity == 0 {
			return fsserr.New(fsserr.ParamErr, "request_item_infos[%d].quantity must be greater than 0", i)
		}
	}

	return nil
}

type CacheItemInfo struct {
	ItemID                   typ.ItemIdType
	ModelID                  typ.ModelIdType
	ShopID                   uint64
	Quantity                 int32
	CacheItemEligibleSources CacheItemEligibleSources
}

type CacheItemEligibleSources struct {
	SOCFulfilmentInfo                FulfilmentInfo
	SelectedFallbackFulfilmentInfo   FulfilmentInfo
	CandidateFallbackFulfilmentInfos []FulfilmentInfo
}

type FulfilmentInfo struct {
	Source         string
	FulfilmentType constant.FulfilmentType
}
