package lpslib

import (
	"strings"

	jsoniter "github.com/json-iterator/go"
)

type (
	ChannelInfo struct {
		ChannelID                     int                            `json:"channelid"`
		Priority                      int                            `json:"priority"`
		Category                      int                            `json:"category"`
		ShippingMethod                int                            `json:"shipping_method"`
		Maintenance                   int                            `json:"maintenance"`
		Flag                          uint64                         `json:"flag"`
		Name                          string                         `json:"name"`
		DisplayName                   string                         `json:"display_name"`
		IsMaskChannel                 int                            `json:"is_mask_channel"`
		SupportMidwayInterception     int                            `json:"support_midway_interception"`
		BuyerPreferDeliveryTime       []BuyerPreferDeliveryTime      `json:"buyer_prefer_delivery_time"`
		IsShowPreferredDeliveryOption int                            `json:"is_show_preferred_delivery_option"`
		LogisticsCapability           int64                          `json:"logistics_capability"`
		NeedToCheckShippingMethod     bool                           `json:"need_to_check_shipping_method"`
		AvailableDeliveryInstruction  []AvailableDeliveryInstruction `json:"available_delivery_instruction"`
		NonIntegratedUseCase          int                            `json:"non_integrated_use_case"`
		FromCountry                   string                         `json:"from_country"`
		VolumeStatus                  VolumeStatusFlag               `json:"volume_status"`
		MultiLanguage                 map[string]DisplayName         `json:"multi_language"`
		SupportBuyOneMore             bool                           `json:"support_buy_one_more"`
		// the following fields are not from SLS
		ExtraInfo                 ChannelExtraInfo `json:"extra_info"`
		HasSelectableDeliveryTime bool             `json:"has_selectable_delivery_time"`
		ATLEligible               bool             `json:"atl_eligible"`
	}

	BuyerPreferDeliveryTime struct {
		StartTime           string `json:"start_time"`
		EndTime             string `json:"end_time"`
		SlotID              int    `json:"slot_id"`
		TimeslotDescription int    `json:"timeslot_description"`
		AddressType         int    `json:"address_type"`
	}

	DisplayName struct {
		BuyerDisplayName  string `json:"buyer_display_name"`
		SellerDisplayName string `json:"seller_display_name"`
	}

	AvailableDeliveryInstruction struct { //new struct
		Category                int   `json:"category"`
		DeliveryInstructionInfo []int `json:"delivery_instruction_info"` //this is the new field to determine ATL eligibility
	}

	ChannelExtraInfo struct {
		DefaultPrice             float64                 `json:"default_price"`
		AddressType              int                     `json:"address_type"`
		DefaultSizeID            *int32                  `json:"default_sizeid"`
		MaxSize                  *float32                `json:"max_size"`
		MinSize                  *float32                `json:"min_size"`
		DimensionCriterion       *DimensionCriterionInfo `json:"dimension_criterion"`
		MaxItems                 *int32                  `json:"max_items"`
		MaxTotalPrice            *float64                `json:"max_total_price"`
		DaysToDelivery           *int32                  `json:"days_to_delivery"`
		MinAmountNeedIC          float64                 `json:"min_amount_need_ic"`
		GuaranteeExtensionPeriod *float32                `json:"guarantee_extension_period"`
		KYCControl               bool                    `json:"kyc_control"`
		ExtraFlag                int                     `json:"extra_flag"`
	}

	DimensionCriterionInfo struct {
		MaxLength *float32 `json:"max_length"`
		MaxWidth  *float32 `json:"max_width"`
		MaxHeight *float32 `json:"max_height"`
		SumLength *float32 `json:"sum_length"`
		SumWidth  *float32 `json:"sum_width"`
		SumHeight *float32 `json:"sum_height"`
	}
)

type VolumeStatusFlag int

const (
	VolumeStatusNormal   VolumeStatusFlag = 0 // 0 means OK
	VolumeStatusOverload VolumeStatusFlag = 1 // 1 means overload
)

func (c *ChannelInfo) GetChannelNameInLang(lang string) (localisedName string, localisedDisplayName string) {
	if c == nil || c.MultiLanguage == nil {
		return "", ""
	}
	for language, displayName := range c.MultiLanguage {
		if strings.EqualFold(language, lang) {
			localisedName = displayName.BuyerDisplayName
			localisedDisplayName = displayName.BuyerDisplayName
			return localisedName, localisedDisplayName
		}
	}

	return "", ""
}

type (
	ChannelCharacteristics struct {
		RequiresDestinationAddress       bool
		IsMask                           bool
		IsIntegrated                     bool
		IsHighKYCRisk                    bool
		CheckoutCharacteristics          CheckoutCharacteristics
		OrderCancellationCharacteristics OrderCancellationCharacteristics
	}
	CheckoutCharacteristics struct {
		SupportsCOD                                             bool
		IsCustomAddress                                         bool
		IsUndeletable                                           bool
		IsEscrowReleaseIncludesShipping                         bool
		HasTrackingInfo                                         bool
		SupportsPickupArrangement                               bool
		SupportsDropOffArrangement                              bool
		AllowsSellerManualConsignment                           bool
		InBuyerWhitelistMode                                    bool
		IsShopeeIntegratedTrackingNumber                        bool
		IsSelfCollect                                           bool
		RequiresIcInAddress                                     bool
		SupportsCrossBorder                                     bool
		IsRapidSLA                                              bool
		RequiresPreferredDeliveryTime                           bool
		RequiresPreferredDeliveryTimeSlot                       bool
		IsNonIntegratedShippingProofCompulsoryForDiscountOrder  bool
		IsNonIntegratedTrackingNumberCompulsoryForDiscountOrder bool
		IsNonIntegratedTrackingNumberCompulsory                 bool
		IsContractualShippingFee                                bool
	}

	OrderCancellationCharacteristics struct {
		SupportsMidwayCancellation bool
	}
)
type GetAllChannelsReq struct {
	Token   string `url:"token"`
	Country string `url:"country"`
}

func (g GetAllChannelsReq) Queries() map[string]string {
	return map[string]string{}
}

func (g GetAllChannelsReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

type (
	GetAllChannelsResp struct {
		RetCode int `json:"retcode"`
		Data    struct {
			Channels []InfoWithExtraData `json:"channels"`
		} `json:"data"`
	}

	InfoWithExtraData struct {
		ChannelInfo
		ExtraData string `json:"extra_data"`
	}
)

func (p *GetAllChannelsResp) IsSuccess() bool {
	return p.RetCode == 0
}

func (p *GetAllChannelsResp) FailMsg() string {
	return "sls get all channels failed"
}
