package lpslib

import (
	jsoniter "github.com/json-iterator/go"
)

type (
	GetAllChannelsWeightDimReq struct {
		Orders []OrderSplitLogisticsQuery `json:"orders"`
	}

	OrderSplitLogisticsQuery struct {
		UniqueID  string                           `json:"unique_id"`
		ChannelID int                              `json:"channel_id"`
		Items     []OrderSplitChannelLogisticsItem `json:"items"`
	}

	OrderSplitChannelLogisticsItem struct {
		ItemID   int     `json:"item_id"`
		ModelID  int     `json:"model_id"`
		Quantity int     `json:"quantity"`
		Weight   float64 `json:"weight"`
		Length   float64 `json:"length"`
		Width    float64 `json:"width"`
		Height   float64 `json:"height"`
	}
)

func (p *GetAllChannelsWeightDimReq) Queries() map[string]string {
	// todo
	return map[string]string{}
}

func (p *GetAllChannelsWeightDimReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

type (
	GetAllChannelsWeightDimResp struct {
		RetCode int                          `json:"retcode"`
		Message string                       `json:"message"`
		Detail  string                       `json:"detail,omitempty"`
		Data    []ChannelWeightDimensionInfo `json:"data"`
	}

	ChannelWeightDimensionInfo struct {
		UniqueID                  string                             `json:"unique_id"`
		RetCode                   int                                `json:"retcode"`
		Message                   string                             `json:"message"`
		ChannelID                 int                                `json:"channel_id"`
		WeightDimensionValidation []ChannelWeightDimensionValidation `json:"weight_dimension_validation_result"`
	}

	ChannelWeightDimensionValidation struct {
		RuleType           int    `json:"rule_type"`
		ErrCode            int    `json:"err_code"`
		ErrMessage         string `json:"err_message"`
		LimitationValueMin int    `json:"limitation_value_min"`
		LimitationValueMax int    `json:"limitation_value_max"`
		CalculateResult    int    `json:"calculate_result"`
		ByVolumetricWeight bool   `json:"by_volumetric_weight"`
		Formula            int    `json:"formula"`
		VolumetricFactor   int    `json:"volumetric_factor"`
	}
)

func (p *GetAllChannelsWeightDimResp) IsSuccess() bool {
	// todo
	return p.RetCode == 0
}

func (p *GetAllChannelsWeightDimResp) FailMsg() string {
	return "lps get all channels weight and dimension fail"
}

const (
	WeightDimensionRuleTypeWeight = 3
	WeightDimensionRuleTypeLength = 7
	WeightDimensionRuleTypeWidth  = 8
	WeightDimensionRuleTypeHeight = 9
)
