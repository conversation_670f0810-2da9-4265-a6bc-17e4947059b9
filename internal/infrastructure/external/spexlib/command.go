package spexlib

type Command = string

const (
	GetCheckoutStockInfoCommand Command = "price.checkout_promo.get_checkout_stock_info"
)

// account
const (
	GetAccountCommand                         Command = "account.core.get_account"
	GetPrivateAddressWithGeocodingCommand     Command = "account.address.get_private_address_with_geocoding"
	GetPrivateAddressListWithGeocodingCommand Command = "account.address.get_private_address_list_with_geocoding"
)

// location
const (
	GetShippingAddressCombinationsCommand Command = "location.user_location.get_shipping_address_combinations"
)

// marketplace
const (
	GetItemLabelsCommand           Command = "marketplace.listing.itemtagservice.querying_api.get_item_labels"
	GetModelLabelsCommand          Command = "marketplace.listing.itemtagservice.querying_api.get_model_labels"
	GetDummyBuyerIdCommand         Command = "marketplace.order_processing.cb_collection.api.get_dummy_buyer_id"
	GetSbsItemInfoCommand          Command = "marketplace.order_processing.fulfilment.sbs.get_sbs_item_info"
	GetSbsShipmentGroupInfoCommand Command = "marketplace.order_processing.fulfilment.sbs.get_sbs_shipment_group_info"
	BatchGetShopChannelsCommand    Command = "marketplace.logistics.shop_channels.batch_get_shop_channels"
	GetProductInfoCommand          Command = "marketplace.listing.item.itemaggregation.iteminfo.get_product_info"
	GetChannelsCommand             Command = "marketplace.logistics.shop_channels.get_channels"
)

// seller
const (
	BatchGetWarehouseFlagByShopCommand              Command = "seller.seller_address.core.batch_get_warehouse_flag_by_shop"
	BatchGetWarehouseByShopWithoutPaginationCommand Command = "seller.seller_address.core.batch_get_warehouse_by_shop_without_pagination"
	BatchGetEntityTagValueCommand                   Command = "seller.seller_tag.core.batch_get_entity_tag_value"
	BatchGetEntityTagCommand                        Command = "seller.seller_tag.core.batch_get_entity_tag"
)

// shop
const (
	GetShopBatchCommand                    Command = "shop.core.get_shop_batch"
	BatchGetShopSipBasicCommand            Command = "shop.core.batch_get_shop_sip_basic"
	BatchCheckSellerWarehouseShopCommand   Command = "shop.core.batch_check_seller_warehouse_shop"
	BatchGetShopWarehousesByShopIdsCommand Command = "shop.core.batch_get_shop_warehouses_by_shop_ids"
)
