package spexlib

import (
	"context"

	location_user_location64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/location_user_location.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetShippingAddressCombinations = spexService{
		Command: GetShippingAddressCombinationsCommand,
		System:  constant.SystemUserLocation,
	}
)

func (client *SpexClientImpl) GetShippingAddresses(ctx context.Context, req *location_user_location64.GetShippingAddressCombinationsRequest) (*location_user_location64.GetShippingAddressCombinationsResponse, fsserr.Error) {
	resp := &location_user_location64.GetShippingAddressCombinationsResponse{}
	if spexErr := client.request(ctx, GetShippingAddressCombinations, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetShippingAddressCombinations, spexErr))
	}
	return resp, nil
}
