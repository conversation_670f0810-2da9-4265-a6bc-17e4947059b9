package spexlib

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shop_core.pb"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/layer_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetShopBatch = spexService{
		Command: GetShopBatchCommand,
		System:  constant.SystemShop,
	}

	BatchGetShopSipBasic = spexService{
		Command: BatchGetShopSipBasicCommand,
		System:  constant.SystemShop,
	}

	BatchCheckSellerWarehouseShop = spexService{
		Command: BatchCheckSellerWarehouseShopCommand,
		System:  constant.SystemShop,
	}

	BatchGetShopWarehousesByShopIds = spexService{
		Command: BatchGetShopWarehousesByShopIdsCommand,
		System:  constant.SystemShop,
	}
)

// GetShopBatch 默认使用缓存的方法
func (client *SpexClientImpl) GetShopBatch(ctx context.Context, shopIds []int64) ([]*shop_core.ShopDetail, fsserr.Error) {
	// 如果没有缓存管理器，则直接调用无缓存方法
	if client.LayerCacheManager == nil {
		return client.getShopBatchWithoutCache(ctx, shopIds)
	}

	// 将shopIds转换为[]uint64以便处理
	shopIDsUint64 := make([]uint64, len(shopIds))
	for i, id := range shopIds {
		shopIDsUint64[i] = uint64(id)
	}

	return client.getShopBatchWithCache(ctx, shopIDsUint64)
}

// getShopBatchWithoutCache 绕过缓存直接调用下游API（私有方法）
func (client *SpexClientImpl) getShopBatchWithoutCache(ctx context.Context, shopIds []int64) ([]*shop_core.ShopDetail, fsserr.Error) {
	req := &shop_core.GetShopBatchRequest{
		ShopidList: shopIds,
	}
	resp := &shop_core.GetShopBatchResponse{}
	if spexErr := client.request(ctx, GetShopBatch, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetShopBatch, spexErr))
	}
	return resp.GetShopList(), nil
}

// BatchGetShopSipBasic 默认使用缓存的方法
func (client *SpexClientImpl) BatchGetShopSipBasic(ctx context.Context, shopIds []int64) ([]*shop_core.SipBasic, fsserr.Error) {
	req := &shop_core.BatchGetShopSipBasicRequest{
		ShopIdList: shopIds,
	}
	resp := &shop_core.BatchGetShopSipBasicResponse{}
	if spexErr := client.request(ctx, BatchGetShopSipBasic, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(BatchGetShopSipBasic, spexErr))
	}
	return resp.GetSipBasicList(), nil
}

func getShopDetailCacheKey(ctx context.Context, shopID uint64) string {
	return common.GenKeyWithRegion(ctx, ":", "shop_detail", strconv.FormatUint(shopID, 10))
}

// getShopBatchWithCache 实现带缓存的shop批量查询
func (client *SpexClientImpl) getShopBatchWithCache(ctx context.Context, shopIds []uint64) ([]*shop_core.ShopDetail, fsserr.Error) {
	if len(shopIds) == 0 {
		return []*shop_core.ShopDetail{}, nil
	}

	results := make([]*shop_core.ShopDetail, 0, len(shopIds))
	var missingShopIds []uint64
	shopDetailMap := make(map[uint64]*shop_core.ShopDetail)

	// 尝试从缓存中获取
	for _, shopID := range shopIds {
		cacheKey := getShopDetailCacheKey(ctx, shopID)

		// 从缓存获取数据
		cachedData, err := client.LayerCacheManager.Get(ctx, layer_cache.ShopDetailNamespace, cacheKey,
			layer_cache.WithUnmarshalFunc(func(data []byte) (interface{}, error) {
				var shopDetail shop_core.ShopDetail
				if unmarshalErr := proto.Unmarshal(data, &shopDetail); unmarshalErr != nil {
					return nil, unmarshalErr
				}
				return &shopDetail, nil
			}),
		)

		if err == nil && cachedData != nil {
			if shopDetail, ok := cachedData.(*shop_core.ShopDetail); ok {
				shopDetailMap[shopID] = shopDetail
			}
		} else {
			// 缓存未命中，记录需要从下游API获取的shopId
			missingShopIds = append(missingShopIds, shopID)
		}
	}

	// 如果有缺失的shopId，批量从下游API获取
	if len(missingShopIds) > 0 {
		missingShopIdsInt64 := make([]int64, len(missingShopIds))
		for i, id := range missingShopIds {
			missingShopIdsInt64[i] = int64(id)
		}

		// 调用无缓存API获取缺失的数据
		missingShopDetails, err := client.getShopBatchWithoutCache(ctx, missingShopIdsInt64)
		if err != nil {
			return nil, err
		}

		// 将获取到的数据存入缓存并合并到结果中
		for _, shopDetail := range missingShopDetails {
			shopID := uint64(shopDetail.GetShopid())
			shopDetailMap[shopID] = shopDetail
			client.asyncWriteShopDetail(ctx, shopDetail, shopID)
		}
	}

	// 按照原始shopIds的顺序构造结果
	for _, shopID := range shopIds {
		if shopDetail, exists := shopDetailMap[shopID]; exists {
			results = append(results, shopDetail)
		}
	}

	return results, nil
}

// asyncWriteShopDetail 异步写入ShopDetail缓存
func (client *SpexClientImpl) asyncWriteShopDetail(ctx context.Context, shopDetail *shop_core.ShopDetail, shopID uint64) {
	if client.LayerCacheManager == nil || shopDetail == nil {
		return
	}

	cacheKey := getShopDetailCacheKey(ctx, shopID)
	data, err := proto.Marshal(shopDetail)
	if err != nil {
		return // 序列化失败，忽略缓存写入
	}

	// 使用LayerCacheManager的异步写入能力
	_ = client.LayerCacheManager.Set(ctx, layer_cache.ShopDetailNamespace, cacheKey, data, layer_cache.WithAsyncSet())
}

func (client *SpexClientImpl) BatchCheckSellerWarehouseShop(ctx context.Context, shopIds []int64) (*shop_core.BatchCheckSellerWarehouseShopResponse, fsserr.Error) {
	req := &shop_core.BatchCheckSellerWarehouseShopRequest{
		ShopIds: shopIds,
		Region:  proto.String(envvar.GetCID(ctx)),
	}
	resp := &shop_core.BatchCheckSellerWarehouseShopResponse{}
	if spexErr := client.request(ctx, BatchCheckSellerWarehouseShop, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetShopBatch, spexErr))
	}
	return resp, nil
}

func (client *SpexClientImpl) BatchGetShopWarehousesByShopIds(ctx context.Context, shopIds []int64) ([]*shop_core.ShopWarehouse, fsserr.Error) {
	req := &shop_core.BatchGetShopWarehousesByShopIdsRequest{
		ShopId: shopIds,
		Region: proto.String(envvar.GetCID(ctx)),
	}
	resp := &shop_core.BatchGetShopWarehousesByShopIdsResponse{}
	if spexErr := client.request(ctx, BatchGetShopWarehousesByShopIds, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetShopBatch, spexErr))
	}
	return resp.GetWarehouses(), nil
}
