package spexlib

import (
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
)

func TestRequestConfig(t *testing.T) {
	tests := []struct {
		name     string
		opts     []RequestOption
		expected requestConfig
	}{
		{
			name:     "default config",
			opts:     nil,
			expected: requestConfig{},
		},
		{
			name:     "with custom CID",
			opts:     []RequestOption{WithCID("sg")},
			expected: requestConfig{cidOverride: stringPtr("sg")},
		},
		{
			name:     "with default CID",
			opts:     []RequestOption{WithDefaultCID()},
			expected: requestConfig{},
		},
		{
			name:     "override then default",
			opts:     []RequestOption{WithCID("sg"), WithDefaultCID()},
			expected: requestConfig{},
		},
		{
			name:     "multiple custom CIDs - last wins",
			opts:     []RequestOption{WithCID("sg"), WithCID("my")},
			expected: requestConfig{cidOverride: stringPtr("my")},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &requestConfig{}
			for _, opt := range tt.opts {
				opt.apply(config)
			}

			if !equalStringPtr(config.cidOverride, tt.expected.cidOverride) {
				t.Errorf("cidOverride = %v, want %v", config.cidOverride, tt.expected.cidOverride)
			}
		})
	}
}

func TestRequestOptionInterfaces(t *testing.T) {
	// 确保所有选项都实现了 RequestOption 接口
	var opts []RequestOption = []RequestOption{
		WithCID("test"),
		WithDefaultCID(),
	}

	// 应该能正常编译，不会有接口不匹配的错误
	config := &requestConfig{}
	for _, opt := range opts {
		opt.apply(config)
	}
}

func TestSpexServiceCreation(t *testing.T) {
	service := spexService{
		Command: "test_command",
		System:  constant.SystemAccount,
	}

	if service.Command != "test_command" {
		t.Errorf("Command = %v, want test_command", service.Command)
	}
	if service.System != constant.SystemAccount {
		t.Errorf("System = %v, want %v", service.System, constant.SystemAccount)
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func equalStringPtr(a, b *string) bool {
	if a == nil && b == nil {
		return true
	}
	if a == nil || b == nil {
		return false
	}
	return *a == *b
}
