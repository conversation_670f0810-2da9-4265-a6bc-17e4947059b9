package spexlib

import (
	"context"

	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_address.pb"
	account_core64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetAccount = spexService{
		Command: GetAccountCommand,
		System:  constant.SystemAccount,
	}

	GetPrivateAddressWithGeocoding = spexService{
		Command: GetPrivateAddressWithGeocodingCommand,
		System:  constant.SystemAccount,
	}

	GetPrivateAddressListWithGeocoding = spexService{
		Command: GetPrivateAddressListWithGeocodingCommand,
		System:  constant.SystemAccount,
	}
)

func (client *SpexClientImpl) GetAccountDetail(ctx context.Context, userId typ.UserIdType, region meta.Region) (*account_core64.UserDetail, fsserr.Error) {
	req := &account_core64.GetAccountRequest{
		Userid:    proto.Int64(int64(userId)),
		Region:    proto.String(string(region)),
		QueryFlag: proto.Int32(int32(account_core64.Constant_ACCOUNT_QUERY_FLAG_MAIN)),
	}
	resp := &account_core64.GetAccountResponse{}
	if spexErr := client.request(ctx, GetAccount, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetAccount, spexErr))
	}
	if resp.GetUser() == nil {
		return nil, fsserr.New(fsserr.SpexError, "user %d not found", userId)
	}
	return resp.GetUser(), nil
}

func (client *SpexClientImpl) GetPrivateAddressWithGeocoding(ctx context.Context, userId typ.UserIdType, addressID uint64) (*account_address.PrivateAddressGeoCoded, fsserr.Error) {
	req := &account_address.GetPrivateAddressWithGeocodingRequest{
		Region:    proto.String(envvar.GetCID(ctx)),
		Userid:    proto.Int64(int64(userId)),
		AddressId: proto.Int64(int64(addressID)),
	}
	resp := &account_address.GetPrivateAddressWithGeocodingResponse{}
	if spexErr := client.request(ctx, GetPrivateAddressWithGeocoding, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetPrivateAddressWithGeocoding, spexErr))
	}
	if resp.GetAddress() == nil {
		return nil, fsserr.New(fsserr.SpexError, "user %d address %d not found", userId, addressID)
	}
	return resp.GetAddress(), nil
}

func (client *SpexClientImpl) BatchGetPrivateAddressWithGeocoding(ctx context.Context, userId typ.UserIdType, addressIDs []uint64) (map[uint64]*account_address.PrivateAddressGeoCoded, fsserr.Error) {
	var addressInt64IDs []int64
	for _, id := range addressIDs {
		addressInt64IDs = append(addressInt64IDs, int64(id))
	}
	caller := concurrency.NewConcurrencySplitCaller[int64, *account_address.PrivateAddressGeoCoded]()
	resp, err := caller.Call(
		ctx,
		addressInt64IDs,
		20,
		client.batchGetAddressWithGeo(userId),
	)
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	addressIDToAddressWithGeo := make(map[uint64]*account_address.PrivateAddressGeoCoded)
	for _, addressWithGeo := range resp {
		addressIDToAddressWithGeo[uint64(addressWithGeo.GetBasic().GetAddressId())] = addressWithGeo
	}
	return addressIDToAddressWithGeo, nil
}

func (client *SpexClientImpl) batchGetAddressWithGeo(userID typ.UserIdType) concurrency.SplitCallerFunc[int64, *account_address.PrivateAddressGeoCoded] {
	return func(ctx context.Context, queries []int64) ([]*account_address.PrivateAddressGeoCoded, error) {
		req := &account_address.GetPrivateAddressListWithGeocodingRequest{
			Region:        proto.String(envvar.GetCID(ctx)),
			Userid:        proto.Int64(int64(userID)),
			AddressIdList: queries,
		}
		addressList, err := client.getPrivateAddressListWithGeocoding(ctx, req)
		if err != nil {
			return nil, err
		}
		return addressList, nil
	}
}

func (client *SpexClientImpl) getPrivateAddressListWithGeocoding(ctx context.Context, req *account_address.GetPrivateAddressListWithGeocodingRequest) ([]*account_address.PrivateAddressGeoCoded, fsserr.Error) {
	resp := &account_address.GetPrivateAddressListWithGeocodingResponse{}
	if spexErr := client.request(ctx, GetPrivateAddressListWithGeocoding, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetPrivateAddressListWithGeocoding, spexErr))
	}
	if resp.GetAddressList() == nil {
		return nil, fsserr.New(fsserr.SpexError, "user %d address", req.GetUserid())
	}
	return resp.GetAddressList(), nil
}
