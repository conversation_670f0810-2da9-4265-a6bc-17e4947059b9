package spexlib

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_item_itemaggregation_iteminfo.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_itemtagservice_querying_api.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_logistics_shop_channels.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_cb_collection_api.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_fulfilment_sbs.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetSbsItemInfo = spexService{
		Command: GetSbsItemInfoCommand,
		System:  constant.SystemMarketplace,
	}

	GetSbsShipmentGroupInfo = spexService{
		Command: GetSbsShipmentGroupInfoCommand,
		System:  constant.SystemMarketplace,
	}

	GetItemLabels = spexService{
		Command: GetItemLabelsCommand,
		System:  constant.SystemMarketplace,
	}

	GetModelLabels = spexService{
		Command: GetModelLabelsCommand,
		System:  constant.SystemMarketplace,
	}

	GetDummyBuyerId = spexService{
		Command: GetDummyBuyerIdCommand,
		System:  constant.SystemMarketplace,
	}

	BatchGetShopChannels = spexService{
		Command: BatchGetShopChannelsCommand,
		System:  constant.SystemMarketplace,
	}

	GetProductInfo = spexService{
		Command: GetProductInfoCommand,
		System:  constant.SystemMarketplace,
	}

	GetChannels = spexService{
		Command: GetChannelsCommand,
		System:  constant.SystemMarketplace,
	}
)

func (client *SpexClientImpl) GetSbsItemInfo(ctx context.Context, shopItems []*marketplace_order_processing_fulfilment_sbs.ItemParam) ([]*marketplace_order_processing_fulfilment_sbs.SbsShopItemInfo, fsserr.Error) {
	request := &marketplace_order_processing_fulfilment_sbs.GetSbsItemInfoRequest{
		RequestId:         proto.String(ctxutils.GetCtxData(ctx).GetRequestId()),
		Items:             shopItems,
		EnabledModelLevel: proto.Bool(true),
	}
	resp := &marketplace_order_processing_fulfilment_sbs.GetSbsItemInfoResponse{}
	if spexErr := client.request(ctx, GetSbsItemInfo, request, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetSbsItemInfo, spexErr))
	}
	return resp.GetShopItemInfo(), nil
}

func (client *SpexClientImpl) GetSBSShipmentGroups(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, fsserr.Error) {
	request := &marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupInfoRequest{
		ShipmentGroupIdList: groupIDs,
	}
	resp := &marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupInfoResponse{}
	if spexErr := client.request(ctx, GetSbsShipmentGroupInfo, request, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetSbsShipmentGroupInfo, spexErr))
	}
	return resp.GetShipmentGroupDetailsList(), nil
}

func (client *SpexClientImpl) GetItemLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ItemLabel, fsserr.Error) {
	resp := &marketplace_listing_itemtagservice_querying_api.GetItemLabelsResponse{}
	if spexErr := client.request(ctx, GetItemLabels, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetItemLabels, spexErr))
	}
	return resp.GetItemLabelList(), nil
}

func (client *SpexClientImpl) GetModelLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetModelLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ModelLabel, fsserr.Error) {
	resp := &marketplace_listing_itemtagservice_querying_api.GetModelLabelsResponse{}
	if spexErr := client.request(ctx, GetModelLabels, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetModelLabels, spexErr))
	}
	return resp.GetModelLabelList(), nil
}

func (client *SpexClientImpl) GetDummyBuyerId(ctx context.Context, cid string) (*marketplace_order_processing_cb_collection_api.GetDummyBuyerIdResponse, fsserr.Error) {
	var (
		req  = &marketplace_order_processing_cb_collection_api.GetDummyBuyerIdRequest{}
		resp = &marketplace_order_processing_cb_collection_api.GetDummyBuyerIdResponse{}
	)
	if spexErr := client.request(ctx, GetDummyBuyerId, req, resp, WithCID(cid)); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetDummyBuyerId, spexErr))
	}
	return resp, nil
}

func (client *SpexClientImpl) BatchGetShopChannels(ctx context.Context, req *marketplace_logistics_shop_channels.BatchGetShopChannelsRequest) ([]*marketplace_logistics_shop_channels.ShopDisplayLogisticsChannels, error) {
	resp := &marketplace_logistics_shop_channels.BatchGetShopChannelsResponse{}
	if spexErr := client.request(ctx, BatchGetShopChannels, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(BatchGetShopChannels, spexErr))
	}
	return resp.GetShopLogisticsChannels(), nil
}

func (client *SpexClientImpl) GetProductInfo(ctx context.Context, req *marketplace_listing_item_itemaggregation_iteminfo.GetProductInfoRequest) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error) {
	resp := &marketplace_listing_item_itemaggregation_iteminfo.GetProductInfoResponse{}
	if spexErr := client.request(ctx, GetProductInfo, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetProductInfo, spexErr))
	}
	return resp.GetInfo(), nil
}

func (client *SpexClientImpl) GetChannels(ctx context.Context, req *marketplace_logistics_shop_channels.GetChannelsRequest) ([]*marketplace_logistics_shop_channels.Channel, error) {
	resp := &marketplace_logistics_shop_channels.GetChannelsResponse{}
	if spexErr := client.request(ctx, GetChannels, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetChannels, spexErr))
	}
	return resp.GetChannelList(), nil
}
