package spexlib

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_logistics_shop_channels.pb"
	"github.com/dolthub/swiss"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
)

func DumpMPChannels(ctx context.Context, confAccessor config.ConfAccessor) (*swiss.Map[uint64, *marketplace_logistics_shop_channels.Channel], error) {
	spexClient := NewBasicSpexClientImpl(confAccessor)
	channels, err := spexClient.GetChannels(ctx, &marketplace_logistics_shop_channels.GetChannelsRequest{})
	if err != nil {
		return nil, err
	}
	ret := swiss.NewMap[uint64, *marketplace_logistics_shop_channels.Channel](uint32(len(channels)))
	for _, channel := range channels {
		ret.Put(channel.GetChannelId(), channel)
	}
	return ret, nil
}
