package spexlib

import (
	"testing"
)

func TestBuildSpexURL(t *testing.T) {
	tests := []struct {
		name     string
		command  string
		cid      string
		expected string
	}{
		{
			name:     "empty cid should return command as is",
			command:  "test.command",
			cid:      "",
			expected: "test.command",
		},
		{
			name:     "simple command with cid",
			command:  "account.core.get_account",
			cid:      "sg",
			expected: "account.core.get_account?sg",
		},
		{
			name:     "command with existing query params",
			command:  "test.command?existing=param",
			cid:      "my",
			expected: "test.command?my",
		},
		{
			name:     "complex cid",
			command:  "marketplace.listing.get_items",
			cid:      "region=sg&env=prod",
			expected: "marketplace.listing.get_items?region=sg&env=prod",
		},
		{
			name:     "invalid URL should fallback to simple concatenation",
			command:  "://invalid-url",
			cid:      "sg",
			expected: "://invalid-url?sg",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := buildSpexURL(tt.command, tt.cid)
			if result != tt.expected {
				t.Errorf("buildSpexURL(%q, %q) = %q, want %q", tt.command, tt.cid, result, tt.expected)
			}
		})
	}
}

func TestBuildSpexURLCompatibility(t *testing.T) {
	// 测试与原始实现的兼容性
	testCases := []struct {
		command string
		cid     string
	}{
		{"account.core.get_account", "sg"},
		{"marketplace.listing.get_items", "my"},
		{"shop.core.get_shop_batch", "th"},
	}

	for _, tc := range testCases {
		// 原始实现
		originalResult := tc.command + "?" + tc.cid

		// 新实现
		newResult := buildSpexURL(tc.command, tc.cid)

		if newResult != originalResult {
			t.Errorf("Compatibility test failed for command=%q, cid=%q: got %q, want %q",
				tc.command, tc.cid, newResult, originalResult)
		}
	}
}
