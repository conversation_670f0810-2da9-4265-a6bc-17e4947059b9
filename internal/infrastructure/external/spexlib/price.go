package spexlib

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/price_checkout_promo.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetCheckoutStockInfo = spexService{
		Command: GetCheckoutStockInfoCommand,
		System:  constant.SystemPrice,
	}
)

func (client *SpexClientImpl) GetCheckoutStockInfo(ctx context.Context, request *price_checkout_promo.GetCheckoutStockInfoRequest) ([]*price_checkout_promo.CheckoutOrderStockInfo, fsserr.Error) {
	resp := &price_checkout_promo.GetCheckoutStockInfoResponse{}
	if spexErr := client.request(ctx, GetCheckoutStockInfo, request, resp); spexErr != nil {
		// todo enrich error
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetCheckoutStockInfo, spexErr))
	}
	return resp.GetCheckoutOrderStockInfo(), nil
}
