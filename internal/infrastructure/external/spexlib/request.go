package spexlib

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/scsps"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	sp_common "git.garena.com/shopee/sp_protocol/golang/common.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/timeutil"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

const (
	defaultSpexTimeout = time.Duration(1000) * time.Millisecond
)

// buildSpexURL 构建带有查询参数的 Spex URL
// 根据原始实现，CID 是直接作为查询字符串添加的，而不是作为命名参数
func buildSpexURL(command, cid string) string {
	if cid == "" {
		return command
	}

	// 使用 net/url 包构建 URL，更加安全和标准
	u, err := url.Parse(command)
	if err != nil {
		// 如果解析失败，回退到简单拼接
		return command + "?" + cid
	}

	// 根据原始逻辑，直接设置 RawQuery 为 CID
	u.RawQuery = cid

	return u.String()
}

// RequestOption 定义请求选项接口
type RequestOption interface {
	apply(*requestConfig)
}

// requestConfig 包含请求的配置选项
type requestConfig struct {
	cidOverride *string // 如果不为 nil，则使用指定的 CID
}

// cidOption 实现 CID 相关的选项
type cidOption struct {
	cid *string
}

func (c cidOption) apply(config *requestConfig) {
	config.cidOverride = c.cid
}

// WithCID 指定特定的 CID 值
func WithCID(cid string) RequestOption {
	cid = strings.ToLower(cid)
	return cidOption{cid: &cid}
}

// WithDefaultCID 使用默认的 CID 获取逻辑（从 envvar 获取），这是默认行为
func WithDefaultCID() RequestOption {
	return cidOption{cid: nil}
}

type spexService struct {
	Command string
	System  constant.SystemCode
}

type spexError struct {
	spexCode uint32
	err      error
}

func (e *spexError) Error() string {
	if e == nil {
		return ""
	}
	return e.err.Error()
}

func (e *spexError) Code() uint32 {
	if e == nil {
		return 0
	}
	return e.spexCode
}

func (client *SpexClientImpl) request(ctx context.Context, spexService spexService, request, response interface{},
	opts ...RequestOption) *spexError {

	startTime := timeutil.Now(ctx)
	logTraceNumPtr := ctxutils.GetLogTraceNum(ctx)
	command := spexService.Command

	err := client.doRequest(ctx, spexService, request, response, opts...)

	if logTraceNumPtr != nil && *logTraceNumPtr > 0 {
		var logTraceNum uint32
		ctx, logTraceNum = ctxutils.SetLogTraceNum(ctx)
		traceEntity := ctxutils.TraceEntity{
			LogTrace:  logTraceNum,
			Name:      command,
			System:    spexService.System,
			StartTime: startTime.String(),
			FromCache: false,
			Cost:      time.Since(startTime).String(),
		}
		if err != nil {
			traceEntity.Request = Logger.JsonString(request)
			traceEntity.Response = Logger.JsonString(response)
			traceEntity.Error = err.Error()
		}
		_ = ctxutils.SetLogTraceInfo(ctx, traceEntity)
	}

	return err
}

func (client *SpexClientImpl) doRequest(ctx context.Context, spexService spexService, request, response interface{}, opts ...RequestOption) *spexError {
	command := spexService.Command

	// 应用配置选项
	config := &requestConfig{}
	for _, opt := range opts {
		opt.apply(config)
	}

	// 处理 CID 设置（CID 是必填项）
	var cid string
	if config.cidOverride != nil {
		// 使用指定的 CID
		cid = *config.cidOverride
	} else {
		// 使用默认逻辑从 envvar 获取 CID
		cid = envvar.GetCIDLower(ctx)
	}

	// 在 spex 调用的 context 增加 cid 信息
	if newCtx, err := viewercontext.WithCID(ctx, cid); err != nil {
		Logger.CtxLogDebugf(ctx, "Set viewer context to context fail, cid=%s, err:%v", cid, err)
	} else {
		ctx = newCtx
	}

	timeout := client.getSpexTimeout(ctx, spexService)
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 使用优雅的URL构建方式
	commandWithCID := buildSpexURL(command, cid)
	spexErr := client.rpcRequest(ctx, commandWithCID, timeout, request, response)
	return spexErr
}

func (client *SpexClientImpl) rpcRequest(ctx context.Context, command Command, timeout time.Duration, request, response interface{}) *spexError {
	ctx, endReportFunc := monitorutils.BusinessAwesomeReportStarted(ctx)
	var (
		respCode   uint32
		statusCode = constant.StatusSuccess
		data       = command
	)
	defer func() {
		_ = endReportFunc(constant.CatModuleSpex, command, statusCode, data)
	}()
	respCode = scsps.RPCRequest(ctx, command, request, response)
	reqStr := Logger.JsonStringForDebugLog(ctx, request)
	respStr := Logger.JsonStringForDebugLog(ctx, response)
	if respCode == 0 {
		if Logger.IsBizDebugLogEnabled(ctx) {
			Logger.CtxLogDebugf(ctx, "request spex success, command=%s, req=%s, resp=%s", command, reqStr, respStr)
		}
		return nil
	}

	err := scsps.CodeToError(respCode)
	spexErr := &spexError{
		spexCode: respCode,
		err:      err,
	}
	statusCode = strconv.FormatUint(uint64(respCode), 10)
	data = fmt.Sprintf("command=%s, err=%v", command, spexErr)
	if respCode == uint32(sp_common.Constant_ERROR_TIMEOUT) {
		Logger.CtxLogErrorf(ctx, "request spex timeout, command=%s, req=%s, timeout=%v,err=%v", command, reqStr, timeout, spexErr)

	} else {
		Logger.CtxLogErrorf(ctx, "request spex error, spex command=%s, req=%s, response=%s, err=%v", command, reqStr, respStr, spexErr)
	}
	return spexErr
}

func (client *SpexClientImpl) getSpexTimeout(ctx context.Context, spexService spexService) time.Duration {
	tc := client.ConfAccessor.GetSpexTimeoutConfig(ctx)
	if t, ok := tc.GetTimeout(spexService.System, spexService.Command); ok {
		return t
	}
	return defaultSpexTimeout
}
