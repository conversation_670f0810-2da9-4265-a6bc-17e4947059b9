package spexlib

import (
	"context"

	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_address_core.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_tag_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	BatchGetEntityTagValue = spexService{
		Command: BatchGetEntityTagValueCommand,
		System:  constant.SystemSeller,
	}

	BatchGetEntityTag = spexService{
		Command: BatchGetEntityTagCommand,
		System:  constant.SystemSeller,
	}

	BatchGetWarehouseFlagByShop = spexService{
		Command: BatchGetWarehouseFlagByShopCommand,
		System:  constant.SystemSeller,
	}

	BatchGetWarehouseByShopWithoutPagination = spexService{
		Command: BatchGetWarehouseByShopWithoutPaginationCommand,
		System:  constant.SystemSeller,
	}
)

func (client *SpexClientImpl) BatchGetEntityTagValue(ctx context.Context, req *seller_seller_tag_core.BatchGetEntityTagValueRequest) ([]*seller_seller_tag_core.EntityTag, fsserr.Error) {
	resp := &seller_seller_tag_core.BatchGetEntityTagResponse{}
	if spexErr := client.request(ctx, BatchGetEntityTagValue, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(BatchGetEntityTagValue, spexErr))
	}
	return resp.GetEntityTags(), nil
}

func (client *SpexClientImpl) BatchGetEntityTag(ctx context.Context, req *seller_seller_tag_core.BatchGetEntityTagRequest) ([]*seller_seller_tag_core.EntityTag, fsserr.Error) {
	resp := &seller_seller_tag_core.BatchGetEntityTagResponse{}
	if spexErr := client.request(ctx, BatchGetEntityTag, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(BatchGetEntityTag, spexErr))
	}
	return resp.GetEntityTags(), nil
}

func (client *SpexClientImpl) BatchGetWarehouseFlagByShop(ctx context.Context, shopIds []int64) ([]*seller_seller_address_core.ShopWarehouseFlag, fsserr.Error) {
	req := &seller_seller_address_core.BatchGetWarehouseFlagByShopRequest{
		ShopIdList: shopIds,
		Region:     proto.String(envvar.GetCID(ctx)),
	}
	resp := &seller_seller_address_core.BatchGetWarehouseFlagByShopResponse{}
	if spexErr := client.request(ctx, BatchGetWarehouseFlagByShop, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(BatchGetWarehouseFlagByShop, spexErr))
	}
	return resp.GetWarehouseFlagList(), nil
}

func (client *SpexClientImpl) BatchGetWarehouseByShopWithoutPagination(ctx context.Context, shopIds []int64) ([]*seller_seller_address_core.ShopWarehouse, fsserr.Error) {
	req := &seller_seller_address_core.BatchGetWarehouseByShopWithoutPaginationRequest{
		ShopIdList: shopIds,
		Region:     proto.String(envvar.GetCID(ctx)),
	}
	resp := &seller_seller_address_core.BatchGetWarehouseByShopWithoutPaginationResponse{}
	if spexErr := client.request(ctx, BatchGetWarehouseByShopWithoutPagination, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(BatchGetWarehouseByShopWithoutPagination, spexErr))
	}
	return resp.GetWarehouseList(), nil
}
