package httplib

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
)

type (
	Request interface {
		Queries() map[string]string
		Entity() ([]byte, error)
	}

	Response interface {
		IsSuccess() bool
		FailMsg() string
	}

	Header interface {
		Header(ctx context.Context) (map[string]string, error)
	}

	HttpService struct {
		Endpoint string
		Scene    constant.Scene
		System   constant.SystemCode
	}
)

func (s HttpService) Copy() HttpService {
	return HttpService{
		Endpoint: s.Endpoint,
		Scene:    s.Scene,
		System:   s.System,
	}
}

type (
	Option struct {
		Region  meta.Region   `json:"region"`
		Timeout time.Duration `json:"timeout"`
	}

	Options func(o *Option)
)

func WithRegion(region meta.Region) Options {
	return func(o *Option) {
		o.Region = region
	}
}

func WithTimeout(timeout time.Duration) Options {
	return func(o *Option) {
		o.Timeout = timeout
	}
}
