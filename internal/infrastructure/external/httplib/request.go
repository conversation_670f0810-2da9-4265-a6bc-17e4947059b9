package httplib

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/utils/httputil"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"github.com/bytedance/sonic"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/mocker"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/timeutil"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

var (
	invoker = chassis.NewRestInvoker()
)

var (
	errInvalidParams = errors.New("invalid request params")
)

func remoteInvokeWithUrl(ctx context.Context, serviceCode constant.ServiceCode, httpService HttpService, url string, method string, timeout time.Duration, req Request, rsp Response, h Header) error {
	startTime := timeutil.Now(ctx)
	logTraceNumPtr := ctxutils.GetLogTraceNum(ctx)

	err := doRemoteInvokeWithUrl(ctx, url, method, timeout, req, rsp, h)

	if logTraceNumPtr != nil && *logTraceNumPtr > 0 {
		var logTraceNum uint32
		ctx, logTraceNum = ctxutils.SetLogTraceNum(ctx)
		traceEntity := ctxutils.TraceEntity{
			LogTrace:  logTraceNum,
			Name:      httpService.Endpoint,
			StartTime: startTime.String(),
			System:    httpService.System,
			Service:   serviceCode,
			FromCache: false,
			Cost:      time.Since(startTime).String(),
		}
		if err != nil {
			traceEntity.Request = Logger.JsonString(req)
			traceEntity.Response = Logger.JsonString(rsp)
			traceEntity.Error = err.Error()
		}
		_ = ctxutils.SetLogTraceInfo(ctx, traceEntity)
	}
	return err
}
func doRemoteInvokeWithUrl(ctx context.Context, url string, method string, timeout time.Duration, req Request, rsp Response, h Header) error {
	var header map[string]string = nil

	if h != nil {
		var err error
		header, err = h.Header(ctx)
		if err != nil {
			Logger.CtxLogErrorf(ctx, "remote_invoke_fail|get_header_fail,url=%s,error=%+v", url, err)
			return err
		}
	}

	if header == nil {
		header = make(map[string]string)
	}
	if header[constant.HeaderToken] == "" {
		header[constant.HeaderToken] = "token"
	}
	if header[constant.HeaderAccount] == "" {
		header[constant.HeaderAccount] = "FSS"
	}
	if header[constant.HeaderRequestID] == "" {
		header[constant.HeaderRequestID] = ctxutils.GetCtxData(ctx).GetRequestId()
	}
	if mockValues := mocker.MockValues(ctx); len(mockValues) > 0 {
		for k, v := range mockValues {
			header[k] = v
		}
	}
	if method == http.MethodGet {
		var queries map[string]string = nil
		if req != nil {
			queries = req.Queries()
		}
		resp, err := get(ctx, url, queries, timeout, header)
		if err != nil {
			Logger.CtxLogErrorf(ctx, "remote_invoke_fail|http_get_fail,url=%s,header=%s,request=%s,error=%+v", url, Logger.JsonString(header), Logger.JsonString(queries), err)
			return err
		}
		if err = sonic.Unmarshal(resp, rsp); err != nil {
			Logger.CtxLogErrorf(ctx, "remote_invoke_fail|unmarshal_fail,url=%s,response=%v, err=%v", url, string(resp), err)
			return err
		}
		Logger.CtxLogDebugf(ctx, "remote_invoke_get|url=%s,header=%s,request=%s,response=%s", url, Logger.JsonStringForDebugLog(ctx, header), Logger.JsonStringForDebugLog(ctx, queries), Logger.JsonStringForDebugLog(ctx, rsp))
		if !rsp.IsSuccess() {
			return fmt.Errorf("%s,url=[%s],rsp=%+v", rsp.FailMsg(), url, Logger.JsonString(rsp))
		}
		return nil
	} else if method == http.MethodPost {
		body, err := req.Entity()
		if err != nil {
			Logger.CtxLogErrorf(ctx, "remote_invoke_fail|marshal_error,url=%s,error=%+v", url, err)
			return err
		}
		resp, err := post(ctx, url, body, timeout, header)
		if err != nil {
			Logger.CtxLogErrorf(ctx, "remote_invoke_fail|http_post_fail,url=%s,header=%s,request=%v,error=%+v", url, Logger.JsonString(header), Logger.JsonString(req), err)
			return err
		}
		if err = sonic.Unmarshal(resp, rsp); err != nil {
			Logger.CtxLogErrorf(ctx, "remote_invoke_fail|unmarshal_fail,url=%s,response=%v, err=%v", url, string(resp), err)
			return err
		}
		Logger.CtxLogDebugf(ctx, "remote_invoke_post|url=%s,header=%s,request=%s,response=%s", url, Logger.JsonStringForDebugLog(ctx, header), Logger.JsonStringForDebugLog(ctx, req), Logger.JsonStringForDebugLog(ctx, rsp))
		if !rsp.IsSuccess() {
			return fmt.Errorf("%s,url=[%s],rsp=%+v", rsp.FailMsg(), url, Logger.JsonString(rsp))
		}
		return nil
	}
	return errInvalidParams
}

func post(ctx context.Context, url string, data []byte, timeout time.Duration, headers map[string]string) ([]byte, error) {
	if len(headers) == 0 {
		headers = map[string]string{"Content-Type": "application/json"}
	} else {
		headers["Content-Type"] = "application/json"
	}
	return request(ctx, http.MethodPost, url, data, nil, timeout, headers)
}

func get(ctx context.Context, url string, param map[string]string, timeout time.Duration, headers map[string]string) ([]byte, error) {
	return request(ctx, http.MethodGet, url, nil, param, timeout, headers)
}

func request(ctx context.Context, method string, url string, data []byte, param map[string]string, timeout time.Duration, headers map[string]string) ([]byte, error) {
	var (
		req *http.Request
		err error
	)
	if method == http.MethodPost {
		req, err = chassis.NewRestRequest(method, url, bytes.NewBuffer(data).Bytes())
		if err != nil {
			return nil, err
		}
	} else {
		req, err = chassis.NewRestRequest(method, url, nil)
		if err != nil {
			return nil, err
		}
	}
	if headers == nil {
		headers = make(map[string]string)
	}

	// 向下传递 log hit
	if Logger.IsLogHit(ctx) {
		headers[constant.LogHitKey] = "true"
	}

	for hKey, hVal := range headers {
		req.Header.Add(hKey, hVal)
	}
	if method == http.MethodGet {
		q := req.URL.Query()
		for paramKey, paramVal := range param {
			q.Add(paramKey, paramVal)
		}
		req.URL.RawQuery = q.Encode()
	}

	ctx, reportFunc := monitor.AwesomeReportTransactionStart2(ctx)
	defer func() {
		if err != nil {
			reportFunc(fmt.Sprintf("%s.%s", constant.CatModuleAPI, req.URL.Host), req.URL.Path, monitor.StatusError, err.Error())
		} else {
			reportFunc(fmt.Sprintf("%s.%s", constant.CatModuleAPI, req.URL.Host), req.URL.Path, monitor.StatusSuccess, "")
		}

	}()

	// invoker.Invoke 每次调用都会重新获取 invoker 的配置，配置采用通知机制，支持实时配置
	//                需要带上chassis.WithoutServiceDiscovery，避免使用服务发现
	rsp, err := invoker.Invoke(ctx, req, chassis.WithRequestTimeout(timeout), chassis.WithoutServiceDiscovery())
	if err != nil {
		return nil, err
	}
	if rsp.StatusCode != http.StatusOK {
		err = fmt.Errorf("http status code %v", rsp.StatusCode)
		return nil, err
	}
	defer rsp.Body.Close()
	ret, err := httputil.FastReadBody(rsp.Body)
	if err != nil {
		return nil, err
	}
	return ret, nil
}
