package httplib

import (
	"context"
	"errors"
	"net/http"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/mocker"
)

const (
	defaultHttpDRTimeout         = time.Duration(6000) * time.Millisecond
	defaultHttpMultiAliveTimeout = time.Duration(2000) * time.Millisecond
)

var (
	errHostNotFound = errors.New("host not found")
)

type Client interface {
	Get(ctx context.Context, serviceCode constant.ServiceCode, httpService HttpService, req Request, rsp Response, header Header, opts ...Options) error
	Post(ctx context.Context, serviceCode constant.ServiceCode, httpService HttpService, req Request, rsp Response, header Header, opts ...Options) error

	GetHost(ctx context.Context, region meta.Region, serviceCode constant.ServiceCode) (string, error)
	GetBaseHost(ctx context.Context, region meta.Region, serviceCode constant.ServiceCode) (string, error)
}

type basicClientImpl struct {
	confAccessor config.ConfAccessor
	hosts        map[constant.ServiceCode]map[meta.Region]string
	baseHosts    map[constant.ServiceCode]map[meta.Region]string
}

func NewBasicClient(confAccessor config.ConfAccessor, serviceCodes ...constant.ServiceCode) (Client, error) {
	hosts := make(map[constant.ServiceCode]map[meta.Region]string, len(serviceCodes))
	baseHosts := make(map[constant.ServiceCode]map[meta.Region]string, len(serviceCodes))

	regions := meta.GetSupportedRegion()
	for _, serviceCode := range serviceCodes {
		hostsUnderServiceCode := make(map[meta.Region]string, len(regions))
		baseHostsUnderServiceCode := make(map[meta.Region]string, len(regions))
		for _, region := range regions {
			host, err := getHost(context.Background(), region, serviceCode)
			if err != nil {
				return nil, err
			}
			hostsUnderServiceCode[region] = "https://" + host
			baseHostsUnderServiceCode[region] = host
		}
		hosts[serviceCode] = hostsUnderServiceCode
		baseHosts[serviceCode] = baseHostsUnderServiceCode
	}
	return &basicClientImpl{
		confAccessor: confAccessor,
		hosts:        hosts,
		baseHosts:    baseHosts,
	}, nil
}

func (c *basicClientImpl) Get(ctx context.Context, serviceCode constant.ServiceCode, httpService HttpService, req Request, rsp Response, header Header, opts ...Options) error {
	return c.request(ctx, serviceCode, httpService, http.MethodGet, req, rsp, header, opts...)
}

func (c *basicClientImpl) Post(ctx context.Context, serviceCode constant.ServiceCode, httpService HttpService, req Request, rsp Response, header Header, opts ...Options) error {
	return c.request(ctx, serviceCode, httpService, http.MethodPost, req, rsp, header, opts...)
}

func (c *basicClientImpl) request(ctx context.Context, serviceCode constant.ServiceCode, httpService HttpService, method string, req Request, rsp Response, header Header, opts ...Options) error {
	opt := Option{}
	for _, o := range opts {
		o(&opt)
	}
	region := envvar.GetRegion(ctx)
	if opt.Region != "" {
		region = opt.Region
	}

	host, err := c.GetHost(ctx, region, serviceCode)
	if err != nil {
		return err
	}
	url := host + httpService.Endpoint
	timeout := c.getHttpTimeout(ctx, httpService, opt)

	return remoteInvokeWithUrl(ctx, serviceCode, httpService, url, method, timeout, req, rsp, header)
}

func (c *basicClientImpl) GetHost(ctx context.Context, region meta.Region, serviceCode constant.ServiceCode) (string, error) {
	if isUseMock(ctx, serviceCode) {
		return constant.MockHost, nil
	}
	if len(c.hosts) == 0 {
		return "", errHostNotFound
	}
	hostsUnderServiceCode, ok := c.hosts[serviceCode]
	if !ok {
		return "", errHostNotFound
	}
	return hostsUnderServiceCode[region], nil
}

func (c *basicClientImpl) GetBaseHost(ctx context.Context, region meta.Region, serviceCode constant.ServiceCode) (string, error) {
	if len(c.baseHosts) == 0 {
		return "", errHostNotFound
	}
	hostsUnderServiceCode, ok := c.baseHosts[serviceCode]
	if !ok {
		return "", errHostNotFound
	}
	return hostsUnderServiceCode[region], nil
}

func (c *basicClientImpl) getHttpTimeout(ctx context.Context, httpService HttpService, option Option) time.Duration {
	if option.Timeout > 0 {
		return option.Timeout
	}
	timeout := defaultHttpDRTimeout
	if httpService.Scene == constant.MultiAlive {
		timeout = defaultHttpMultiAliveTimeout
	}
	httpTimeout := c.confAccessor.GetHttpTimeoutConfig(ctx)
	if tmpTimeout, ok := httpTimeout.GetTimeout(httpService.System, httpService.Endpoint); ok && tmpTimeout > 0 {
		timeout = tmpTimeout
	}
	return timeout
}

func isUseMock(ctx context.Context, serviceCode constant.ServiceCode) bool {
	if mockMap := mocker.MockValues(ctx); len(mockMap) > 0 {
		if mockSys, ok := mockMap[constant.MockSystemsKey]; ok {
			systems := strings.Split(mockSys, ",")
			for _, system := range systems {
				if string(serviceCode) == system {
					return true
				}
			}
		}
	}
	return false
}

func getHost(ctx context.Context, region meta.Region, serviceCode constant.ServiceCode) (string, error) {
	if envvar.IsLivetest(ctx) {
		switch serviceCode {
		case constant.ServiceLpsApi:
			return buildHostWithRegion(ctx, region, "api-lps-livetest.ssc", "shopee"), nil
		}
	}
	switch serviceCode {
	case constant.ServiceOmsApi:
		return buildHostWithRegion(ctx, region, "oms", "shopee"), nil
	case constant.ServiceLpsApi:
		return buildHostWithRegion(ctx, region, "api.lps", "shopee"), nil
	case constant.ServiceSBSApi:
		return buildHostWithoutRegion(ctx, "sbs-portal", "shopee.com"), nil
	}
	return "", errHostNotFound
}

// buildHostWithRegion return {{prefix}}.env.{{suffix}}.cid
func buildHostWithRegion(ctx context.Context, region meta.Region, prefix string, suffix string) string {
	var builder strings.Builder
	builder.WriteString(prefix)
	if !envvar.IsLive(ctx) {
		env := meta.GetUrlEnv(envvar.GetEnv(ctx))
		builder.WriteByte('.')
		builder.WriteString(env)
	}
	builder.WriteByte('.')
	builder.WriteString(suffix)
	builder.WriteByte('.')
	builder.WriteString(meta.GetDomainFromRegion(region))
	return builder.String()
}

// buildHostWithoutRegion return {{prefix}}.env.{{suffix}}
func buildHostWithoutRegion(ctx context.Context, prefix string, suffix string) string {
	var builder strings.Builder
	builder.WriteString(prefix)
	if !envvar.IsLive(ctx) {
		env := meta.GetUrlEnv(envvar.GetEnv(ctx))
		builder.WriteByte('.')
		builder.WriteString(env)
	}
	builder.WriteByte('.')
	builder.WriteString(suffix)
	return builder.String()
}
