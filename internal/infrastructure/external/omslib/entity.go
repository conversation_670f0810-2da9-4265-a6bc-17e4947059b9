package omslib

import (
	"strconv"
)

type (
	RespHeader struct {
		SeqId   string `json:"seq_id"`
		Retcode int32  `json:"retcode"`
		Message string `json:"message"`
		Rettype string `json:"rettype"`
	}
)

type (
	WhsPriorityBatchRequest struct {
		Country  string `url:"country"`
		State    string `url:"state"`
		City     string `url:"city"`
		District string `url:"district"`
		ShopID   uint64 `url:"shop_id"`
	}
)

func (w *WhsPriorityBatchRequest) Queries() map[string]string {
	return nil
}

func (w *WhsPriorityBatchRequest) Params() []string {
	var params []string
	if w.Country != "" {
		params = append(params, "country="+w.Country)
	}
	if w.State != "" {
		params = append(params, "state="+w.State)
	}
	if w.City != "" {
		params = append(params, "city="+w.City)
	}
	if w.District != "" {
		params = append(params, "district="+w.District)
	}
	if w.ShopID != 0 {
		params = append(params, "shop_id="+strconv.FormatUint(w.ShopID, 10))
	}
	return params
}

func (w *WhsPriorityBatchRequest) Entity() ([]byte, error) {
	return nil, nil
}

type (
	WhsPriorityBatchResponse struct {
		Header *RespHeader         `json:"header"`
		Data   []WhsPriorityEntity `json:"data"`
	}

	WhsPriorityEntity struct {
		WarehouseID string  `json:"whs_id"`
		Priority    int     `json:"priority"`
		ESF         float64 `json:"esf"`
	}
)

func (r *WhsPriorityBatchResponse) GetHeader() *RespHeader {
	if r == nil {
		return nil
	}
	return r.Header
}

func (r *WhsPriorityBatchResponse) IsSuccess() bool {
	return r.GetHeader().IsSuccess()
}

func (r *WhsPriorityBatchResponse) FailMsg() string {
	if r == nil {
		return "oms response is nil"
	}
	return r.GetHeader().FailMsg()
}

func (r *RespHeader) IsSuccess() bool {
	return r != nil && r.Retcode == 0
}

func (r *RespHeader) FailMsg() string {
	if r == nil {
		return "oms response header is nil"
	}
	return r.Message
}
