package omslib

import (
	"context"
	"time"

	"github.com/dgrijalva/jwt-go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/timeutil"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type omsHeader struct {
	secret string
}

func newHeader(secret string) *omsHeader {
	return &omsHeader{
		secret: secret,
	}
}

func (s *omsHeader) Header(ctx context.Context) (map[string]string, error) {
	token, err := s.generateToken(ctx)
	if err != nil {
		return nil, err
	}
	return map[string]string{"jwt-token": token}, nil
}

func (s *omsHeader) generateToken(ctx context.Context) (string, error) {
	token := jwt.New(jwt.GetSigningMethod("HS256"))
	token.Header["optr"] = "sls"
	claims := make(jwt.MapClaims)
	// this token valid time is 5min
	claims["exp"] = timeutil.Now(ctx).Add(time.Minute * 5).Unix()
	token.Claims = claims
	tokenString, err := token.SignedString(typ.StringToBytes(s.secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}
