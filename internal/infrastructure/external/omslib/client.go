package omslib

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/httplib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	BatchGetWhsPriority = httplib.HttpService{
		Endpoint: "/whs_api/batch_priority",
		Scene:    constant.MultiAlive,
		System:   constant.SystemOMS,
	}
)

type OMSClient interface {
	GetAllWarehousePriority(ctx context.Context, req WhsPriorityBatchRequest) ([]WhsPriorityEntity, error)
	GetAllWarehousePriorityUsingCache(ctx context.Context, req WhsPriorityBatchRequest) ([]WhsPriorityEntity, error)
}

func NewOMSClientImpl(confAccessor config.ConfAccessor, clients redishelper.GlobalRedisClients) (*OmsClientImpl, error) {
	defaultClient, err := clients.GetRedisClusterByClusterName(redishelper.Default)
	if err != nil {
		return nil, err
	}

	warehousePriorityCache, err := mixed_cache.NewLruLayerCache[WhsPriorityBatchRequest, []WhsPriorityEntity](
		cache.WarehousePriorityCacheName, defaultClient, nil, nil)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	client, err := httplib.NewBasicClient(confAccessor, constant.ServiceOmsApi)
	if err != nil {
		return nil, err
	}
	omsClient := &OmsClientImpl{
		confAccessor:           confAccessor,
		client:                 client,
		warehousePriorityCache: warehousePriorityCache,
	}
	return omsClient, nil
}

type OmsClientImpl struct {
	confAccessor           config.ConfAccessor
	client                 httplib.Client
	warehousePriorityCache cache.MultiCache[WhsPriorityBatchRequest, []WhsPriorityEntity]
}

func (o *OmsClientImpl) GetAllWarehousePriority(ctx context.Context, req WhsPriorityBatchRequest) ([]WhsPriorityEntity, error) {
	resp := &WhsPriorityBatchResponse{}
	httpService := BatchGetWhsPriority.Copy()
	if params := req.Params(); len(params) > 0 {
		httpService.Endpoint = httpService.Endpoint + "?" + strings.Join(params, "&")
	}
	err := o.client.Get(ctx, constant.ServiceOmsApi, BatchGetWhsPriority, &req, resp, o.header(ctx))
	if err != nil {
		return nil, fsserr.With(fsserr.OmsError, err)
	}
	return resp.Data, nil
}

func (o *OmsClientImpl) GetAllWarehousePriorityUsingCache(ctx context.Context, req WhsPriorityBatchRequest) ([]WhsPriorityEntity, error) {
	ret, err := cache.LoadSingle(
		ctx,
		o.warehousePriorityCache,
		req,
		o.GetAllWarehousePriority,
		cache.WithKeyConvertor[WhsPriorityBatchRequest, []WhsPriorityEntity](batchGetAllWarehousePriorityCacheKey),
	)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (o *OmsClientImpl) header(ctx context.Context) *omsHeader {
	return newHeader(o.confAccessor.GetOmsApiSecret(ctx))
}

func batchGetAllWarehousePriorityCacheKey(ctx context.Context, req WhsPriorityBatchRequest) string {
	return common.GenKeyWithRegion(ctx, ":", "whs.priority.batch",
		strings.ToLower(strings.TrimSpace(req.State)),
		strings.ToLower(strings.TrimSpace(req.City)),
		strings.ToLower(strings.TrimSpace(req.District)),
		strconv.FormatUint(req.ShopID, 10),
	)
}
