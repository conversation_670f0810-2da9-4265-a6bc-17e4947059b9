package sbslib

import (
	"context"
	"net/url"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/timeutil"
)

const SBSPortalApiPrefix = "sbs-auth-v1/"

type sbsHeader struct {
	apiKey    string
	apiSecret string
	host      string
	urlVals   url.Values
}

func newSbsHeader(apiKey, apiSecret, host string, urlVals url.Values) *sbsHeader {
	return &sbsHeader{
		apiKey:    apiKey,
		apiSecret: apiSecret,
		host:      host,
		urlVals:   urlVals,
	}
}

func (s *sbsHeader) Header(ctx context.Context) (map[string]string, error) {
	return map[string]string{
		"Authorization": generateSBSToken(ctx, "GET",
			s.host, GetAllChannelByRegion.Endpoint, s.urlVals, s.apiKey, s.apiSecret),
		"region": envvar.GetCIDLower(ctx),
	}, nil
}

func generateSBSToken(ctx context.Context, requestMethod, host, endpoint string, req url.Values, apiKey, apiSecret string) string {
	timestamp := timeutil.Now(ctx).Unix()
	nonce := common.GenerateRandomStringNonce(5)

	var sb strings.Builder

	// {AuthPrefix}=sbs-auth-v1/{accessKey}/{timestamp}/{nonce}
	prefixArray := [...]string{SBSPortalApiPrefix, apiKey, "/", strconv.FormatInt(timestamp, 10), "/", nonce}
	for _, s := range prefixArray {
		sb.WriteString(s)
	}
	authPrefix := sb.String()
	sb.Reset()

	// {Data}={REQUEST_METHOD} + "\n" + {HOST} + "\n" + {URI} + "\n" + {SORTED_QUERY_STRING}
	if !strings.HasPrefix(endpoint, "/") {
		endpoint = "/" + endpoint
	}

	params := req.Encode()

	//url.Encode does a partial escape which cause confusion later, so revert it and do proper escaping later
	params, _ = url.QueryUnescape(params)
	params = common.SortAndFullEscapeUrlParams(params)

	dataArray := [...]string{requestMethod, "\n", host, "\n", endpoint, "\n", params}
	for _, s := range dataArray {
		sb.WriteString(s)
	}
	data := sb.String()
	sb.Reset()

	// {Signature}=HMAC-SHA256-BASE64({secretKey}, {AuthPrefix} + "\n" + {Data})
	sb.WriteString(authPrefix)
	sb.WriteString("\n")
	sb.WriteString(data)
	signature := common.ComputeHmacSha256Base64URL(sb.String(), apiSecret)
	sb.Reset()

	// Authorization: {AuthPrefix}/{Signature}
	sb.WriteString(authPrefix)
	sb.WriteString("/")
	sb.WriteString(signature)

	return sb.String()
}
