package sbslib

import (
	jsoniter "github.com/json-iterator/go"
)

type GetAllChannelByRegionRequest struct {
	Region string `json:"region"`
}

func (p *GetAllChannelByRegionRequest) Queries() map[string]string {
	return map[string]string{"region": p.Region}
}

func (p *GetAllChannelByRegionRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

type (
	SBSGetAllRegionResponse struct {
		Retcode int                   `json:"retcode"`
		Message string                `json:"message"`
		Data    []SBSGetAllRegionData `json:"data"`
		Info    string                `json:"info"`
	}

	SBSGetAllRegionData struct {
		Region  string             `json:"region"`
		WhsList []SBSWarehouseList `json:"whs_list"`
	}

	SBSWarehouseList struct {
		WhsId       string           `json:"whs_id"`
		ChannelList []SBSChannelList `json:"channel_list"`
	}

	SBSChannelList struct {
		WhsId                 string   `json:"whs_id"`
		Region                string   `json:"region"`
		IsMask                bool     `json:"is_mask"`
		ChannelName           string   `json:"channel_name"`
		ChannelId             int      `json:"channel_id"`
		ChannelStatus         int      `json:"channel_status"`
		ChannelCodStatus      int      `json:"channel_cod_status"`
		FulfillmentChannelIds []uint32 `json:"fulfillment_channel_ids"`
	}
)

func (p *SBSGetAllRegionResponse) IsSuccess() bool {
	return p.Retcode == 0 && p.Data != nil
}

func (p *SBSGetAllRegionResponse) FailMsg() string {
	return "sbs get all region fail"
}
