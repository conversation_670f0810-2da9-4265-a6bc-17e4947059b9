package dbutils

import (
	"context"
	"errors"

	"git.garena.com/shopee/bg-logistics/go/scormv2"
)

var (
	errDBNotFound = errors.New("db not found")
)

var (
	dbMap = make(map[DBTag]scormv2.SQLCommon)
)

func Register(d map[DBTag]scormv2.SQLCommon) {
	dbMap = d
}

func getDB(ctx context.Context, dbTag DBTag) (scormv2.SQLCommon, error) {
	if len(dbMap) == 0 {
		return nil, errDBNotFound
	}
	dbConn, ok := dbMap[dbTag]
	if !ok {
		return nil, errDBNotFound
	}
	sqlCommon := dbConn.WithContext(ctx)
	return sqlCommon, nil
}

func ReadDB(ctx context.Context, table DataTable) (scormv2.SQLCommon, error) {
	db, err := getDB(ctx, table.DbTag(ctx))
	if err != nil {
		return nil, err
	}
	return db.Replica(), nil
}
func WriteDB(ctx context.Context, table DataTable) (scormv2.SQLCommon, error) {
	db, err := getDB(ctx, table.DbTag(ctx))
	if err != nil {
		return nil, err
	}
	return db.Master(), nil
}
