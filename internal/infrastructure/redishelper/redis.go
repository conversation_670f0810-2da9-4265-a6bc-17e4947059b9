package redishelper

import (
	"context"

	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/go/go-redis"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
)

func IsNil(err error) bool {
	if err == redis.Nil {
		return true
	}
	return errors.Is(err, redis.Nil)
}

type RedisOption struct {
	// ReadOnly  禁写, 主要用于 livetest 需要 live redis 计数的需求
	ReadOnly bool
	// UseSameKeyWithShadow shadow 流量与正式流量读相同 key
	UseSameKeyWithShadow bool
}

var (
	redisClientMap = make(map[CacheCloudClusterName]RedisOption)
)

// init 初始化 redis 配置，先通过代码 hard code，后续可以改为 apollo 配置
func init() {
	ctx := context.Background()
	initDefaultRedisClient(ctx)
	initStaticRedisClient(ctx)
}

func initDefaultRedisClient(ctx context.Context) {
	redisClientMap[Default] = RedisOption{}
}

func initStaticRedisClient(ctx context.Context) {
	opt := RedisOption{}
	if envvar.IsLivetest(ctx) {
		opt.ReadOnly = true
	}
	redisClientMap[Static] = opt
}
