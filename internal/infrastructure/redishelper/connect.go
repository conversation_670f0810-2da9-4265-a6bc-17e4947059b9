package redishelper

import (
	"context"

	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

type GlobalRedisClients map[CacheCloudClusterName]*redis.Client

func (c GlobalRedisClients) GetRedisClusterByClusterName(clusterName CacheCloudClusterName) (*redis.Client, error) {
	client, ok := globalRedisClients[clusterName]
	if !ok || client == nil {
		return nil, ErrRedisNotFound
	}
	return client, nil
}

var (
	globalRedisClients = make(GlobalRedisClients)
)

var (
	ErrRedisNotFound = errors.New("Not found redis client by redis cluster name")
)

func InitRedisClientMap() (GlobalRedisClients, error) {
	for clusterName, option := range redisClientMap {
		client, err := buildClient(clusterName, option)
		if err != nil {
			Logger.CtxLogErrorf(context.Background(), "init cache redis failed|clusterName=%v|err=%v", clusterName, err)
			return globalRedisClients, err
		}
		globalRedisClients[clusterName] = client
	}
	return globalRedisClients, nil
}

func buildClient(clusterName CacheCloudClusterName, option RedisOption) (*redis.Client, error) {
	client, err := initClient(string(clusterName), option)
	if err != nil {
		_ = monitor.ReportEvent(constant.CatModuleRedis, string(clusterName), monitor.StatusError, err.Error())
		return nil, err
	}
	_ = monitor.ReportEvent(constant.CatModuleRedis, string(clusterName), monitor.StatusSuccess, client.String())
	return client, nil
}

func initClient(clusterName string, initOption RedisOption) (*redis.Client, error) {
	option := redis.NewStandardOption().WithClusterName(clusterName)
	if initOption.UseSameKeyWithShadow {
		option.WithShadowKeyFunc(nil)
	}
	option.PoolFIFO = true
	client := redis.NewStandardClient(option)
	if option.Addr == redisDefaultAddr {
		return nil, errors.Errorf("init redis fail, get config fail, key=%s", clusterName)
	}

	if initOption.ReadOnly {
		client.AddHook(writeForbiddenHook)
	}
	_, err := client.Ping(context.Background()).Result() // 此处会上报了一个unsafe context
	if err != nil {
		return nil, errors.Errorf("init redis fail, clusterName = %s, client=%+v, err=%s", clusterName, client, err)
	}
	return client, nil
}

func GetRedisClusterByClusterName(clusterName CacheCloudClusterName) (*redis.Client, error) {
	return globalRedisClients.GetRedisClusterByClusterName(clusterName)
}

func GetRedisPipLineByCluster(clusterName CacheCloudClusterName) (redis.Pipeliner, error) {
	client, err := GetRedisClusterByClusterName(clusterName)
	if err != nil {
		return nil, err
	}
	return client.Pipeline(), nil
}
