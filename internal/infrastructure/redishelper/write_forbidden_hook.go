package redishelper

import (
	"context"
	"errors"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
)

var (
	// writeCmds redis 写命令
	// redis command 结果: acl cat write
	writeCmds = map[string]struct{}{
		"blmpop":                {},
		"incrby":                {},
		"hincrby":               {},
		"xreadgroup":            {},
		"getdel":                {},
		"zrangestore":           {},
		"mset":                  {},
		"incrbyfloat":           {},
		"migrate":               {},
		"bitfield":              {},
		"pexpire":               {},
		"setnx":                 {},
		"hincrbyfloat":          {},
		"lmpop":                 {},
		"xadd":                  {},
		"spop":                  {},
		"decrby":                {},
		"ltrim":                 {},
		"xtrim":                 {},
		"xautoclaim":            {},
		"blmove":                {},
		"persist":               {},
		"xack":                  {},
		"set":                   {},
		"rename":                {},
		"function|load":         {},
		"function|flush":        {},
		"function|restore":      {},
		"function|delete":       {},
		"copy":                  {},
		"setex":                 {},
		"unlink":                {},
		"linsert":               {},
		"zadd":                  {},
		"swapdb":                {},
		"restore":               {},
		"getex":                 {},
		"blpop":                 {},
		"move":                  {},
		"brpoplpush":            {},
		"xdel":                  {},
		"incr":                  {},
		"hsetnx":                {},
		"hdel":                  {},
		"decr":                  {},
		"lset":                  {},
		"bzpopmax":              {},
		"smove":                 {},
		"rpop":                  {},
		"flushall":              {},
		"lmove":                 {},
		"bzpopmin":              {},
		"sunionstore":           {},
		"lpushx":                {},
		"zunionstore":           {},
		"rpoplpush":             {},
		"zpopmin":               {},
		"geoadd":                {},
		"expireat":              {},
		"brpop":                 {},
		"zremrangebyscore":      {},
		"sdiffstore":            {},
		"hmset":                 {},
		"del":                   {},
		"append":                {},
		"sort":                  {},
		"restore-asking":        {},
		"rpush":                 {},
		"pfmerge":               {},
		"georadius":             {},
		"pexpireat":             {},
		"georadiusbymember":     {},
		"srem":                  {},
		"lpop":                  {},
		"psetex":                {},
		"setrange":              {},
		"zmpop":                 {},
		"lrem":                  {},
		"bitop":                 {},
		"lpush":                 {},
		"sadd":                  {},
		"pfadd":                 {},
		"rpushx":                {},
		"zpopmax":               {},
		"expire":                {},
		"zdiffstore":            {},
		"renamenx":              {},
		"zremrangebyrank":       {},
		"geosearchstore":        {},
		"zremrangebylex":        {},
		"bzmpop":                {},
		"zinterstore":           {},
		"xsetid":                {},
		"zrem":                  {},
		"zincrby":               {},
		"setbit":                {},
		"hset":                  {},
		"sinterstore":           {},
		"flushdb":               {},
		"pfdebug":               {},
		"msetnx":                {},
		"getset":                {},
		"xgroup|destroy":        {},
		"xgroup|delconsumer":    {},
		"xgroup|createconsumer": {},
		"xgroup|setid":          {},
		"xgroup|create":         {},
	}
)

var (
	writeForbiddenHook       = &WriteForbiddenHook{}
	redisWriteForbiddenError = errors.New("livetest static redis write forbidden")
)

type WriteForbiddenHook struct{}

func (w *WriteForbiddenHook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	if _, ok := writeCmds[cmd.FullName()]; ok {
		return ctx, redisWriteForbiddenError
	}
	return ctx, nil
}

func (w *WriteForbiddenHook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	return nil
}

func (w *WriteForbiddenHook) BeforeProcessPipeline(ctx context.Context, cmds []redis.Cmder) (context.Context, error) {
	for _, cmd := range cmds {
		if _, ok := writeCmds[cmd.FullName()]; ok {
			return ctx, redisWriteForbiddenError
		}
	}
	return ctx, nil
}

func (w *WriteForbiddenHook) AfterProcessPipeline(ctx context.Context, cmds []redis.Cmder) error {
	return nil
}
