package bootstrap

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/scsps"
	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
)

var initOnce sync.Once

func InitSpex(ctx context.Context, confAccessor config.ConfAccessor) error {
	spexConfig := confAccessor.GetSpexConfig(ctx)
	cid := envvar.GetCIDLower(ctx)
	env := envvar.GetEnvLower(ctx)

	instanceId, err := scsps.GenerateInstanceID(spexConfig.ServiceName, cid, env, "", "", "")
	if err != nil {
		return errors.Wrap(err, "generate instance id")
	}
	initOnce.Do(func() {
		// 可使用这个方式本地启动spex
		//err = sps.Init(sps.WithInstanceID(instanceId), sps.WithConfigKey(cfg.SpexConfig.ConfigKey), sps.WithSpexAddress("tcp", "spex.lls-lcs.tech:8088"))
		err = scsps.Init(scsps.WithInstanceID(instanceId), scsps.WithConfigKey(spexConfig.ConfigKey))
		if err != nil {
			return
		}
		scsps.RegisterGlobalClientInterceptors(scsps.NewInterceptor(spexConfig.ServiceName))
		err = startSpex()
	})
	return err
}

func startSpex() error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	if err := scsps.SubscribeConfig(ctx); err != nil {
		return errors.Wrap(err, "subscribe config")
	}
	if err := scsps.Register(ctx); err != nil {
		return errors.Wrap(err, "register")
	}
	return nil
}
