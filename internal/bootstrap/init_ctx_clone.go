package bootstrap

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
)

func InitCtxCloneFunc(ctx context.Context, confAccessor config.ConfAccessor) error {
	// Initialize the context clone function for ctxhelper
	ctxhelper.RegisterContextCloneFunc("ctx_data", ctxutils.CtxDataCloneFunc)
	return nil
}
