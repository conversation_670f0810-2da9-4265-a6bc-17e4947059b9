package bootstrap

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
)

type dependencyInitFactory func(ctx context.Context, confAccessor config.ConfAccessor) error

func InitRestServerDependency(ctx context.Context, confAccessor config.ConfAccessor) error {
	serviceInitFactories := []dependencyInitFactory{
		InitSpex,
		InitDB,
		lru.InitLruDependency,
		InitCtxCloneFunc,
	}
	return initServerDependency(ctx, confAccessor, serviceInitFactories)
}

func initServerDependency(ctx context.Context, confAccessor config.ConfAccessor, factories []dependencyInitFactory) error {
	for _, factory := range factories {
		if err := factory(ctx, confAccessor); err != nil {
			return err
		}
	}
	return nil
}
