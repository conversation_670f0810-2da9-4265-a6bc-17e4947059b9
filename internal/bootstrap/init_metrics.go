package bootstrap

import (
	"slices"

	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
)

func InitMetrics() error {
	var err error
	opt := metrics.CounterOpts{
		Name:   constant.IGSOrderMonitor,
		Help:   "Counter for IGS order metrics",
		Labels: []string{"order_type"},
	}
	err = CreateCounter(opt)
	if err != nil {
		return err
	}
	return nil
}

func CreateCounter(opt metrics.CounterOpts) error {
	opt.Labels = updateLabels(opt.Labels)
	return metrics.CreateCounter(opt)
}

func updateLabels(labels []string) []string {
	if slices.Contains(labels, "type_mark") {
		return labels
	}
	return append(labels, "type_mark")
}
