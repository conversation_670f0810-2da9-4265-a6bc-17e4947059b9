package bootstrap

import (
	"context"
	"os"

	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
)

func InitEnvironmentVariables(ctx context.Context) error {
	// livetest envvar change
	_ = os.Setenv("SSC_ENV", envvar.GetEnvLower(ctx))
	if envvar.IsLivetest(ctx) {
		err := os.Setenv("SSC_ENV", "livetest")
		if err != nil {
			return errors.Errorf("write chassis envvar error: %v", err)
		}

		err = os.Setenv("APOLLO_CLUSTER", envvar.GetCIDLower(ctx)+"_livetest")
		if err != nil {
			return errors.Errorf("write chassis envvar error: %v", err)
		}
	} else {
		err := os.Setenv("APOLLO_CLUSTER", envvar.GetCIDLower(ctx))
		if err != nil {
			return errors.Errorf("write chassis envvar error: %v", err)
		}
	}
	return nil
}
