package bootstrap

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/localcache/lcregistry"
)

func InitLocalCache(ctx context.Context, confAccessor config.ConfAccessor) error {
	manager := localcache.NewManager(confAccessor)
	lcregistry.Register()
	err := manager.Init(ctx)
	if err != nil {
		return err
	}
	return nil
}
