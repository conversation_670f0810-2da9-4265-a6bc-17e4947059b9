package bootstrap

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"gorm.io/driver/mysql"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/dbutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

func InitDB(ctx context.Context, confAccessor config.ConfAccessor) error {
	dbs := make(map[dbutils.DBTag]scormv2.SQLCommon)
	dbConf := confAccessor.GetDBConfig(ctx)
	dbConnectionsConf := confAccessor.GetDBConnectionConfig(ctx)
	for dbTag, dbConnection := range dbConnectionsConf {
		masterDsn, replicasDsn := dbConnection.MasterDsn, dbConnection.ReplicasDsn
		db, err := initDB(masterDsn, replicasDsn, dbConf.MaxIdleConn, dbConf.MaxOpenConn,
			dbConf.ConnMaxAge, dbConf.DisableShadowDBForReadOnly, dbConf.SlowQueryTime)
		if err != nil {
			return err
		}
		dbs[dbutils.DBTag(dbTag)] = db
	}
	dbutils.Register(dbs)
	return nil
}

func initDB(masterDsn string, replicasDsn string, maxIdleConn, maxOpenConn, connMaxAge int,
	disableShadowDBForReadOnly bool, slowQueryTime int) (*scormv2.OrmDB, error) {
	dialect := scormv2.NewMultiDialector().AddDefaultDialector(mysql.Open(masterDsn), mysql.Open(replicasDsn))

	conf := &scormv2.Config{
		AutoReport:                 true,
		DisableShadowDBForReadOnly: disableShadowDBForReadOnly,
		SlowQuery:                  time.Duration(slowQueryTime) * time.Millisecond,
	}

	db, cErr := scormv2.Open(dialect, conf)
	if cErr != nil {
		logger.CtxLogErrorf(context.TODO(), "initialize database connection fail, masterDsn:%s, replicasDsn:%s, error:%+v",
			masterDsn, replicasDsn, cErr)
		return nil, cErr
	}

	sqlDB := db.DB()
	db.SetMaxIdleConns(maxIdleConn)
	db.SetMaxOpenConns(maxOpenConn)
	db.SetConnMaxLifetime(time.Duration(connMaxAge) * time.Second)
	if err := sqlDB.Ping(); err != nil {
		logger.CtxLogErrorf(context.TODO(), "ping database fail, error:%+v", err)
		return nil, err
	}

	return db, nil
}
