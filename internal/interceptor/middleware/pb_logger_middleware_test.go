package middleware

import (
	"context"
	"testing"

	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/reflect/protoreflect"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

func TestLogPBDataWithError(t *testing.T) {
	ctx := context.Background()
	path := "/api/test"

	// 创建包含错误的 PB 数据
	pbData := &ctxutils.PBContextData{
		Request:   &mockProtoMessage{data: `{"test": "request"}`},
		Response:  &mockProtoMessage{data: `{"test": "response"}`},
		Error:     fsserr.New(fsserr.ParamErr, "test error"),
		Operation: "TestOperation",
	}

	// 测试错误情况下的日志记录
	logPBDataWithError(ctx, path, pbData)

	// 验证数据没有被修改
	assert.NotNil(t, pbData.Request)
	assert.NotNil(t, pbData.Response)
	assert.NotNil(t, pbData.Error)
	assert.Equal(t, "TestOperation", pbData.Operation)
}

func TestLogPBDataWithSuccess(t *testing.T) {
	ctx := context.Background()
	path := "/api/test"

	// 创建成功的 PB 数据
	pbData := &ctxutils.PBContextData{
		Request:   &mockProtoMessage{data: `{"test": "request"}`},
		Response:  &mockProtoMessage{data: `{"test": "response"}`},
		Error:     nil,
		Operation: "TestOperation",
	}

	// 测试成功情况下的日志记录
	logPBDataWithSuccess(ctx, path, pbData)

	// 验证数据没有被修改
	assert.NotNil(t, pbData.Request)
	assert.NotNil(t, pbData.Response)
	assert.Nil(t, pbData.Error)
	assert.Equal(t, "TestOperation", pbData.Operation)
}

func TestLogPBDataWithSuccess_NoResponse(t *testing.T) {
	ctx := context.Background()
	path := "/api/test"

	// 创建成功但没有响应的 PB 数据
	pbData := &ctxutils.PBContextData{
		Request:   &mockProtoMessage{data: `{"test": "request"}`},
		Response:  nil,
		Error:     nil,
		Operation: "TestOperation",
	}

	// 测试成功但没有响应的情况
	logPBDataWithSuccess(ctx, path, pbData)

	// 验证数据没有被修改
	assert.NotNil(t, pbData.Request)
	assert.Nil(t, pbData.Response)
	assert.Nil(t, pbData.Error)
}

func TestLogPBDataWithSuccess_NoRequest(t *testing.T) {
	ctx := context.Background()
	path := "/api/test"

	// 创建成功但没有请求的 PB 数据
	pbData := &ctxutils.PBContextData{
		Request:   nil,
		Response:  &mockProtoMessage{data: `{"test": "response"}`},
		Error:     nil,
		Operation: "TestOperation",
	}

	// 测试成功但没有请求的情况
	logPBDataWithSuccess(ctx, path, pbData)

	// 验证数据没有被修改
	assert.Nil(t, pbData.Request)
	assert.NotNil(t, pbData.Response)
	assert.Nil(t, pbData.Error)
}

func TestLogPBDataWithError_NoRequest(t *testing.T) {
	ctx := context.Background()
	path := "/api/test"

	// 创建错误但没有请求的 PB 数据
	pbData := &ctxutils.PBContextData{
		Request:   nil,
		Response:  &mockProtoMessage{data: `{"test": "response"}`},
		Error:     fsserr.New(fsserr.ServerErr, "server error"),
		Operation: "TestOperation",
	}

	// 测试错误但没有请求的情况
	logPBDataWithError(ctx, path, pbData)

	// 验证数据没有被修改
	assert.Nil(t, pbData.Request)
	assert.NotNil(t, pbData.Response)
	assert.NotNil(t, pbData.Error)
}

func TestLogPBData_ErrorCase(t *testing.T) {
	ctx := context.Background()
	path := "/api/test"

	// 创建包含错误的 PB 数据
	pbData := &ctxutils.PBContextData{
		Request:   &mockProtoMessage{data: `{"test": "request"}`},
		Response:  &mockProtoMessage{data: `{"test": "response"}`},
		Error:     fsserr.New(fsserr.ServerErr, "server error"),
		Operation: "TestOperation",
	}

	// 测试完整的错误情况日志记录
	logPBData(ctx, path, pbData)

	// 验证数据没有被修改
	assert.NotNil(t, pbData.Request)
	assert.NotNil(t, pbData.Response)
	assert.NotNil(t, pbData.Error)
}

func TestLogPBData_SuccessCase(t *testing.T) {
	ctx := context.Background()
	path := "/api/test"

	// 创建成功的 PB 数据
	pbData := &ctxutils.PBContextData{
		Request:   &mockProtoMessage{data: `{"test": "response"}`},
		Response:  &mockProtoMessage{data: `{"test": "response"}`},
		Error:     nil,
		Operation: "TestOperation",
	}

	// 测试完整的成功情况日志记录
	logPBData(ctx, path, pbData)

	// 验证数据没有被修改
	assert.NotNil(t, pbData.Request)
	assert.NotNil(t, pbData.Response)
	assert.Nil(t, pbData.Error)
}

// mockProtoMessage 用于测试的 mock protobuf 消息
type mockProtoMessage struct {
	data string
}

func (m *mockProtoMessage) Reset() {
	m.data = ""
}

func (m *mockProtoMessage) String() string {
	return m.data
}

func (m *mockProtoMessage) ProtoMessage() {}

func (m *mockProtoMessage) ProtoReflect() protoreflect.Message {
	return new(fss_proto.GroupItemsRequest).ProtoReflect()
}
