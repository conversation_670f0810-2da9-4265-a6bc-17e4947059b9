package middleware

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

const PBLoggerHandlerName = "pb-logger"

type PBLoggerHandler struct{}

func RegisterPBLoggerHandler() {
	_ = handler.RegisterHandler(PBLoggerHandlerName, NewPBLoggerHandler)
}

func NewPBLoggerHandler() handler.Handler {
	return &PBLoggerHandler{}
}

func (h *PBLoggerHandler) Name() string {
	return PBLoggerHandlerName
}

func (h *PBLoggerHandler) Handle(i *invocation.Invocation) {
	var isProtobufReq bool
	var path string
	if req, ok := i.Args.(*restful.Request); ok {
		contentType := req.HeaderParameter("Content-Type")
		isProtobufReq = isProtobufRequest(contentType)
		if isProtobufReq && Logger.IsBizInfoLogEnabled(i.Ctx) {
			i.Ctx = ctxutils.InitPBContextData(i.Ctx)
			path = req.Request.URL.Path
		}
	}

	// 继续处理请求
	i.Next()
	if !isProtobufReq {
		return
	}

	if ctxutils.IsProtobufRequest(i.Ctx) {
		// 从 context 中获取 protobuf 数据
		pbData := ctxutils.GetPBContextData(i.Ctx)
		if pbData == nil {
			return
		}

		// 记录请求和响应
		logPBData(i.Ctx, path, pbData)

		// 清理 context 数据
		i.Ctx = ctxutils.ClearPBContextData(i.Ctx)
	}
}

// isProtobufRequest 检查是否为 protobuf 请求
func isProtobufRequest(contentType string) bool {
	return strings.Contains(contentType, "application/x-protobuf")
}

// logPBData 记录 protobuf 数据
func logPBData(ctx context.Context, path string, pbData *ctxutils.PBContextData) {
	// 根据是否有错误分别处理
	if pbData.Error != nil {
		logPBDataWithError(ctx, path, pbData)
	} else {
		logPBDataWithSuccess(ctx, path, pbData)
	}
}

// logPBDataWithError 记录错误情况下的 protobuf 数据
func logPBDataWithError(ctx context.Context, path string, pbData *ctxutils.PBContextData) {
	// 构建包含请求和错误的日志
	var (
		requestJSONData string
		retcode         int
		message         string
		detail          string
	)

	// 添加请求信息
	requestJSONData, _ = protoToJSON(pbData.Request)

	retcode = pbData.Error.Code()
	message = pbData.Error.Message()
	detail = pbData.Error.Detail()
	Logger.CtxLogInfof(ctx, "pd request failed|%s|request: %s|retcode: %d|message: %s| detail: %s",
		path, requestJSONData, retcode, message, detail)
}

// logPBDataWithSuccess 记录成功情况下的 protobuf 数据
func logPBDataWithSuccess(ctx context.Context, path string, pbData *ctxutils.PBContextData) {
	// 构建包含请求和响应的日志
	var (
		requestJSONData  string
		responseJSONData string
	)

	// 添加请求信息
	requestJSONData, _ = protoToJSON(pbData.Request)

	// 添加响应信息
	responseJSONData, _ = protoToJSON(pbData.Response)

	Logger.CtxLogInfof(ctx, "pd request success|%s|request: %s|response: %s",
		path, requestJSONData, responseJSONData)
}

var (
	marshaler = protojson.MarshalOptions{
		AllowPartial:   true,
		UseProtoNames:  true,
		UseEnumNumbers: true,
	}
)

// protoToJSON 将 protobuf 消息转换为 JSON
func protoToJSON(pb proto.Message) (string, error) {
	buf, err := marshaler.Marshal(pb)
	if err != nil {
		return "", err
	}
	return typ.BytesToString(buf), nil
}
