package middleware

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	uuid "github.com/satori/go.uuid"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

const TaskHandlerName = "task-context"

type TaskContextHandler struct {
	ConfAccessor config.ConfAccessor
}

func RegisterTaskContextHandler(confAccessor config.ConfAccessor) {
	TaskContextHandlerInit := func() handler.Handler {
		return NewTaskContextHandler(confAccessor)
	}
	_ = handler.RegisterHandler(TaskHandlerName, TaskContextHandlerInit)
}

func NewTaskContextHandler(confAccessor config.ConfAccessor) handler.Handler {
	return &TaskContextHandler{
		ConfAccessor: confAccessor,
	}
}

func (th *TaskContextHandler) Name() string {
	return TaskHandlerName
}

func (th *TaskContextHandler) Handle(i *invocation.Invocation) {
	ctx := i.Ctx
	var (
		sourceRequestId, serverRequestId = genTaskRequestID(ctx)
		requestTypeMark                  = common.GetReqTypeMakerFromXRequestId(ctx, sourceRequestId)
		logHit                           = false
		req, path                        = getReqAndPath(ctx, i.Args)
	)
	ctx = common.InitRequestContext(ctx, th.ConfAccessor, serverRequestId, logHit, requestTypeMark, path)

	var logTraceNum uint32
	ctx, logTraceNum = ctxutils.SetLogTraceNum(ctx)
	if logTraceNum > 0 {
		// set log trace info
		traceEntity := ctxutils.TraceEntity{
			LogTrace:  logTraceNum,
			Name:      path,
			FromCache: false,
			Error:     "",
		}
		ctx = ctxutils.SetLogTraceInfo(ctx, traceEntity)
	}

	start := time.Now()

	i.Ctx = ctx
	i.Next()
	end := time.Now()
	latency := end.Sub(start)

	ctx = i.Ctx
	if logTraceNum > 0 {
		traceInfoList := ctxutils.GetLogTraceInfo(ctx)
		if traceInfoList != nil {
			traceInfoListV2 := make([]ctxutils.TraceEntity, 0, len(*traceInfoList))
			for index, traceInfo := range *traceInfoList {
				// time consume to replace with url
				if index == 0 {
					traceInfo.Cost = latency.String()
				}
				traceInfoListV2 = append(traceInfoListV2, traceInfo)
			}

			// todo solve the problem of large number of logs and large storage space
			Logger.CtxLogInfof(ctx, "log_trace [request] length:%d, log_trace_info:%s", len(*traceInfoList), Logger.JsonString(traceInfoListV2))
		}
	}

	if chassis_config.IsInfoEnabled() && path != "" {
		Logger.CtxLogInfof(ctx, "%s|%s|%s|%s|request: %s|response: %s",
			start.Format("2006/01/02 - 15:04:05"),
			sourceRequestId,
			i.MicroServiceName,
			path,
			req,
			getReply(i.Reply),
		)

	}
}

func getReqAndPath(ctx context.Context, arg interface{}) (string, string) {
	if arg == nil {
		return "", ""
	}
	if args, ok := arg.(saturn.JobArgs); ok {
		return args.ShardingParam, args.JobName
	}
	if args, ok := arg.(*saturn.SaturnMessage); ok && args != nil {
		path, _ := ctx.Value("saturn-job-name").(string)
		return string(args.MsgText), path
	}
	return "", ""
}

func getReply(arg interface{}) string {
	if args, ok := arg.(*saturn.SaturnReply); ok && args != nil {
		return Logger.JsonString(args)
	}
	return ""
}
func genTaskRequestID(ctx context.Context) (string, string) {
	source := uuid.NewV4().String()
	return source, "|" + source + "|"
}
