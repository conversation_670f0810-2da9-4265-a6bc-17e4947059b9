package logger

import (
	"context"

	"github.com/bytedance/sonic"
)

// JsonString 返回json格式，用于常规实例转为json字符串
func JsonString(a interface{}) string {
	data, err := sonic.MarshalString(a)
	if err != nil {
		return ""
	}
	return data
}

// JsonStringForDebugLog 用于日志打印json格式，会进行logLevel判断，只适用于debug日志输出复杂参数，切勿随处使用
//
//	ps：与logger.CtxLogDebugf()方法联合使用
//	@param obj 待marshal序列化的实例
//	@return 实例序列化后，json字符串
//	 1.序列化失败，返回空字符串
//	 2.日志级别非debug时，返回特殊提示内容：logLevel is not debug, please after considering the code, analyze whether you need to use debug log output
func JsonStringForDebugLog(ctx context.Context, obj interface{}) string {
	// 日志级别为debug，才进行序列化
	if IsBizDebugLogEnabled(ctx) {
		data, err := sonic.MarshalString(obj)
		if err != nil {
			return ""
		}
		return data
	}
	return "logLevel is not debug"
}
