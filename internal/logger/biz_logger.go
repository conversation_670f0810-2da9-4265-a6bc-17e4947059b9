package logger

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/asmutil"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
)

func CtxLogErrorf(ctx context.Context, format string, v ...interface{}) {
	if isDisablePrintLog(ctx) {
		return
	}
	sp := asmutil.GetRetAddr()
	logger.CtxLogErrorf2(ctx, sp, format, v...)
}

func CtxLogFatalf(ctx context.Context, format string, v ...interface{}) {
	if isDisablePrintLog(ctx) {
		return
	}
	sp := asmutil.GetRetAddr()
	logger.CtxLogFatalf2(ctx, sp, format, v...)
}

func LogDebugf(format string, v ...interface{}) {
	sp := asmutil.GetRetAddr()
	logger.LogDebugf2(sp, format, v...)
}

func LogInfof(format string, v ...interface{}) {
	sp := asmutil.GetRetAddr()
	logger.LogInfof2(sp, format, v...)
}

func LogInfo(v ...interface{}) {
	sp := asmutil.GetRetAddr()
	logger.LogInfo2(sp, v...)
}

func LogErrorf(format string, v ...interface{}) {
	sp := asmutil.GetRetAddr()
	logger.LogErrorf2(sp, format, v...)
}

func LogFatalf(format string, v ...interface{}) {
	sp := asmutil.GetRetAddr()
	logger.LogFatalf2(sp, format, v...)
}

func CtxLogInfof(ctx context.Context, format string, v ...interface{}) {
	if isDisablePrintLog(ctx) {
		return
	}
	sp := asmutil.GetRetAddr()
	if IsLogHit(ctx) {
		logger.CtxLogErrorf2(ctx, sp, format, v...)
		return
	}
	logger.CtxLogInfof2(ctx, sp, format, v...)
}

func CtxLogDebugf(ctx context.Context, format string, v ...interface{}) {
	if chassis_config.IsFastReturnInDebugLog() {
		return
	}
	sp := asmutil.GetRetAddr()
	logger.CtxLogDebugf2(ctx, sp, format, v...)
}

func CtxLogErrorf2(ctx context.Context, caller uintptr, format string, v ...interface{}) {
	if isDisablePrintLog(ctx) {
		return
	}
	logger.CtxLogErrorf2(ctx, caller, format, v...)
}

func CtxLogInfof2(ctx context.Context, caller uintptr, format string, v ...interface{}) {
	if isDisablePrintLog(ctx) {
		return
	}
	if IsLogHit(ctx) {
		logger.CtxLogErrorf2(ctx, caller, format, v...)
		return
	}
	logger.CtxLogInfof2(ctx, caller, format, v...)
}

func CtxLogDebugf2(ctx context.Context, caller uintptr, format string, v ...interface{}) {
	if isDisablePrintLog(ctx) {
		return
	}
	if IsLogHit(ctx) {
		logger.CtxLogErrorf2(ctx, caller, format, v...)
		return
	}
	logger.CtxLogDebugf2(ctx, caller, format, v...)
}

func IsBizInfoLogEnabled(ctx context.Context) bool {
	if IsLogHit(ctx) {
		return true
	}
	return chassis_config.IsInfoEnabled()
}

func IsBizDebugLogEnabled(ctx context.Context) bool {
	if IsLogHit(ctx) {
		return true
	}
	return logger.IsDebugEnabled()
}
