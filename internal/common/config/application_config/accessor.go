package application_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type ApplicationAccessor interface {
	GetSpexConfig(ctx context.Context) SpexConfig

	Init(ctx context.Context) error
	utils.ConfManager
}

func NewApplicationAccessorImpl() *ApplicationAccessorImpl {
	confListener := utils.NewConfListener(applicationConfigPrefix, refreshConfigInterval, false)
	accessor := &ApplicationAccessorImpl{
		ConfListener: confListener,
	}
	return accessor
}

type ApplicationAccessorImpl struct {
	utils.ConfListener
}

func (a *ApplicationAccessorImpl) GetSpexConfig(ctx context.Context) SpexConfig {
	return spexConfig.ValueCtx(ctx).Get()
}

func (a *ApplicationAccessorImpl) Init(ctx context.Context) error {
	a.ConfListener.Register(spexConfig)
	return a.ConfListener.Init(ctx)
}
