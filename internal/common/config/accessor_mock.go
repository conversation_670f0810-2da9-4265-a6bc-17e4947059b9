// Code generated by MockGen. DO NOT EDIT.
// Source: accessor.go

// Package config is a generated GoMock package.
package config

import (
	context "context"
	reflect "reflect"

	application_config "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	business_config "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	datasource_config "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/datasource_config"
	mutable_application_config "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	server_config "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
	config "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"
	gomock "github.com/golang/mock/gomock"
)

// MockConfAccessor is a mock of ConfAccessor interface.
type MockConfAccessor struct {
	ctrl     *gomock.Controller
	recorder *MockConfAccessorMockRecorder
}

// MockConfAccessorMockRecorder is the mock recorder for MockConfAccessor.
type MockConfAccessorMockRecorder struct {
	mock *MockConfAccessor
}

// NewMockConfAccessor creates a new mock instance.
func NewMockConfAccessor(ctrl *gomock.Controller) *MockConfAccessor {
	mock := &MockConfAccessor{ctrl: ctrl}
	mock.recorder = &MockConfAccessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfAccessor) EXPECT() *MockConfAccessorMockRecorder {
	return m.recorder
}

// All mocks base method.
func (m *MockConfAccessor) All(ctx context.Context) map[string]interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "All", ctx)
	ret0, _ := ret[0].(map[string]interface{})
	return ret0
}

// All indicates an expected call of All.
func (mr *MockConfAccessorMockRecorder) All(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "All", reflect.TypeOf((*MockConfAccessor)(nil).All), ctx)
}

// GetABTestingConfig mocks base method.
func (m *MockConfAccessor) GetABTestingConfig(ctx context.Context) config.Config {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetABTestingConfig", ctx)
	ret0, _ := ret[0].(config.Config)
	return ret0
}

// GetABTestingConfig indicates an expected call of GetABTestingConfig.
func (mr *MockConfAccessorMockRecorder) GetABTestingConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetABTestingConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetABTestingConfig), ctx)
}

// GetAllocateWarehouseBySalesOrderGroups mocks base method.
func (m *MockConfAccessor) GetAllocateWarehouseBySalesOrderGroups(ctx context.Context) []business_config.AllocateWarehouseBySalesOrdersGroup {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllocateWarehouseBySalesOrderGroups", ctx)
	ret0, _ := ret[0].([]business_config.AllocateWarehouseBySalesOrdersGroup)
	return ret0
}

// GetAllocateWarehouseBySalesOrderGroups indicates an expected call of GetAllocateWarehouseBySalesOrderGroups.
func (mr *MockConfAccessorMockRecorder) GetAllocateWarehouseBySalesOrderGroups(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllocateWarehouseBySalesOrderGroups", reflect.TypeOf((*MockConfAccessor)(nil).GetAllocateWarehouseBySalesOrderGroups), ctx)
}

// GetBatchGetEntityTagAPISize mocks base method.
func (m *MockConfAccessor) GetBatchGetEntityTagAPISize(ctx context.Context) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchGetEntityTagAPISize", ctx)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetBatchGetEntityTagAPISize indicates an expected call of GetBatchGetEntityTagAPISize.
func (mr *MockConfAccessorMockRecorder) GetBatchGetEntityTagAPISize(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchGetEntityTagAPISize", reflect.TypeOf((*MockConfAccessor)(nil).GetBatchGetEntityTagAPISize), ctx)
}

// GetChannelsWithSelectableDeliveryTime mocks base method.
func (m *MockConfAccessor) GetChannelsWithSelectableDeliveryTime(ctx context.Context) []int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelsWithSelectableDeliveryTime", ctx)
	ret0, _ := ret[0].([]int)
	return ret0
}

// GetChannelsWithSelectableDeliveryTime indicates an expected call of GetChannelsWithSelectableDeliveryTime.
func (mr *MockConfAccessorMockRecorder) GetChannelsWithSelectableDeliveryTime(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelsWithSelectableDeliveryTime", reflect.TypeOf((*MockConfAccessor)(nil).GetChannelsWithSelectableDeliveryTime), ctx)
}

// GetCounterfeitItemChannelBlockToggle mocks base method.
func (m *MockConfAccessor) GetCounterfeitItemChannelBlockToggle(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCounterfeitItemChannelBlockToggle", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetCounterfeitItemChannelBlockToggle indicates an expected call of GetCounterfeitItemChannelBlockToggle.
func (mr *MockConfAccessorMockRecorder) GetCounterfeitItemChannelBlockToggle(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCounterfeitItemChannelBlockToggle", reflect.TypeOf((*MockConfAccessor)(nil).GetCounterfeitItemChannelBlockToggle), ctx)
}

// GetCounterfeitItemLabelId mocks base method.
func (m *MockConfAccessor) GetCounterfeitItemLabelId(ctx context.Context) uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCounterfeitItemLabelId", ctx)
	ret0, _ := ret[0].(uint64)
	return ret0
}

// GetCounterfeitItemLabelId indicates an expected call of GetCounterfeitItemLabelId.
func (mr *MockConfAccessorMockRecorder) GetCounterfeitItemLabelId(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCounterfeitItemLabelId", reflect.TypeOf((*MockConfAccessor)(nil).GetCounterfeitItemLabelId), ctx)
}

// GetDBConfig mocks base method.
func (m *MockConfAccessor) GetDBConfig(ctx context.Context) mutable_application_config.DBConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDBConfig", ctx)
	ret0, _ := ret[0].(mutable_application_config.DBConfig)
	return ret0
}

// GetDBConfig indicates an expected call of GetDBConfig.
func (mr *MockConfAccessorMockRecorder) GetDBConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDBConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetDBConfig), ctx)
}

// GetDBConnectionConfig mocks base method.
func (m *MockConfAccessor) GetDBConnectionConfig(ctx context.Context) datasource_config.DBConnectionConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDBConnectionConfig", ctx)
	ret0, _ := ret[0].(datasource_config.DBConnectionConfig)
	return ret0
}

// GetDBConnectionConfig indicates an expected call of GetDBConnectionConfig.
func (mr *MockConfAccessorMockRecorder) GetDBConnectionConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDBConnectionConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetDBConnectionConfig), ctx)
}

// GetDefaultBuyerGeoLocationConfig mocks base method.
func (m *MockConfAccessor) GetDefaultBuyerGeoLocationConfig(ctx context.Context) business_config.GeoLocationConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultBuyerGeoLocationConfig", ctx)
	ret0, _ := ret[0].(business_config.GeoLocationConfig)
	return ret0
}

// GetDefaultBuyerGeoLocationConfig indicates an expected call of GetDefaultBuyerGeoLocationConfig.
func (mr *MockConfAccessorMockRecorder) GetDefaultBuyerGeoLocationConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultBuyerGeoLocationConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetDefaultBuyerGeoLocationConfig), ctx)
}

// GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig mocks base method.
func (m *MockConfAccessor) GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx context.Context) map[string]business_config.GeoLocationConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig", ctx)
	ret0, _ := ret[0].(map[string]business_config.GeoLocationConfig)
	return ret0
}

// GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig indicates an expected call of GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig.
func (mr *MockConfAccessorMockRecorder) GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig), ctx)
}

// GetEnable3PFIgnoreSellerTag mocks base method.
func (m *MockConfAccessor) GetEnable3PFIgnoreSellerTag(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnable3PFIgnoreSellerTag", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetEnable3PFIgnoreSellerTag indicates an expected call of GetEnable3PFIgnoreSellerTag.
func (mr *MockConfAccessorMockRecorder) GetEnable3PFIgnoreSellerTag(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnable3PFIgnoreSellerTag", reflect.TypeOf((*MockConfAccessor)(nil).GetEnable3PFIgnoreSellerTag), ctx)
}

// GetEnableAllocateWarehouseBySalesOrders mocks base method.
func (m *MockConfAccessor) GetEnableAllocateWarehouseBySalesOrders(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnableAllocateWarehouseBySalesOrders", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetEnableAllocateWarehouseBySalesOrders indicates an expected call of GetEnableAllocateWarehouseBySalesOrders.
func (mr *MockConfAccessorMockRecorder) GetEnableAllocateWarehouseBySalesOrders(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnableAllocateWarehouseBySalesOrders", reflect.TypeOf((*MockConfAccessor)(nil).GetEnableAllocateWarehouseBySalesOrders), ctx)
}

// GetEnableCachedSellerTagFlow mocks base method.
func (m *MockConfAccessor) GetEnableCachedSellerTagFlow(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnableCachedSellerTagFlow", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetEnableCachedSellerTagFlow indicates an expected call of GetEnableCachedSellerTagFlow.
func (mr *MockConfAccessorMockRecorder) GetEnableCachedSellerTagFlow(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnableCachedSellerTagFlow", reflect.TypeOf((*MockConfAccessor)(nil).GetEnableCachedSellerTagFlow), ctx)
}

// GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix mocks base method.
func (m *MockConfAccessor) GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix indicates an expected call of GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix.
func (mr *MockConfAccessorMockRecorder) GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix", reflect.TypeOf((*MockConfAccessor)(nil).GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix), ctx)
}

// GetFilterChannelsForPreorder mocks base method.
func (m *MockConfAccessor) GetFilterChannelsForPreorder(ctx context.Context) []int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilterChannelsForPreorder", ctx)
	ret0, _ := ret[0].([]int)
	return ret0
}

// GetFilterChannelsForPreorder indicates an expected call of GetFilterChannelsForPreorder.
func (mr *MockConfAccessorMockRecorder) GetFilterChannelsForPreorder(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterChannelsForPreorder", reflect.TypeOf((*MockConfAccessor)(nil).GetFilterChannelsForPreorder), ctx)
}

// GetGroupSellerCoverShippingFeeConfig mocks base method.
func (m *MockConfAccessor) GetGroupSellerCoverShippingFeeConfig(ctx context.Context) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupSellerCoverShippingFeeConfig", ctx)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetGroupSellerCoverShippingFeeConfig indicates an expected call of GetGroupSellerCoverShippingFeeConfig.
func (mr *MockConfAccessorMockRecorder) GetGroupSellerCoverShippingFeeConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupSellerCoverShippingFeeConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetGroupSellerCoverShippingFeeConfig), ctx)
}

// GetGrpcTimeoutConfig mocks base method.
func (m *MockConfAccessor) GetGrpcTimeoutConfig(ctx context.Context) mutable_application_config.TimeoutConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGrpcTimeoutConfig", ctx)
	ret0, _ := ret[0].(mutable_application_config.TimeoutConfig)
	return ret0
}

// GetGrpcTimeoutConfig indicates an expected call of GetGrpcTimeoutConfig.
func (mr *MockConfAccessorMockRecorder) GetGrpcTimeoutConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGrpcTimeoutConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetGrpcTimeoutConfig), ctx)
}

// GetHttpTimeoutConfig mocks base method.
func (m *MockConfAccessor) GetHttpTimeoutConfig(ctx context.Context) mutable_application_config.TimeoutConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHttpTimeoutConfig", ctx)
	ret0, _ := ret[0].(mutable_application_config.TimeoutConfig)
	return ret0
}

// GetHttpTimeoutConfig indicates an expected call of GetHttpTimeoutConfig.
func (mr *MockConfAccessorMockRecorder) GetHttpTimeoutConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHttpTimeoutConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetHttpTimeoutConfig), ctx)
}

// GetIGS3PFShopFlags mocks base method.
func (m *MockConfAccessor) GetIGS3PFShopFlags(ctx context.Context) []uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIGS3PFShopFlags", ctx)
	ret0, _ := ret[0].([]uint64)
	return ret0
}

// GetIGS3PFShopFlags indicates an expected call of GetIGS3PFShopFlags.
func (mr *MockConfAccessorMockRecorder) GetIGS3PFShopFlags(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIGS3PFShopFlags", reflect.TypeOf((*MockConfAccessor)(nil).GetIGS3PFShopFlags), ctx)
}

// GetIGSDynamicConfig mocks base method.
func (m *MockConfAccessor) GetIGSDynamicConfig(ctx context.Context) business_config.IGSDynamicConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIGSDynamicConfig", ctx)
	ret0, _ := ret[0].(business_config.IGSDynamicConfig)
	return ret0
}

// GetIGSDynamicConfig indicates an expected call of GetIGSDynamicConfig.
func (mr *MockConfAccessorMockRecorder) GetIGSDynamicConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIGSDynamicConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetIGSDynamicConfig), ctx)
}

// GetIGSLFFShopFlags mocks base method.
func (m *MockConfAccessor) GetIGSLFFShopFlags(ctx context.Context) []uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIGSLFFShopFlags", ctx)
	ret0, _ := ret[0].([]uint64)
	return ret0
}

// GetIGSLFFShopFlags indicates an expected call of GetIGSLFFShopFlags.
func (mr *MockConfAccessorMockRecorder) GetIGSLFFShopFlags(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIGSLFFShopFlags", reflect.TypeOf((*MockConfAccessor)(nil).GetIGSLFFShopFlags), ctx)
}

// GetIGSResellModeShopFlags mocks base method.
func (m *MockConfAccessor) GetIGSResellModeShopFlags(ctx context.Context) []uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIGSResellModeShopFlags", ctx)
	ret0, _ := ret[0].([]uint64)
	return ret0
}

// GetIGSResellModeShopFlags indicates an expected call of GetIGSResellModeShopFlags.
func (mr *MockConfAccessorMockRecorder) GetIGSResellModeShopFlags(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIGSResellModeShopFlags", reflect.TypeOf((*MockConfAccessor)(nil).GetIGSResellModeShopFlags), ctx)
}

// GetIGSShopBusinessModeConfig mocks base method.
func (m *MockConfAccessor) GetIGSShopBusinessModeConfig(ctx context.Context) business_config.IGSShopBusinessModeConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIGSShopBusinessModeConfig", ctx)
	ret0, _ := ret[0].(business_config.IGSShopBusinessModeConfig)
	return ret0
}

// GetIGSShopBusinessModeConfig indicates an expected call of GetIGSShopBusinessModeConfig.
func (mr *MockConfAccessorMockRecorder) GetIGSShopBusinessModeConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIGSShopBusinessModeConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetIGSShopBusinessModeConfig), ctx)
}

// GetIsShopeeFoodRegion mocks base method.
func (m *MockConfAccessor) GetIsShopeeFoodRegion(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIsShopeeFoodRegion", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetIsShopeeFoodRegion indicates an expected call of GetIsShopeeFoodRegion.
func (mr *MockConfAccessorMockRecorder) GetIsShopeeFoodRegion(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIsShopeeFoodRegion", reflect.TypeOf((*MockConfAccessor)(nil).GetIsShopeeFoodRegion), ctx)
}

// GetLFFWarehouseRegionPriority mocks base method.
func (m *MockConfAccessor) GetLFFWarehouseRegionPriority(ctx context.Context) []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLFFWarehouseRegionPriority", ctx)
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetLFFWarehouseRegionPriority indicates an expected call of GetLFFWarehouseRegionPriority.
func (mr *MockConfAccessorMockRecorder) GetLFFWarehouseRegionPriority(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLFFWarehouseRegionPriority", reflect.TypeOf((*MockConfAccessor)(nil).GetLFFWarehouseRegionPriority), ctx)
}

// GetLPSAPIToken mocks base method.
func (m *MockConfAccessor) GetLPSAPIToken(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLPSAPIToken", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetLPSAPIToken indicates an expected call of GetLPSAPIToken.
func (mr *MockConfAccessorMockRecorder) GetLPSAPIToken(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLPSAPIToken", reflect.TypeOf((*MockConfAccessor)(nil).GetLPSAPIToken), ctx)
}

// GetLayerCacheConfig mocks base method.
func (m *MockConfAccessor) GetLayerCacheConfig(ctx context.Context) mutable_application_config.LayerCacheConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLayerCacheConfig", ctx)
	ret0, _ := ret[0].(mutable_application_config.LayerCacheConfig)
	return ret0
}

// GetLayerCacheConfig indicates an expected call of GetLayerCacheConfig.
func (mr *MockConfAccessorMockRecorder) GetLayerCacheConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLayerCacheConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetLayerCacheConfig), ctx)
}

// GetLocalCacheConfig mocks base method.
func (m *MockConfAccessor) GetLocalCacheConfig(ctx context.Context) mutable_application_config.LocalCacheConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocalCacheConfig", ctx)
	ret0, _ := ret[0].(mutable_application_config.LocalCacheConfig)
	return ret0
}

// GetLocalCacheConfig indicates an expected call of GetLocalCacheConfig.
func (mr *MockConfAccessorMockRecorder) GetLocalCacheConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalCacheConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetLocalCacheConfig), ctx)
}

// GetLruCacheConfig mocks base method.
func (m *MockConfAccessor) GetLruCacheConfig(ctx context.Context) mutable_application_config.LruCacheConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLruCacheConfig", ctx)
	ret0, _ := ret[0].(mutable_application_config.LruCacheConfig)
	return ret0
}

// GetLruCacheConfig indicates an expected call of GetLruCacheConfig.
func (mr *MockConfAccessorMockRecorder) GetLruCacheConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLruCacheConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetLruCacheConfig), ctx)
}

// GetMigrationConfig mocks base method.
func (m *MockConfAccessor) GetMigrationConfig(ctx context.Context) business_config.MigrationConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMigrationConfig", ctx)
	ret0, _ := ret[0].(business_config.MigrationConfig)
	return ret0
}

// GetMigrationConfig indicates an expected call of GetMigrationConfig.
func (mr *MockConfAccessorMockRecorder) GetMigrationConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMigrationConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetMigrationConfig), ctx)
}

// GetOmsApiSecret mocks base method.
func (m *MockConfAccessor) GetOmsApiSecret(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOmsApiSecret", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetOmsApiSecret indicates an expected call of GetOmsApiSecret.
func (mr *MockConfAccessorMockRecorder) GetOmsApiSecret(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOmsApiSecret", reflect.TypeOf((*MockConfAccessor)(nil).GetOmsApiSecret), ctx)
}

// GetProductInfoAPISize mocks base method.
func (m *MockConfAccessor) GetProductInfoAPISize(ctx context.Context) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductInfoAPISize", ctx)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetProductInfoAPISize indicates an expected call of GetProductInfoAPISize.
func (mr *MockConfAccessorMockRecorder) GetProductInfoAPISize(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductInfoAPISize", reflect.TypeOf((*MockConfAccessor)(nil).GetProductInfoAPISize), ctx)
}

// GetReportDegradeConfig mocks base method.
func (m *MockConfAccessor) GetReportDegradeConfig(ctx context.Context) server_config.ReportDegradeConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReportDegradeConfig", ctx)
	ret0, _ := ret[0].(server_config.ReportDegradeConfig)
	return ret0
}

// GetReportDegradeConfig indicates an expected call of GetReportDegradeConfig.
func (mr *MockConfAccessorMockRecorder) GetReportDegradeConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReportDegradeConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetReportDegradeConfig), ctx)
}

// GetSBSApiKey mocks base method.
func (m *MockConfAccessor) GetSBSApiKey(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSBSApiKey", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetSBSApiKey indicates an expected call of GetSBSApiKey.
func (mr *MockConfAccessorMockRecorder) GetSBSApiKey(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSBSApiKey", reflect.TypeOf((*MockConfAccessor)(nil).GetSBSApiKey), ctx)
}

// GetSBSApiSecret mocks base method.
func (m *MockConfAccessor) GetSBSApiSecret(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSBSApiSecret", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetSBSApiSecret indicates an expected call of GetSBSApiSecret.
func (mr *MockConfAccessorMockRecorder) GetSBSApiSecret(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSBSApiSecret", reflect.TypeOf((*MockConfAccessor)(nil).GetSBSApiSecret), ctx)
}

// GetSellerMultiWHWithPartialFBSConfig mocks base method.
func (m *MockConfAccessor) GetSellerMultiWHWithPartialFBSConfig(ctx context.Context) business_config.SellerMultiWhWithPartialFBSConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSellerMultiWHWithPartialFBSConfig", ctx)
	ret0, _ := ret[0].(business_config.SellerMultiWhWithPartialFBSConfig)
	return ret0
}

// GetSellerMultiWHWithPartialFBSConfig indicates an expected call of GetSellerMultiWHWithPartialFBSConfig.
func (mr *MockConfAccessorMockRecorder) GetSellerMultiWHWithPartialFBSConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSellerMultiWHWithPartialFBSConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetSellerMultiWHWithPartialFBSConfig), ctx)
}

// GetShopEnableChannelsAPIBatchSize mocks base method.
func (m *MockConfAccessor) GetShopEnableChannelsAPIBatchSize(ctx context.Context) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShopEnableChannelsAPIBatchSize", ctx)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetShopEnableChannelsAPIBatchSize indicates an expected call of GetShopEnableChannelsAPIBatchSize.
func (mr *MockConfAccessorMockRecorder) GetShopEnableChannelsAPIBatchSize(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShopEnableChannelsAPIBatchSize", reflect.TypeOf((*MockConfAccessor)(nil).GetShopEnableChannelsAPIBatchSize), ctx)
}

// GetSpexConfig mocks base method.
func (m *MockConfAccessor) GetSpexConfig(ctx context.Context) application_config.SpexConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpexConfig", ctx)
	ret0, _ := ret[0].(application_config.SpexConfig)
	return ret0
}

// GetSpexConfig indicates an expected call of GetSpexConfig.
func (mr *MockConfAccessorMockRecorder) GetSpexConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpexConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetSpexConfig), ctx)
}

// GetSpexTimeoutConfig mocks base method.
func (m *MockConfAccessor) GetSpexTimeoutConfig(ctx context.Context) mutable_application_config.TimeoutConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpexTimeoutConfig", ctx)
	ret0, _ := ret[0].(mutable_application_config.TimeoutConfig)
	return ret0
}

// GetSpexTimeoutConfig indicates an expected call of GetSpexTimeoutConfig.
func (mr *MockConfAccessorMockRecorder) GetSpexTimeoutConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpexTimeoutConfig", reflect.TypeOf((*MockConfAccessor)(nil).GetSpexTimeoutConfig), ctx)
}

// GetSupportSellerMultiWhPFF mocks base method.
func (m *MockConfAccessor) GetSupportSellerMultiWhPFF(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportSellerMultiWhPFF", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetSupportSellerMultiWhPFF indicates an expected call of GetSupportSellerMultiWhPFF.
func (mr *MockConfAccessorMockRecorder) GetSupportSellerMultiWhPFF(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportSellerMultiWhPFF", reflect.TypeOf((*MockConfAccessor)(nil).GetSupportSellerMultiWhPFF), ctx)
}

// GetWeightDimMaxNumOfShippingOrders mocks base method.
func (m *MockConfAccessor) GetWeightDimMaxNumOfShippingOrders(ctx context.Context) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeightDimMaxNumOfShippingOrders", ctx)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetWeightDimMaxNumOfShippingOrders indicates an expected call of GetWeightDimMaxNumOfShippingOrders.
func (mr *MockConfAccessorMockRecorder) GetWeightDimMaxNumOfShippingOrders(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeightDimMaxNumOfShippingOrders", reflect.TypeOf((*MockConfAccessor)(nil).GetWeightDimMaxNumOfShippingOrders), ctx)
}

// GetWeightDimOrderSplitPercentage mocks base method.
func (m *MockConfAccessor) GetWeightDimOrderSplitPercentage(ctx context.Context) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeightDimOrderSplitPercentage", ctx)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetWeightDimOrderSplitPercentage indicates an expected call of GetWeightDimOrderSplitPercentage.
func (mr *MockConfAccessorMockRecorder) GetWeightDimOrderSplitPercentage(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeightDimOrderSplitPercentage", reflect.TypeOf((*MockConfAccessor)(nil).GetWeightDimOrderSplitPercentage), ctx)
}

// GetWeightDimOrderSplitWhitelist mocks base method.
func (m *MockConfAccessor) GetWeightDimOrderSplitWhitelist(ctx context.Context) []uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeightDimOrderSplitWhitelist", ctx)
	ret0, _ := ret[0].([]uint64)
	return ret0
}

// GetWeightDimOrderSplitWhitelist indicates an expected call of GetWeightDimOrderSplitWhitelist.
func (mr *MockConfAccessorMockRecorder) GetWeightDimOrderSplitWhitelist(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeightDimOrderSplitWhitelist", reflect.TypeOf((*MockConfAccessor)(nil).GetWeightDimOrderSplitWhitelist), ctx)
}

// Init mocks base method.
func (m *MockConfAccessor) Init(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Init", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init.
func (mr *MockConfAccessorMockRecorder) Init(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockConfAccessor)(nil).Init), ctx)
}

// IsEnableWarehouseRegionForAddressAPI mocks base method.
func (m *MockConfAccessor) IsEnableWarehouseRegionForAddressAPI(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsEnableWarehouseRegionForAddressAPI", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsEnableWarehouseRegionForAddressAPI indicates an expected call of IsEnableWarehouseRegionForAddressAPI.
func (mr *MockConfAccessorMockRecorder) IsEnableWarehouseRegionForAddressAPI(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEnableWarehouseRegionForAddressAPI", reflect.TypeOf((*MockConfAccessor)(nil).IsEnableWarehouseRegionForAddressAPI), ctx)
}

// Keys mocks base method.
func (m *MockConfAccessor) Keys() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Keys")
	ret0, _ := ret[0].([]string)
	return ret0
}

// Keys indicates an expected call of Keys.
func (mr *MockConfAccessorMockRecorder) Keys() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Keys", reflect.TypeOf((*MockConfAccessor)(nil).Keys))
}

// ValueICtx mocks base method.
func (m *MockConfAccessor) ValueICtx(ctx context.Context, key string) (interface{}, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValueICtx", ctx, key)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// ValueICtx indicates an expected call of ValueICtx.
func (mr *MockConfAccessorMockRecorder) ValueICtx(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValueICtx", reflect.TypeOf((*MockConfAccessor)(nil).ValueICtx), ctx, key)
}
