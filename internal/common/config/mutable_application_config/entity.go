package mutable_application_config

import (
	"context"
	"time"

	"github.com/bytedance/sonic"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
)

var (
	lruCacheConfig   = utils.NewRecordConfig[LruCacheConfig](LRUCacheKey, utils.WithRefreshFunc(lruCacheRefreshFuncAdapter))
	layerCacheConfig = utils.NewRecordConfig[LayerCacheConfig](LayerCacheConfigKey, utils.WithRefreshFunc(layerCacheRefreshFuncAdapter))
	localCacheConfig = utils.NewRecordConfig[LocalCacheConfig](LocalCacheConfigKey, utils.WithRefreshFunc(localCacheRefreshFuncAdapter), utils.WithDefaultValue(LocalCacheConfig{
		DefaultAutoRefresh:     proto.Bool(true),
		DefaultUsingLayerCache: proto.Bool(false),
		DefaultForceSwitch:     proto.Bool(true),
		KeyConfigsMap:          make(map[string]*LocalCacheKeyConfig),
	}))
	dbConfig           = utils.NewRecordConfig[DBConfig](DBConfigKey, utils.WithRefreshFunc(dbRefreshFuncAdapter))
	httpTimeoutConfig  = utils.NewRecordConfig[TimeoutConfig](HttpTimeoutKey, utils.WithRefreshFunc(timeoutRefreshFuncAdapter))
	grpcTimeoutConfig  = utils.NewRecordConfig[TimeoutConfig](GrpcTimeoutKey, utils.WithRefreshFunc(timeoutRefreshFuncAdapter))
	spexTimeoutConfig  = utils.NewRecordConfig[TimeoutConfig](SpexTimeoutKey, utils.WithRefreshFunc(timeoutRefreshFuncAdapter))
	lpsAPITokenConfig  = utils.NewStringConfig[string](LpsApiTokenKey)
	sbsApiKeyConfig    = utils.NewStringConfig[string](SbsApiKeyKey)
	sbsApiSecretConfig = utils.NewStringConfig[string](SbsApiSecretKey)
	omsApiSecretConfig = utils.NewStringConfig[string](OmsApiSecretKey)
)

type (
	LruCacheConfig struct {
		Config string                   `yaml:"config"` // LRU缓存公共配置
		Param  map[string]LruCacheParam // 用于保存config反序列化的内容
	}

	LruCacheParam struct {
		Timeout int64 `json:"timeout"` // LRU缓存过期时间，此为数值，默认单位：秒（s）
		Size    int   `json:"size"`    // LRU缓存长度
		Enable  bool  `json:"enable"`
	}

	LayerCacheConfig struct {
		MemSize             int                                  `yaml:"MemSize" json:"MemSize"`
		MemNumOfCounters    int                                  `yaml:"MemNumOfCounters" json:"MemNumOfCounters"`
		MemLogicTTL         int                                  `yaml:"MemLogicTTL" json:"MemLogicTTL"`
		MemPhysicalTTL      int                                  `yaml:"MemPhysicalTTL" json:"MemPhysicalTTL"`
		PenetrationQpsLimit int                                  `yaml:"PenetrationQpsLimit" json:"PenetrateQpsLimit"`
		Expire              string                               `yaml:"Expire" json:"Expire"`
		Namespaces          map[string]LayerCacheNamespaceConfig `yaml:"namespaces" json:"namespaces"`
		ExpireConfig        map[string]LayerCacheExpireConfig    `yaml:"expire_config" json:"expire_config"`
	}

	LayerCacheNamespaceConfig struct {
		MemSize          int `yaml:"MemSize" json:"MemSize"`
		MemNumOfCounters int `yaml:"MemNumOfCounters" json:"MemNumOfCounters"`
		MemLogicTTL      int `yaml:"MemLogicTTL" json:"MemLogicTTL"`
		MemPhysicalTTL   int `yaml:"MemPhysicalTTL" json:"MemPhysicalTTL"`
	}

	LayerCacheExpireConfig struct {
		Namespace     string `json:"namespace" yaml:"namespace"`
		ExpireSeconds uint32 `json:"expire_seconds" yaml:"expire_seconds"`
		Timeout       uint32 `json:"timeout" yaml:"timeout"`
	}

	LocalCacheConfig struct {
		DefaultForceSwitch     *bool                           `yaml:"default_force_switch"`      // 是否强制切换。true时，local-cache初始化成功后强制切换使用local-cache
		DefaultAutoRefresh     *bool                           `yaml:"default_auto_refresh"`      // 默认是否开启定时刷新。没有单独配置key的属性的话，则用这个值
		DefaultUsingLayerCache *bool                           `yaml:"default_using_layer_cache"` // 默认是否使用layer-cache。没有单独配置key的属性的话，则用这个值
		KeyConfigsJson         string                          `yaml:"key_configs_json"`          // json map key是local cache key, value是LocalCacheKeyConfig
		KeyConfigsMap          map[string]*LocalCacheKeyConfig // key_configs_json的struct实体
	}

	LocalCacheKeyConfig struct {
		IsAutoRefresh     *bool  `json:"is_auto_refresh"`      // 是否开启定时刷新
		IsUsingLayerCache *bool  `json:"is_using_layer_cache"` // 是否使用layer-cache
		Version           int64  `json:"version"`              // 配置版本号，守护协程会根据这个版本决定是否立即刷新
		CacheKey          string `json:"cache_key"`            // 配置作用的缓存key
	}

	DBConfig struct {
		Config                     string `yaml:"config"`       // JSON配置字符串
		ConnMaxAge                 int    `yaml:"conn_max_age"` //链接最大可存活时间，单位：s
		MaxIdleConn                int    `yaml:"max_idle_conn"`
		MaxOpenConn                int    `yaml:"max_open_conn"`
		DisableShadowDBForReadOnly bool   `yaml:"disable_shadow_db_for_read_only"`
		SlowQueryTime              int    `yaml:"slow_query_time"`
	}

	TimeoutConfig struct {
		Config map[constant.SystemCode]map[string]TimeoutItemConfig `json:"-" yaml:"-"` // 用于保存序列化后的内容
	}

	timeoutConfig map[constant.SystemCode]string

	TimeoutItemConfig struct {
		Endpoint string `json:"endpoint"`
		Timeout  int    `json:"timeout"` // Timeout: Millisecond
		Enable   bool   `json:"enable"`
	}
)

func lruCacheRefreshFuncAdapter(ctx context.Context, key string, defaultValue LruCacheConfig) (value LruCacheConfig, err error) {
	// 首先使用默认的刷新函数加载配置
	value, err = utils.DefaultRefreshFunc(ctx, key, defaultValue)
	if err != nil {
		return value, err
	}

	// 然后将 Config 字段反序列化到 Param 字段
	if value.Config != "" {
		param := make(map[string]LruCacheParam)
		if err := sonic.UnmarshalString(value.Config, &param); err != nil {
			logger.LogErrorf("Unmarshal LruCacheConfig.Config failed|key=%s|config=%s|err=%v",
				key, value.Config, err.Error())
			return value, err
		}
		value.Param = param
	}

	return value, nil
}

func layerCacheRefreshFuncAdapter(ctx context.Context, key string, defaultValue LayerCacheConfig) (value LayerCacheConfig, err error) {
	value, err = utils.DefaultRefreshFunc(ctx, key, defaultValue)
	if err != nil {
		return value, err
	}
	expireConfigList := make([]LayerCacheExpireConfig, 0)
	err = jsoniter.UnmarshalFromString(value.Expire, &expireConfigList)
	if err != nil {
		logger.LogErrorf("UnmarshalFromString failed|config=%s", value)
		return value, err
	}
	for _, expireConfig := range expireConfigList {
		value.ExpireConfig[expireConfig.Namespace] = expireConfig
	}

	return value, nil
}

func localCacheRefreshFuncAdapter(ctx context.Context, key string, defaultValue LocalCacheConfig) (value LocalCacheConfig, err error) {
	value, err = utils.DefaultRefreshFunc(ctx, key, defaultValue)
	if err != nil {
		logger.LogErrorf("UnmarshalFromString failed|config=%s", value)
		return value, err
	}

	value.KeyConfigsMap = make(map[string]*LocalCacheKeyConfig)
	if len(value.KeyConfigsJson) != 0 {
		sonic.UnmarshalString(value.KeyConfigsJson, &value.KeyConfigsMap)
	}
	return value, nil
}

func timeoutRefreshFuncAdapter(ctx context.Context, key string, defaultValue TimeoutConfig) (value TimeoutConfig, err error) {
	tc := make(timeoutConfig)
	tc, err = utils.DefaultRefreshFunc(ctx, key, tc)
	if err != nil {
		return defaultValue, err
	}
	value.Config = make(map[constant.SystemCode]map[string]TimeoutItemConfig, len(tc))
	for code, data := range tc {
		items := make([]TimeoutItemConfig, 0)
		if unmarshalErr := sonic.UnmarshalString(data, &items); unmarshalErr != nil {
			logger.LogErrorf("UnmarshalFromString TimeoutItemConfig %s failed|err=%v", code, unmarshalErr)
			continue
		}
		value.Config[code] = make(map[string]TimeoutItemConfig, len(items))
		for _, item := range items {
			value.Config[code][item.Endpoint] = item
		}
	}
	return value, nil
}

func dbRefreshFuncAdapter(ctx context.Context, key string, defaultValue DBConfig) (value DBConfig, err error) {
	value, err = utils.DefaultRefreshFunc(ctx, key, defaultValue)
	if err != nil {
		logger.LogErrorf("UnmarshalFromString DBConfig failed|err=%v", err)
		return value, err
	}
	return value, nil
}

func (t TimeoutConfig) GetTimeout(systemCode constant.SystemCode, endpoint string) (time.Duration, bool) {
	if len(t.Config) == 0 {
		return 0, false
	}
	items, ok := t.Config[systemCode]
	if !ok || len(items) == 0 {
		return 0, false
	}
	item, ok := items[endpoint]
	if !ok {
		return 0, false
	}
	return time.Duration(item.Timeout) * time.Millisecond, item.Enable && item.Timeout > 0
}
