package mutable_application_config

import "time"

const (
	refreshConfigInterval = 10 * time.Minute
)

const (
	mutableApplicationConfigPrefix = "mutable_application"

	LRUCacheKey         = "lru_cache"
	LayerCacheConfigKey = "layer_cache"
	LocalCacheConfigKey = "local_cache"
	DBConfigKey         = "db_config"
	HttpTimeoutKey      = "http_timeout"
	GrpcTimeoutKey      = "grpc_timeout"
	SpexTimeoutKey      = "spex_timeout"

	LpsApiTokenKey  = "lps_api_token"
	SbsApiKeyKey    = "sbs_api_key"
	SbsApiSecretKey = "sbs_api_secret"
	OmsApiSecretKey = "oms_api_secret"
)
