package mutable_application_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type MutableApplicationAccessor interface {
	GetLruCacheConfig(ctx context.Context) LruCacheConfig
	GetLayerCacheConfig(ctx context.Context) LayerCacheConfig
	GetLocalCacheConfig(ctx context.Context) LocalCacheConfig
	GetDBConfig(ctx context.Context) DBConfig
	GetHttpTimeoutConfig(ctx context.Context) TimeoutConfig
	GetGrpcTimeoutConfig(ctx context.Context) TimeoutConfig
	GetSpexTimeoutConfig(ctx context.Context) TimeoutConfig
	GetLPSAPIToken(ctx context.Context) string
	GetSBSApiKey(ctx context.Context) string
	GetSBSApiSecret(ctx context.Context) string
	GetOmsApiSecret(ctx context.Context) string
	Init(ctx context.Context) error
	utils.ConfManager
}

func NewMutableApplicationAccessorImpl() *MutableApplicationAccessorImpl {
	confListener := utils.NewConfListener(mutableApplicationConfigPrefix, refreshConfigInterval, true)
	accessor := &MutableApplicationAccessorImpl{
		ConfListener: confListener,
	}
	return accessor
}

type MutableApplicationAccessorImpl struct {
	utils.ConfListener
}

func (a *MutableApplicationAccessorImpl) GetLruCacheConfig(ctx context.Context) LruCacheConfig {
	return lruCacheConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetLayerCacheConfig(ctx context.Context) LayerCacheConfig {
	return layerCacheConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetLocalCacheConfig(ctx context.Context) LocalCacheConfig {
	return localCacheConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetDBConfig(ctx context.Context) DBConfig {
	return dbConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetHttpTimeoutConfig(ctx context.Context) TimeoutConfig {
	return httpTimeoutConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetGrpcTimeoutConfig(ctx context.Context) TimeoutConfig {
	return grpcTimeoutConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetSpexTimeoutConfig(ctx context.Context) TimeoutConfig {
	return spexTimeoutConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetLPSAPIToken(ctx context.Context) string {
	return lpsAPITokenConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetSBSApiKey(ctx context.Context) string {
	return sbsApiKeyConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetSBSApiSecret(ctx context.Context) string {
	return sbsApiSecretConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetOmsApiSecret(ctx context.Context) string {
	return omsApiSecretConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) Init(ctx context.Context) error {
	a.ConfListener.Register(lruCacheConfig)
	a.ConfListener.Register(layerCacheConfig)
	a.ConfListener.Register(localCacheConfig)
	a.ConfListener.Register(httpTimeoutConfig)
	a.ConfListener.Register(grpcTimeoutConfig)
	a.ConfListener.Register(spexTimeoutConfig)
	a.ConfListener.Register(lpsAPITokenConfig)
	a.ConfListener.Register(sbsApiKeyConfig)
	a.ConfListener.Register(sbsApiSecretConfig)
	a.ConfListener.Register(omsApiSecretConfig)
	a.ConfListener.Register(dbConfig)
	return a.ConfListener.Init(ctx)
}
