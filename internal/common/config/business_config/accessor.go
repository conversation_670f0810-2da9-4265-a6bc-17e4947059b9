package business_config

import (
	"context"

	abconfig "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type BusinessAccessor interface {
	GetMigrationConfig(ctx context.Context) MigrationConfig
	GetIGS3PFShopFlags(ctx context.Context) []uint64
	GetIGSLFFShopFlags(ctx context.Context) []uint64
	GetIGSResellModeShopFlags(ctx context.Context) []uint64
	GetLFFWarehouseRegionPriority(ctx context.Context) []string
	GetIGSDynamicConfig(ctx context.Context) IGSDynamicConfig
	GetIGSShopBusinessModeConfig(ctx context.Context) IGSShopBusinessModeConfig
	GetIsShopeeFoodRegion(ctx context.Context) bool
	IsEnableWarehouseRegionForAddressAPI(ctx context.Context) bool
	GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx context.Context) map[string]GeoLocationConfig
	GetDefaultBuyerGeoLocationConfig(ctx context.Context) GeoLocationConfig
	GetABTestingConfig(ctx context.Context) abconfig.Config
	GetEnableCachedSellerTagFlow(ctx context.Context) bool
	GetBatchGetEntityTagAPISize(ctx context.Context) int
	GetAllocateWarehouseBySalesOrderGroups(ctx context.Context) []AllocateWarehouseBySalesOrdersGroup
	GetSupportSellerMultiWhPFF(ctx context.Context) bool
	GetSellerMultiWHWithPartialFBSConfig(ctx context.Context) SellerMultiWhWithPartialFBSConfig
	GetEnableAllocateWarehouseBySalesOrders(ctx context.Context) bool
	GetEnable3PFIgnoreSellerTag(ctx context.Context) bool
	GetShopEnableChannelsAPIBatchSize(ctx context.Context) int
	GetProductInfoAPISize(ctx context.Context) int
	GetGroupSellerCoverShippingFeeConfig(ctx context.Context) int
	GetFilterChannelsForPreorder(ctx context.Context) []int
	GetCounterfeitItemChannelBlockToggle(ctx context.Context) bool
	GetCounterfeitItemLabelId(ctx context.Context) uint64
	GetChannelsWithSelectableDeliveryTime(ctx context.Context) []int
	GetWeightDimMaxNumOfShippingOrders(ctx context.Context) int
	GetWeightDimOrderSplitPercentage(ctx context.Context) int
	GetWeightDimOrderSplitWhitelist(ctx context.Context) []uint64
	GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix(ctx context.Context) bool

	Init(ctx context.Context) error
	utils.ConfManager
}

func NewBusinessAccessorImpl() *BusinessAccessorImpl {
	confListener := utils.NewConfListener(businessPrefix, refreshConfigInterval, true)
	accessor := &BusinessAccessorImpl{
		ConfListener: confListener,
	}
	return accessor
}

type BusinessAccessorImpl struct {
	utils.ConfListener
}

func (a *BusinessAccessorImpl) GetMigrationConfig(ctx context.Context) MigrationConfig {
	return migrationConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGS3PFShopFlags(ctx context.Context) []uint64 {
	return igs3PFShopFlagsConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGSLFFShopFlags(ctx context.Context) []uint64 {
	return igsLFFShopFlags.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGSResellModeShopFlags(ctx context.Context) []uint64 {
	return igsResellModelShopFlags.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetLFFWarehouseRegionPriority(ctx context.Context) []string {
	return lffWarehouseRegionPriority.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGSDynamicConfig(ctx context.Context) IGSDynamicConfig {
	return igsDynamicConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGSShopBusinessModeConfig(ctx context.Context) IGSShopBusinessModeConfig {
	return igsShopBusinessModeConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIsShopeeFoodRegion(ctx context.Context) bool {
	return isShopeeFoodRegionConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) IsEnableWarehouseRegionForAddressAPI(ctx context.Context) bool {
	return enableWarehouseRegionForAddressAPIConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx context.Context) map[string]GeoLocationConfig {
	return defaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetDefaultBuyerGeoLocationConfig(ctx context.Context) GeoLocationConfig {
	return defaultBuyerGeoLocationConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetABTestingConfig(ctx context.Context) abconfig.Config {
	return abTestingConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetEnableCachedSellerTagFlow(ctx context.Context) bool {
	return enableCachedSellerTagFlowConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetBatchGetEntityTagAPISize(ctx context.Context) int {
	return batchGetEntityTagAPISizeConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetAllocateWarehouseBySalesOrderGroups(ctx context.Context) []AllocateWarehouseBySalesOrdersGroup {
	return allocateWarehouseBySalesOrdersGroupConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetSupportSellerMultiWhPFF(ctx context.Context) bool {
	return supportSellerMultiWhPFFConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetSellerMultiWHWithPartialFBSConfig(ctx context.Context) SellerMultiWhWithPartialFBSConfig {
	return sellerMultiWhWithPartialFBSConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetEnableAllocateWarehouseBySalesOrders(ctx context.Context) bool {
	return enableAllocateWarehouseBySalesOrders.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetEnable3PFIgnoreSellerTag(ctx context.Context) bool {
	return enable3PFIgnoreSellerTagConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetShopEnableChannelsAPIBatchSize(ctx context.Context) int {
	return shopEnableChannelsAPIBatchSizeConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetProductInfoAPISize(ctx context.Context) int {
	return getProductInfoAPISizeConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetGroupSellerCoverShippingFeeConfig(ctx context.Context) int {
	return groupSellerCoverShippingFeeConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetFilterChannelsForPreorder(ctx context.Context) []int {
	return filterChannelsForPreorderConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetCounterfeitItemChannelBlockToggle(ctx context.Context) bool {
	return counterfeitItemChannelBlockToggleConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetCounterfeitItemLabelId(ctx context.Context) uint64 {
	return uint64(counterfeitItemLabelIdConfig.ValueCtx(ctx).Get())
}

func (a *BusinessAccessorImpl) GetChannelsWithSelectableDeliveryTime(ctx context.Context) []int {
	return channelsWithSelectableDeliveryTimeConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetWeightDimMaxNumOfShippingOrders(ctx context.Context) int {
	return weightDimMaxNumOfShippingOrdersConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetWeightDimOrderSplitPercentage(ctx context.Context) int {
	return weightDimOrderSplitPercentageConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetWeightDimOrderSplitWhitelist(ctx context.Context) []uint64 {
	return weightDimOrderSplitWhitelistConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetEnableIGSPFFRatioAndMultiSellerWHAllocationFix(ctx context.Context) bool {
	return enableIGSPFFRatioAndMultiSellerWHAllocationFixConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) Init(ctx context.Context) error {
	a.ConfListener.Register(migrationConfig)
	a.ConfListener.Register(igs3PFShopFlagsConfig)
	a.ConfListener.Register(igsLFFShopFlags)
	a.ConfListener.Register(igsResellModelShopFlags)
	a.ConfListener.Register(lffWarehouseRegionPriority)
	a.ConfListener.Register(igsDynamicConfig)
	a.ConfListener.Register(igsShopBusinessModeConfig)
	a.ConfListener.Register(isShopeeFoodRegionConfig)
	a.ConfListener.Register(enableWarehouseRegionForAddressAPIConfig)
	a.ConfListener.Register(defaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig)
	a.ConfListener.Register(defaultBuyerGeoLocationConfig)
	a.ConfListener.Register(abTestingConfig)
	a.ConfListener.Register(enableCachedSellerTagFlowConfig)
	a.ConfListener.Register(batchGetEntityTagAPISizeConfig)
	a.ConfListener.Register(allocateWarehouseBySalesOrdersGroupConfig)
	a.ConfListener.Register(supportSellerMultiWhPFFConfig)
	a.ConfListener.Register(sellerMultiWhWithPartialFBSConfig)
	a.ConfListener.Register(enableAllocateWarehouseBySalesOrders)
	a.ConfListener.Register(enable3PFIgnoreSellerTagConfig)
	a.ConfListener.Register(shopEnableChannelsAPIBatchSizeConfig)
	a.ConfListener.Register(getProductInfoAPISizeConfig)
	a.ConfListener.Register(groupSellerCoverShippingFeeConfig)
	a.ConfListener.Register(filterChannelsForPreorderConfig)
	a.ConfListener.Register(counterfeitItemChannelBlockToggleConfig)
	a.ConfListener.Register(counterfeitItemLabelIdConfig)
	a.ConfListener.Register(channelsWithSelectableDeliveryTimeConfig)
	a.ConfListener.Register(weightDimMaxNumOfShippingOrdersConfig)
	a.ConfListener.Register(weightDimOrderSplitPercentageConfig)
	a.ConfListener.Register(weightDimOrderSplitWhitelistConfig)
	a.ConfListener.Register(enableIGSPFFRatioAndMultiSellerWHAllocationFixConfig)
	return a.ConfListener.Init(ctx)
}
