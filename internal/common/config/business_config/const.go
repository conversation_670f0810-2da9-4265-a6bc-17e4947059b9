package business_config

import "time"

const (
	refreshConfigInterval = 10 * time.Minute
)

const (
	businessPrefix = "business"

	MigrationKey                                      = "migration"
	IGS3PFShopFlagsKey                                = "igs_3pf_shop_flags"
	IGSLFFShopFlagsKey                                = "igs_lff_shop_flags"
	IGSResellModelShopFlagsKey                        = "igs_resell_model_shop_flags"
	LFFWarehouseRegionPriorityKey                     = "lff_warehouse_region_priority"
	IGSDynamicConfigKey                               = "igs_dynamic_config"
	IGSShopBusinessModeConfigKey                      = "igs_shop_business_mode_config"
	IsShopeeFoodRegionKey                             = "is_shopee_food_region"
	EnableWarehouseRegionForAddressAPIKey             = "enable_warehouse_region_for_address_api"
	DefaultSIPPrimaryRegionToDummyBuyerGeoLocationKey = "default_sip_primary_region_to_dummy_buyer_geo_location"
	DefaultBuyerGeoLocationKey                        = "default_buyer_geo_location"
	AbTestingKey                                      = "ab_testing" // todo add it
	Enable3PFIgnoreSellerTagKey                       = "enable_3pf_ignore_seller_tag"
	EnableCachedSellerTagFlowKey                      = "enable_cached_seller_tag_flow" // todo no use
	EnableIGSPFFRatioAndMultiSellerWHAllocationFixKey = "enable_igs_pff_ratio_and_multi_seller_wh_allocation_fix"
	BatchGetEntityTagAPISizeKey                       = "batch_get_entity_tag_api_size" // todo no use
	AllocateWarehouseBySalesOrderGroupsKey            = "allocate_warehouse_by_sales_order_groups"
	SupportSellerMultiWhPFFKey                        = "support_seller_multi_wh_pff"
	SellerMultiWHWithPartialFBSKey                    = "seller_multiwh_with_partial_fbs" // todo add it
	EnableAllocateWarehouseBySalesOrdersKey           = "enable_allocate_warehouse_by_sales_orders"
	BatchGetShopDisplayLogisticChannelsApiSizeKey     = "batch_get_shop_display_logistic_channels_api_size"
	GetProductInfoApiSizeKey                          = "get_product_info_api_size"
	GroupSellerCoverShippingFeeConfig                 = "group_seller_cover_shipping_fee"
	FilterChannelsForPreorder                         = "filter_channels_for_preorder"          // todo no use
	CounterfeitItemChannelBlockToggleKey              = "counterfeit_item_channel_block_toggle" // todo no use
	CounterfeitItemLabelIdKey                         = "counterfeit_item_label_id"
	ChannelsWithSelectableDeliveryTimeKey             = "channels_with_selectable_delivery_time"
	WeightDimMaxNumOfShippingOrdersKey                = "weight_dim_max_num_of_shipping_orders"
	WeightDimOrderSplitPercentageKey                  = "weight_dim_order_split_percentage"
	WeightDimOrderSplitWhitelistKey                   = "weight_dim_order_split_whitelist"
)
