package business_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/bytedance/sonic"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type (
	IGSDynamicConfig struct {
		DefaultWhPriority                []string                   `json:"default_wh_priority" yaml:"default_wh_priority"`
		TransferDecoupleShopLogic        TransferDecoupleShopConfig `json:"transfer_decouple_shop_config" yaml:"transfer_decouple_shop_config"`
		DowngradeToggle                  DowngradeToggle            `json:"downgrade_toggle" yaml:"downgrade_toggle"`
		ShipFromOverseaFlagToggle        bool                       `json:"ship_from_oversea_flag_toggle" yaml:"ship_from_oversea_flag_toggle"`
		ShipFromOverseaFlagUserWhitelist []uint64                   `json:"ship_from_oversea_flag_user_whitelist" yaml:"ship_from_oversea_flag_user_whitelist"`
		MinParcelLimit                   int                        `json:"min_parcel_limit" yaml:"min_parcel_limit"`
	}

	TransferDecoupleShopConfig struct {
		Enable  bool   `json:"enable" yaml:"enable"`
		TagName string `json:"tag_name" yaml:"tag_name"`
		TagId   uint64 `json:"tag_id" yaml:"tag_id"`
	}

	DowngradeToggle struct {
		ToggleOffOmsWhPriorityApi              bool `json:"toggle_off_oms_wh_priority_api" yaml:"toggle_off_oms_wh_priority_api"`
		ToggleOffWhSplitStrategyApi            bool `json:"toggle_off_wh_split_strategy_api" yaml:"toggle_off_wh_split_strategy_api"`
		EnablePFFCheckoutAllocationImprovement bool `json:"enable_pff_checkout_allocation_improvement" yaml:"enable_pff_checkout_allocation_improvement"`
		Enable3PFSellerWhPriority              bool `json:"enable_3pf_seller_wh_priority" yaml:"enable_3pf_seller_wh_priority"`
	}

	GeoLocationConfig struct {
		Latitude  float64 `json:"latitude" yaml:"latitude"`
		Longitude float64 `json:"longitude" yaml:"longitude"`
	}
)

func igsDynamicConfigRefreshFuncAdapter(ctx context.Context, key string, defaultValue IGSDynamicConfig) (value IGSDynamicConfig, err error) {
	type temp struct {
		DefaultWhPriority                string                     `json:"default_wh_priority" yaml:"default_wh_priority"`
		TransferDecoupleShopLogic        TransferDecoupleShopConfig `json:"transfer_decouple_shop_config" yaml:"transfer_decouple_shop_config"`
		DowngradeToggle                  DowngradeToggle            `json:"downgrade_toggle" yaml:"downgrade_toggle"`
		ShipFromOverseaFlagToggle        bool                       `json:"ship_from_oversea_flag_toggle" yaml:"ship_from_oversea_flag_toggle"`
		ShipFromOverseaFlagUserWhitelist []uint64                   `json:"ship_from_oversea_flag_user_whitelist" yaml:"ship_from_oversea_flag_user_whitelist"`
		MinParcelLimit                   int                        `json:"min_parcel_limit" yaml:"min_parcel_limit"`
	}

	c, err := utils.DefaultRefreshFunc(ctx, key, temp{})
	if err != nil {
		return defaultValue, err
	}

	value = IGSDynamicConfig{
		TransferDecoupleShopLogic: c.TransferDecoupleShopLogic,
		DowngradeToggle:           c.DowngradeToggle,
		ShipFromOverseaFlagToggle: c.ShipFromOverseaFlagToggle,
		MinParcelLimit:            c.MinParcelLimit,
	}
	value.ShipFromOverseaFlagUserWhitelist = c.ShipFromOverseaFlagUserWhitelist

	if unmarshalErr := sonic.UnmarshalString(c.DefaultWhPriority, &value.DefaultWhPriority); unmarshalErr != nil {
		logger.LogErrorf("UnmarshalFromString DefaultWhPriority failed|config=%s", c.DefaultWhPriority)
	}
	return value, nil
}
