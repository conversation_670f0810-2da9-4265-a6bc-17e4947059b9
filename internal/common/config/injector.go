package config

import (
	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/datasource_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
)

var (
	ConfAccessorSet = wire.NewSet(
		NewConfAccessorImpl,
		wire.Bind(new(ConfAccessor), new(*ConfAccessorImpl)),
		application_config.ApplicationAccessorSet,
		business_config.BusinessAccessorSet,
		mutable_application_config.MutableApplicationAccessorSet,
		server_config.ServerAccessorSet,
		datasource_config.DatasourceAccessorSet,
	)
)
