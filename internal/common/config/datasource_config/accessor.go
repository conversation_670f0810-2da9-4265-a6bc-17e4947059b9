package datasource_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type DatasourceAccessor interface {
	GetDBConnectionConfig(ctx context.Context) DBConnectionConfig

	Init(ctx context.Context) error
	utils.ConfManager
}

func NewDatasourceAccessorImpl() *DatasourceAccessorImpl {
	confListener := utils.NewConfListener(datasourceApplicationConfigPrefix, refreshConfigInterval, true)
	accessor := &DatasourceAccessorImpl{
		ConfListener: confListener,
	}
	return accessor
}

type DatasourceAccessorImpl struct {
	utils.ConfListener
}

func (a *DatasourceAccessorImpl) GetDBConnectionConfig(ctx context.Context) DBConnectionConfig {
	return dbConnectionConfig.ValueCtx(ctx).Get()
}

func (a *DatasourceAccessorImpl) Init(ctx context.Context) error {
	a.ConfListener.Register(dbConnectionConfig)

	return a.ConfListener.Init(ctx)
}
