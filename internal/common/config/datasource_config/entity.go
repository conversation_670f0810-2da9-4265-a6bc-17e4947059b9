package datasource_config

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

var (
	dbConnectionConfig = utils.NewRecordConfig[DBConnectionConfig](dbCoonKey)
)

type (
	DBConnectionConfig map[string]DBConnection

	DBConnection struct {
		MasterDsn   string `json:"masterDsn" yaml:"masterDsn"`
		ReplicasDsn string `json:"replicasDsn" yaml:"replicasDsn"`
	}
)
