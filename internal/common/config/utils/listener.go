package utils

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/event"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

// KeyListener 配置监听需要实现此方法，单独隔离原因是因为 golang 不支持泛型集合，例如 []Config{ Config[int], Config[float64]}
type KeyListener interface {
	Key() string
	Refresh(ctx context.Context, confKey string) (value interface{})
	ValueICtx(ctx context.Context) interface{}
}

type ConfManager interface {
	Keys() []string
	ValueICtx(ctx context.Context, key string) (interface{}, bool)
	All(ctx context.Context) map[string]interface{}
}

type ConfListener interface {
	Register(keyListeners ...KeyListener)
	Init(ctx context.Context) error
	config.ConfigListener
	ConfManager
}

func NewConfListener(prefix string, refresh time.Duration, needToUpdate bool) ConfListener {
	listener := &confListener{
		Prefix:       prefix,
		ticket:       time.NewTicker(refresh),
		needToUpdate: needToUpdate,
	}
	return listener
}

type confListener struct {
	Prefix          string
	keyListenerList []KeyListener
	lock            sync.Mutex
	once            sync.Once
	ticket          *time.Ticker
	needToUpdate    bool
}

func (l *confListener) Register(keyListeners ...KeyListener) {
	l.lock.Lock()
	defer l.lock.Unlock()
	for _, keyListener := range keyListeners {
		l.keyListenerList = append(l.keyListenerList, keyListener)
	}
}

func (l *confListener) ConfPrefix() string {
	return l.Prefix
}

func (l *confListener) Event(events []*event.Event) {
	if len(l.keyListenerList) == 0 {
		return
	}
	l.lock.Lock()
	defer l.lock.Unlock()
	ctx := context.Background()
	for _, e := range events {
		for _, conf := range l.keyListenerList {
			key := e.Key
			confKey := l.getConfigKey(conf.Key())
			if strings.HasPrefix(key, confKey) {
				value := conf.Refresh(ctx, confKey)
				logger.LogInfof("After refresh: %s, value: %s", conf.Key(), toString(value))
			}
		}
	}
}

func (l *confListener) Keys() []string {
	if l == nil {
		return make([]string, 0)
	}
	keys := make([]string, 0, len(l.keyListenerList))
	for _, listener := range l.keyListenerList {
		keys = append(keys, l.getConfigKey(listener.Key()))
	}
	return keys
}

func (l *confListener) ValueICtx(ctx context.Context, key string) (interface{}, bool) {
	if l == nil {
		return nil, false
	}
	for _, listener := range l.keyListenerList {
		if l.getConfigKey(listener.Key()) == key {
			return listener.ValueICtx(ctx), true
		}
	}
	return nil, false
}

func (l *confListener) All(ctx context.Context) map[string]interface{} {
	if l == nil {
		return make(map[string]interface{})
	}
	ret := make(map[string]interface{}, len(l.keyListenerList))
	for _, listener := range l.keyListenerList {
		ret[l.getConfigKey(listener.Key())] = listener.ValueICtx(ctx)
	}
	return ret
}

func (l *confListener) Init(ctx context.Context) error {
	l.once.Do(func() {
		l.refresh(ctx)
	})
	if l.needToUpdate {
		l.regularlyRefresh(ctx)
		err := config.RegisterListener(l)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("register %s config listener err", l.ConfPrefix()))
		}
	}
	fmt.Printf("register %s config listener success\n", l.ConfPrefix())
	return nil
}

func (l *confListener) regularlyRefresh(ctx context.Context) {
	go func() {
		defer l.ticket.Stop()
		for range l.ticket.C {
			l.refresh(ctx)
		}
	}()
}

func (l *confListener) refresh(ctx context.Context) {
	l.lock.Lock()
	defer l.lock.Unlock()
	for _, conf := range l.keyListenerList {
		key := l.getConfigKey(conf.Key())
		value := conf.Refresh(ctx, key)
		logger.LogInfof("After refresh: %s, value: %s", conf.Key(), toString(value))
	}
}

func (l *confListener) getConfigKey(key string) string {
	return l.ConfPrefix() + "." + key
}

func toString(v interface{}) string {
	data, _ := sonic.MarshalString(v)
	return data
}
