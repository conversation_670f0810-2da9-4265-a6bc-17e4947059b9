package utils

import (
	"context"
	"fmt"
	"testing"

	"git.garena.com/shopee/bg-logistics/dms/go-proto/playback"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/mocker"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

func TestRecordConfig_Refresh(t *testing.T) {
	sy := newTestingGlobalVarSyncer()
	recorder.SetSyncer(sy)
	recorder.SetMocker(sy)
	chassis_config.EnableRecordForTest()

	t.Run("录制并回放一个实例类型的配置", func(t *testing.T) {
		type C = testStruct
		key := "test_record_ts"
		transactionId := "globalvar-1"
		ctx := context.Background()
		source := mocker.GenMockData[C]()
		ctx = recorder.StartRecordTransaction(ctx, transactionId)
		recordConfig := NewRecordConfig(key, WithDefaultValue(source))
		val1 := recordConfig.ValueCtx(ctx)
		recorder.EndRecordTransaction(ctx)
		equal(t, source, val1.Get())

		mocker.UpdateMockData(&source)
		recordConfig.Update(typ.NewOptional(source))
		ctx = context.Background()
		ctx = recorder.SetReplayId(ctx, transactionId)
		val2 := recordConfig.ValueCtx(ctx)
		notEqual(t, source, val2.Get())
		equal(t, val1, val2)
	})
}

// copy from git.garena.com/shopee/bg-logistics/go/gocommon recorder/global_test.go
var _ recorder.Syncer = (*noOpSyncer)(nil)

type noOpSyncer struct {
}

func (n noOpSyncer) Send(ctx context.Context, tran playback.Transaction) error {
	return nil
}

func (n noOpSyncer) Stop(ctx context.Context) error {
	return nil
}

type testingGlobalVarSyncer struct {
	result playback.Transaction
}

func (t *testingGlobalVarSyncer) GetMatchedResponse(ctx context.Context, req *recorder.MockRequest, rsp recorder.MockResponse) (isMatch bool, err error) {
	if len(t.result.OutBoundReqs) == 0 {
		return false, fmt.Errorf("mock fail")
	}
	if t.result.OutBoundReqs[0].ReqData != req.RequestData {
		return false, fmt.Errorf("mock fail")
	}
	resp := t.result.OutBoundReqs[0].RspData
	err = rsp.Unmarshal(ctx, resp)
	if err != nil {
		return false, err
	}
	return true, nil
}

func newTestingGlobalVarSyncer() *testingGlobalVarSyncer {
	return &testingGlobalVarSyncer{}
}

func (t *testingGlobalVarSyncer) Send(ctx context.Context, tran playback.Transaction) error {
	t.result = tran
	return nil
}

func (t *testingGlobalVarSyncer) Stop(ctx context.Context) error {
	return nil
}

func (t *testingGlobalVarSyncer) Reset() {
	t.result = playback.Transaction{}
}
