package utils

import (
	"context"
	"sync"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/bytedance/sonic"
	uuid "github.com/satori/go.uuid"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/recordutil"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type Config[T any] interface {
	// KeyListener 配置监听方法
	KeyListener

	// Value 返回配置值，任何时候不会使用流量录制的值
	//       业务使用时会识别数据没有配置的情况, 因此使用 typ.Optional 封装
	Value() typ.Optional[T]
	// ValueCtx 返回配置值，如果开启了流量录制，会返回流量录制的值
	ValueCtx(ctx context.Context) typ.Optional[T]

	// valuePtr 返回配置对应值的指针，用于流量录制
	valuePtr() *typ.Optional[T]
	// UniqueId 返回配置的唯一ID，用于流量录制
	uniqueId() string

	// WithRefreshFunc 设置配置更新函数
	WithRefreshFunc(refreshFunc RefreshFunc[T])
	// Update 手动更新配置值（主要用于测试）
	Update(newVal typ.Optional[T])
}

func NewRecordConfig[T any](key string, opts ...ConfigOptions[T]) *RecordConfig[T] {
	structConfig := NewStructConfig(key, opts...)
	return newRecordConfig(structConfig, opts...)
}

func NewIntRecordConfig[T ~int](key string, opts ...ConfigOptions[T]) *RecordConfig[T] {
	structConfig := NewIntConfig(key, opts...)
	return newRecordConfig(structConfig, opts...)
}

func NewInt64RecordConfig[T ~int64](key string, opts ...ConfigOptions[T]) *RecordConfig[T] {
	structConfig := NewInt64Config(key, opts...)
	return newRecordConfig(structConfig, opts...)
}

func NewBoolRecordConfig[T typ.Bool](key string, opts ...ConfigOptions[T]) *RecordConfig[T] {
	structConfig := NewBoolConfig(key, opts...)
	return newRecordConfig(structConfig, opts...)
}

func NewStringRecordConfig[T typ.String](key string, opts ...ConfigOptions[T]) *RecordConfig[T] {
	structConfig := NewStringConfig(key, opts...)
	return newRecordConfig(structConfig, opts...)
}

func NewFloat64RecordConfig[T ~float64](key string, opts ...ConfigOptions[T]) *RecordConfig[T] {
	structConfig := NewFloat64Config(key, opts...)
	return newRecordConfig(structConfig, opts...)
}

func NewJsonRecordConfig[T any](key string, opts ...ConfigOptions[T]) *RecordConfig[T] {
	structConfig := NewJsonConfig(key, opts...)
	return newRecordConfig(structConfig, opts...)
}

func newRecordConfig[T any](c Config[T], opts ...ConfigOptions[T]) *RecordConfig[T] {
	rc := &RecordConfig[T]{
		Config: c,
	}
	rc.initRecorderFunc()
	return rc
}

// RecordConfig 支持流量录制， 默认使用 Record Config
type RecordConfig[T any] struct {
	Config       Config[T]
	recorderFunc func(ctx context.Context) *typ.Optional[T]
	lock         sync.RWMutex
}

func (c *RecordConfig[T]) initRecorderFunc() {
	c.lock.Lock()
	defer c.lock.Unlock()
	c.recorderFunc = recordutil.RegisterGlobalVar(c.uniqueId(), c.Config.valuePtr())
}

func (c *RecordConfig[T]) ValueCtx(ctx context.Context) typ.Optional[T] {
	if chassis_config.IsRecordEnable() {
		valuePtr := c.recorderFunc(ctx)
		if valuePtr != nil {
			return *valuePtr
		}
	}
	return c.Config.Value()
}

func (c *RecordConfig[T]) ValueICtx(ctx context.Context) interface{} {
	return c.Config.ValueCtx(ctx).Get()
}

func (c *RecordConfig[T]) Key() string {
	return c.Config.Key()
}

func (c *RecordConfig[T]) uniqueId() string {
	return c.Config.uniqueId()
}

func (c *RecordConfig[T]) Value() typ.Optional[T] {
	return c.Config.Value()
}

func (c *RecordConfig[T]) Refresh(ctx context.Context, confKey string) (value interface{}) {
	value = c.Config.Refresh(ctx, confKey)
	c.initRecorderFunc()
	return value
}

func (c *RecordConfig[T]) Update(newVal typ.Optional[T]) {
	c.Config.Update(newVal)
	c.initRecorderFunc()
}

func (c *RecordConfig[T]) WithRefreshFunc(f RefreshFunc[T]) {
	c.Config.WithRefreshFunc(f)
}

func (c *RecordConfig[T]) valuePtr() *typ.Optional[T] {
	return c.Config.valuePtr()
}

func NewStructConfig[T any](key string, opts ...ConfigOptions[T]) *StructConfig[T] {
	option := ConfigOption[T]{
		refreshFunc: DefaultRefreshFunc[T],
	}
	for _, opt := range opts {
		opt(&option)
	}
	unique := uuid.NewV4().String() + "_" + key

	return &StructConfig[T]{
		unique:       unique,
		key:          key,
		value:        option.defaultValue,
		defaultValue: option.defaultValue,
		refreshFunc:  option.refreshFunc,
	}
}

func NewIntConfig[T ~int](key string, opts ...ConfigOptions[T]) *StructConfig[T] {
	opts = append([]ConfigOptions[T]{WithRefreshFunc(IntRefreshFunc[T])}, opts...)
	return NewStructConfig[T](key, opts...)
}

func NewInt64Config[T ~int64](key string, opts ...ConfigOptions[T]) *StructConfig[T] {
	opts = append([]ConfigOptions[T]{WithRefreshFunc(Int64RefreshFunc[T])}, opts...)
	return NewStructConfig[T](key, opts...)
}

func NewBoolConfig[T typ.Bool](key string, opts ...ConfigOptions[T]) *StructConfig[T] {
	opts = append([]ConfigOptions[T]{WithRefreshFunc(BoolRefreshFunc[T])}, opts...)
	return NewStructConfig[T](key, opts...)
}

func NewStringConfig[T typ.String](key string, opts ...ConfigOptions[T]) *StructConfig[T] {
	opts = append([]ConfigOptions[T]{WithRefreshFunc(StringRefreshFunc[T])}, opts...)
	return NewStructConfig[T](key, opts...)
}

func NewFloat64Config[T ~float64](key string, opts ...ConfigOptions[T]) *StructConfig[T] {
	opts = append([]ConfigOptions[T]{WithRefreshFunc(Float64RefreshFunc[T])}, opts...)
	return NewStructConfig[T](key, opts...)
}

func NewJsonConfig[T any](key string, opts ...ConfigOptions[T]) *StructConfig[T] {
	opts = append([]ConfigOptions[T]{WithRefreshFunc(JsonRefreshFunc[T])}, opts...)
	return NewStructConfig[T](key, opts...)
}

// StructConfig 封装了刷新方法
type StructConfig[T any] struct {
	unique       string
	key          string
	value        typ.Optional[T]
	defaultValue typ.Optional[T]
	refreshFunc  RefreshFunc[T]
}

func (s *StructConfig[T]) Key() string {
	return s.key
}

func (s *StructConfig[T]) uniqueId() string {
	return s.unique
}

func (s *StructConfig[T]) Value() typ.Optional[T] {
	return s.value
}

func (s *StructConfig[T]) ValueCtx(ctx context.Context) typ.Optional[T] {
	return s.value
}

func (s *StructConfig[T]) ValueICtx(ctx context.Context) interface{} {
	return s.ValueCtx(ctx).Get()
}

func (s *StructConfig[T]) valuePtr() *typ.Optional[T] {
	return &s.value
}

func (s *StructConfig[T]) Refresh(ctx context.Context, confKey string) (value interface{}) {
	if s.refreshFunc != nil {
		c, err := s.refreshFunc(ctx, confKey, s.defaultValue.Get())
		if err != nil {
			logger.LogErrorf("Unmarshal config failed|key=%s|err=%v", s.Key(), err.Error())
		} else {
			s.value.Set(c)
		}
		return s.value
	}
	return s.value
}

func (s *StructConfig[T]) WithRefreshFunc(f RefreshFunc[T]) {
	s.refreshFunc = f
}

func (s *StructConfig[T]) Update(newVal typ.Optional[T]) {
	s.value = newVal
}

type RefreshFunc[T any] func(ctx context.Context, key string, defaultValue T) (value T, err error)

func DefaultRefreshFunc[T any](ctx context.Context, key string, defaultValue T) (value T, err error) {
	if err = config.UnmarshalConfigWithContext(ctx, key, &value); err != nil {
		return value, err
	}
	return value, nil
}

func IntRefreshFunc[T ~int](ctx context.Context, key string, defaultValue T) (value T, err error) {
	c := config.GetIntWithContext(ctx, key, int(defaultValue))
	return T(c), nil
}

func Int64RefreshFunc[T ~int64](ctx context.Context, key string, defaultValue T) (value T, err error) {
	c := config.GetInt64WithContext(ctx, key, int64(defaultValue))
	return T(c), nil
}

func StringRefreshFunc[T typ.String](ctx context.Context, key string, defaultValue T) (value T, err error) {
	c := config.GetStringWithContext(ctx, key, string(defaultValue))
	return T(c), nil
}

func BoolRefreshFunc[T typ.Bool](ctx context.Context, key string, defaultValue T) (value T, err error) {
	c := config.GetBoolWithContext(ctx, key, bool(defaultValue))
	return T(c), nil
}

func Float64RefreshFunc[T ~float64](ctx context.Context, key string, defaultValue T) (value T, err error) {
	c := config.GetFloat64WithContext(ctx, key, float64(defaultValue))
	return T(c), nil
}

func JsonRefreshFunc[T any](ctx context.Context, key string, defaultValue T) (value T, err error) {
	s := config.GetStringWithContext(ctx, key, "")
	if err = sonic.UnmarshalString(s, &value); err != nil {
		return value, err
	}
	return value, nil
}

type ConfigOption[T any] struct {
	defaultValue typ.Optional[T]
	refreshFunc  RefreshFunc[T]
}

type ConfigOptions[T any] func(opt *ConfigOption[T])

func WithRefreshFunc[T any](f RefreshFunc[T]) ConfigOptions[T] {
	return func(c *ConfigOption[T]) {
		c.refreshFunc = f
	}
}

func WithDefaultValue[T any](defaultValue T) ConfigOptions[T] {
	return func(c *ConfigOption[T]) {
		c.defaultValue = typ.NewOptional(defaultValue)
	}
}
