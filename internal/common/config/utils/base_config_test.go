package utils

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/mocker"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

func testGetRefreshFunc[T any](newValue *T) RefreshFunc[T] {
	return func(ctx context.Context, key string, defaultValue T) (T, error) {
		if newValue == nil {
			return defaultValue, errors.New("newValue is nil")
		}
		return *newValue, nil
	}
}

type testStruct struct {
	A int     `json:"a"`
	B bool    `json:"b"`
	C string  `json:"c"`
	D float64 `json:"d"`
}

func Test_StructConfig(t *testing.T) {
	key := "test_ts"
	testingStructConfig(t, key, NewStructConfig[testStruct])
}

func Test_IntConfig(t *testing.T) {
	key := "test_int"
	testingStructConfig(t, key, NewIntConfig[int])
	type C int
	key = "test_int_t"
	testingStructConfig(t, key, NewIntConfig[C])
}

func Test_Int64Config(t *testing.T) {
	key := "test_int64"
	testingStructConfig(t, key, NewInt64Config[int64])
	type C int64
	key = "test_int64_t"
	testingStructConfig(t, key, NewInt64Config[C])
}

func Test_BoolConfig(t *testing.T) {
	key := "test_bool"
	testingStructConfig(t, key, NewBoolConfig[bool])
	type C bool
	key = "test_bool_t"
	testingStructConfig(t, key, NewBoolConfig[C])
}

func Test_StringConfig(t *testing.T) {
	key := "test_string"
	testingStructConfig(t, key, NewStringConfig[string])
	type C string
	key = "test_string_t"
	testingStructConfig(t, key, NewStringConfig[C])
}

func Test_Float64Config(t *testing.T) {
	key := "test_float64"
	testingStructConfig(t, key, NewFloat64Config[float64])
	type C float64
	key = "test_float64_t"
	testingStructConfig(t, key, NewFloat64Config[C])
}

type newConfigFunc[T any] func(key string, opts ...ConfigOptions[T]) *StructConfig[T]

func testingStructConfig[S comparable](t *testing.T, key string, cf newConfigFunc[S]) {
	ctx := context.Background()
	for i := 0; i < 20; i++ {
		var (
			source       = mocker.GenMockData[S]()
			refreshValue = mocker.GenMockData[S]()
		)

		var (
			refreshValueEqual = source == refreshValue
		)

		refreshFunc := testGetRefreshFunc[S](&refreshValue)
		s := cf(key, WithRefreshFunc(refreshFunc))
		equal(t, false, s.Value().Exists())
		s.Refresh(ctx, key)
		equal(t, true, s.Value().Exists())
		s.Update(typ.NewOptional(source))
		equal(t, true, s.Value().Exists())

		equal(t, source, s.Value().Get())

		s.Refresh(ctx, key)
		equal(t, refreshValue, s.Value().Get())
		equal(t, refreshValueEqual, source == s.Value().Get())

		mocker.UpdateMockData(&refreshValue)
		s.Refresh(ctx, key)
		equal(t, refreshValue, s.Value().Get())
	}
}

func equal[S comparable](t *testing.T, expect, actual S) {
	if assert.Equal(t, expect, actual) {
		return
	}
	fmt.Println("result is not equal")
}

func notEqual[S comparable](t *testing.T, expect, actual S) {
	if assert.NotEqual(t, expect, actual) {
		return
	}
	fmt.Println("result is equal")
}
