package server_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type ServerAccessor interface {
	GetReportDegradeConfig(ctx context.Context) ReportDegradeConfig

	Init(ctx context.Context) error
	utils.ConfManager
}

type ServerAccessorImpl struct {
	utils.ConfListener
}

func NewServerAccessorImpl() *ServerAccessorImpl {
	confListener := utils.NewConfListener(serverConfigPrefix, refreshConfigInterval, true)
	accessor := &ServerAccessorImpl{
		ConfListener: confListener,
	}
	return accessor
}

func (s *ServerAccessorImpl) GetReportDegradeConfig(ctx context.Context) ReportDegradeConfig {
	return reportDegradeConfig.ValueCtx(ctx).Get()
}

func (s *ServerAccessorImpl) Init(ctx context.Context) error {
	s.ConfListener.Register(reportDegradeConfig)
	return s.ConfListener.Init(ctx)
}
