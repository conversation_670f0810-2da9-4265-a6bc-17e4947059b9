package chassis_config

// 流量录制做为底层函数需要单独抽出来,避免循环依赖，其他配置获取依赖于流量录制

import (
	"sync/atomic"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
)

var (
	gRecorderEnable atomic.Bool
)

// IsRecordEnable 是否打开流量录制开关
func IsRecordEnable() bool {
	return gRecorderEnable.Load()
}

func refreshRecordEnable() {
	newVal := config.GetBool(config.CkPluginsRecorderEnable, false)
	if newVal {
		newVal = config.GetBool(config.CkPluginsRecorderForceCloseAllRecord, false)
	}
	gRecorderEnable.Store(newVal)
}

// DisableRecordForTest it is only used for self test
func DisableRecordForTest() {
	gRecorderEnable.Store(false)
}

// EnableRecordForTest it is only used for self test
func EnableRecordForTest() {
	gRecorderEnable.Store(true)
}
