package chassis_config

import (
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/event"
)

var (
	refreshInterval          = 5 * time.Minute
	gChassisConfigListener   = NewChassisConfigListener()
	gChassisConfigEnableOnce sync.Once
)

func InitChassisConfigListener() error {
	gChassisConfigEnableOnce.Do(func() {
		gChassisConfigListener.refresh()
	})
	gChassisConfigListener.regularlyRefresh()

	err := config.RegisterListener(gChassisConfigListener)
	if err != nil {
		return errors.WithMessage(err, "register server config listener err")
	}
	return nil
}

type ChassisConfigListener struct {
	ticket *time.Ticker
}

func NewChassisConfigListener() *ChassisConfigListener {
	return &ChassisConfigListener{
		ticket: time.NewTicker(refreshInterval),
	}
}

func (s *ChassisConfigListener) ConfPrefix() string {
	return config.ChassisConfigPrefix
}

func (s *ChassisConfigListener) Event(events []*event.Event) {
	for _, e := range events {
		if e.Key == replaceChassisKey(config.CkInitialLogLogLevel) {
			updateLogLevel()
		}
		if e.Key == replaceChassisKey(FastReturnInDebugLog) {
			updateFastReturnInDebugLog()
		}
		if e.Key == replaceChassisKey(config.CkInitialLogDisableShadowLog) {
			updateDisableShadowLog()
		}
		if e.Key == replaceChassisKey(config.CkPluginsRecorderEnable) ||
			e.Key == replaceChassisKey(config.CkPluginsRecorderForceCloseAllRecord) {
			updateLogLevel()
		}
	}
}

func (s *ChassisConfigListener) regularlyRefresh() {
	go func() {
		defer s.ticket.Stop()
		for range s.ticket.C {
			s.refresh()
		}
	}()
}

func (s *ChassisConfigListener) refresh() {
	updateLogLevel()
	updateFastReturnInDebugLog()
	updateDisableShadowLog()
	refreshRecordEnable()
}

func replaceChassisKey(key string) string {
	return strings.ReplaceAll(key, "$CHASSIS", config.ChassisConfigPrefix)
}
