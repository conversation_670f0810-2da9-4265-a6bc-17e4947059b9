package chassis_config

import (
	"sync/atomic"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

var (
	// gLogLevel can only be updated by updateLogLevel
	gLogLevel int64
	// gFastReturnInDebugLog 由于组件提供的CtxLogDebugf中IsShadow方法性能消耗较大，在组件优化之前，业务团队进行快速返回
	gFastReturnInDebugLog int64
	// gDisableShadowLog
	gDisableShadowLog atomic.Bool
)

const fastReturnInDebugLogFlag = 1 // debug日志快速返回标记, 1代表打开，其他代表关闭(一般为0)\
const FastReturnInDebugLog = "$CHASSIS.fastReturnInDebugLog"

func IsInfoEnabled() bool {
	return gLogLevel >= logger.LogLevelInfo
}

func IsShadowLogEnabled() bool {
	return gDisableShadowLog.Load()
}

func IsFastReturnInDebugLog() bool {
	// 如果日志级别不为debug，那么根据配置决定是否快速返回，默认快速返回提升系统性能
	if !logger.IsDebugEnabled() {
		return gFastReturnInDebugLog == fastReturnInDebugLogFlag
	}
	return false
}

// updateLogLevel updates log level
func updateLogLevel() {
	newLevel := config.GetInt64(config.CkInitialLogLogLevel, 1)
	atomic.StoreInt64(&gLogLevel, newLevel)
}

// updateDisableShadowLog update key to fast return in debug log
func updateDisableShadowLog() {
	newVal := config.GetBool(config.CkInitialLogDisableShadowLog, false) // 默认打开
	gDisableShadowLog.Store(newVal)
}

// updateFastReturnInDebugLog update key to fast return in debug log
func updateFastReturnInDebugLog() {
	newVal := config.GetInt64(FastReturnInDebugLog, 1) // 默认打开
	atomic.StoreInt64(&gFastReturnInDebugLog, newVal)
}
