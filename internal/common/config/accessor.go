package config

// 将 config 合入一个接口，这样只需要对 confAccessor mock

//go:generate mockgen --build_flags=--mod=mod -source=accessor.go -destination=accessor_mock.go -package=config -mock_names=ConfAccessor=MockConfAccessor

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/datasource_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type ConfAccessor interface {
	application_config.ApplicationAccessor
	business_config.BusinessAccessor
	mutable_application_config.MutableApplicationAccessor
	server_config.ServerAccessor
	datasource_config.DatasourceAccessor

	Init(ctx context.Context) error
	utils.ConfManager
}

type ConfAccessorImpl struct {
	application_config.ApplicationAccessor
	business_config.BusinessAccessor
	mutable_application_config.MutableApplicationAccessor
	server_config.ServerAccessor
	datasource_config.DatasourceAccessor
}

func (c *ConfAccessorImpl) Init(ctx context.Context) error {
	if err := c.ApplicationAccessor.Init(ctx); err != nil {
		return err
	}
	if err := c.BusinessAccessor.Init(ctx); err != nil {
		return err
	}
	if err := c.MutableApplicationAccessor.Init(ctx); err != nil {
		return err
	}
	if err := c.ServerAccessor.Init(ctx); err != nil {
		return err
	}
	if err := c.DatasourceAccessor.Init(ctx); err != nil {
		return err
	}
	return nil
}

func (c *ConfAccessorImpl) Keys() []string {
	if c == nil {
		return make([]string, 0)
	}
	keys := make([]string, 0)
	for _, conf := range []utils.ConfManager{c.ApplicationAccessor, c.MutableApplicationAccessor, c.BusinessAccessor, c.ServerAccessor} {
		keys = append(keys, conf.Keys()...)
	}
	return keys
}

func (c *ConfAccessorImpl) ValueICtx(ctx context.Context, key string) (interface{}, bool) {
	if c == nil {
		return nil, false
	}
	for _, conf := range []utils.ConfManager{c.ApplicationAccessor, c.MutableApplicationAccessor, c.BusinessAccessor, c.ServerAccessor} {
		val, ok := conf.ValueICtx(ctx, key)
		if ok {
			return val, true
		}
	}
	return nil, false
}

func (c *ConfAccessorImpl) All(ctx context.Context) map[string]interface{} {
	if c == nil {
		return make(map[string]interface{})
	}
	ret := make(map[string]interface{})
	for _, conf := range []utils.ConfManager{c.ApplicationAccessor, c.MutableApplicationAccessor, c.BusinessAccessor, c.ServerAccessor} {
		for k, v := range conf.All(ctx) {
			ret[k] = v
		}
	}
	return ret
}

func NewConfAccessorImpl(
	applicationAccessor application_config.ApplicationAccessor,
	businessAccessor business_config.BusinessAccessor,
	mutableApplicationAccessor mutable_application_config.MutableApplicationAccessor,
	serverAccessor server_config.ServerAccessor,
	datasourceAccessor datasource_config.DatasourceAccessor,
) *ConfAccessorImpl {
	return &ConfAccessorImpl{
		ApplicationAccessor:        applicationAccessor,
		BusinessAccessor:           businessAccessor,
		MutableApplicationAccessor: mutableApplicationAccessor,
		ServerAccessor:             serverAccessor,
		DatasourceAccessor:         datasourceAccessor,
	}
}
