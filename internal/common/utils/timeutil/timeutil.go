package timeutil

import (
	"context"
	"log"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

var countryToLocationZoneMap = map[string]string{
	"ID": IDLocationZone,
	"TH": THLocationZone,
	"VN": VNLocationZone,
	"MY": MYLocationZone,
	"PH": PHLocationZone,
	"SG": SGLocationZone,
	"TW": TWLocationZone,
	"BR": BRLocationZone,
	"MX": MXLocationZone,
	"CO": COLocationZone,
	"CL": CLLocationZone,
	"AR": ARLocationZone,
	"PL": PLLocationZone,
	"ES": ESLocationZone,
	"FR": FRLocationZone,
	"IN": INLocationZone,
	"CN": CNLocationZone,
	"KR": KRLocationZone,
	"JP": JPLocationZone,
}

const (
	OneDay            = "24h"
	DefaultTimeFormat = "2006-01-02 15:04:05"
	DateFormat        = "2006-01-02"
)

const NanoToMilli = 1e6

var (
	timeZone = make(map[string]*time.Location, len(countryToLocationZoneMap))
	// timeOffset 每一个市场相对于 UTC 时间的偏移秒数, 例如 UTC +8 偏移秒数是 8 * 3600
	timeOffset = make(map[string]int64, len(countryToLocationZoneMap))
)

func init() {
	bjLoc, _ := time.LoadLocation("Asia/Shanghai")
	utcTimeUnix := time.Date(0, 0, 0, 0, 0, 0, 0, time.UTC).Unix()
	for cid, zone := range countryToLocationZoneMap {
		tz, err := time.LoadLocation(zone)
		if err != nil {
			_ = monitor.ReportEvent("InitTimeZone", cid, monitor.StatusError, err.Error())
			tz = bjLoc
		}
		timeZone[cid] = tz
		tzTimeUnix := time.Date(0, 0, 0, 0, 0, 0, 0, tz).Unix()
		timeOffset[cid] = utcTimeUnix - tzTimeUnix
	}
}

func GetCurrentUnixTimeStamp(ctx context.Context) uint32 {
	return uint32(Now(ctx).Unix())
}

func GetCurrentUnixMilliTimeStamp(ctx context.Context) int64 {
	return Now(ctx).UnixNano() / NanoToMilli
}

func GetCurrentUnixNanoTimeStamp(ctx context.Context) int64 {
	return Now(ctx).UnixNano()
}

func GetLocalTime(ctx context.Context) time.Time {
	return Now(ctx).In(GetTimeLocationZone(ctx))
}

func GetNextLocalTime(ctx context.Context, next int) time.Time {
	return Now(ctx).In(GetTimeLocationZone(ctx)).AddDate(0, 0, next)
}

func GetYesterdayTimeStamp(ctx context.Context) int64 {
	t := Now(ctx)
	yesTime := t.AddDate(0, 0, -1)
	return yesTime.Unix()
}

func LogTime(name string, s time.Time) {
	Logger.LogInfof("%s done, duration=%.3fs", name, time.Since(s).Seconds())
}

type DurationPrinter struct {
	beginTime time.Time
	name      string
}

func NewDurationPrinter(ctx context.Context, name string) *DurationPrinter {
	log.Println(name, "begin.")
	return &DurationPrinter{
		name:      name,
		beginTime: Now(ctx),
	}
}

func (l *DurationPrinter) PrintDuration() {
	log.Printf("%s done, duration=%.3fs\n", l.name, time.Since(l.beginTime).Seconds())
}

func ParseLocalTime(ctx context.Context, layout string, value string) (time.Time, error) {
	return time.ParseInLocation(layout, value, GetTimeLocationZone(ctx))
}

func GetYesterdayStartTime(ctx context.Context) int64 {
	t := Now(ctx)
	yesTime := t.AddDate(0, 0, -1)
	timeYesterday := time.Date(yesTime.Year(), yesTime.Month(), yesTime.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return timeYesterday.Unix()
}

func GetYesterdayLocalStartTime(ctx context.Context) int64 {
	t := GetLocalTime(ctx)
	yesTime := t.AddDate(0, 0, -1)
	timeYesterday := time.Date(yesTime.Year(), yesTime.Month(), yesTime.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return timeYesterday.Unix()
}

func GetYesterdayEndTime(ctx context.Context) int64 {
	t := Now(ctx)
	yesTime := t.AddDate(0, 0, -1)
	timeYesterday := time.Date(yesTime.Year(), yesTime.Month(), yesTime.Day(), 23, 59, 59, 59, GetTimeLocationZone(ctx))
	return timeYesterday.Unix()
}

func GetYesterdayLocalEndTime(ctx context.Context) int64 {
	t := GetLocalTime(ctx)
	yesTime := t.AddDate(0, 0, -1)
	timeYesterday := time.Date(yesTime.Year(), yesTime.Month(), yesTime.Day(), 23, 59, 59, 59, GetTimeLocationZone(ctx))
	return timeYesterday.Unix()
}

func ConvertTimeStampToTimeByCountry(timestamp int64, country string) time.Time {
	return time.Unix(timestamp, 0).In(timeZone[country])
}

func ConvertTimeStampToTime(ctx context.Context, timestamp int64) time.Time {
	return ConvertTimeStampToTimeByCountry(timestamp, envvar.GetCID(ctx))
}

func ConvertTimeStampToLocalTime(ctx context.Context, timestamp int64) time.Time {
	return time.Unix(timestamp, 0).In(GetTimeLocationZone(ctx))
}

func GetTodayDateString(ctx context.Context) string {
	return ConvertTimeStampToTimeByCountry(Now(ctx).Unix(), envvar.GetCID(ctx)).Format(TimeLayout)
}

func GetTimeLocationZone(ctx context.Context) *time.Location {
	return timeZone[envvar.GetCID(ctx)]
}

func GetTimeLocationZoneByRegion(region string) *time.Location {
	return timeZone[region]
}

// GetDays 通过给定的起止时间来获得这段区间内的time.Time列表 -> [start, end]
func GetDays(start, end time.Time) (results []time.Time) {
	for cur := start; !cur.After(end); cur = cur.Add(time.Hour * 24) {
		results = append(results, cur)
	}
	return results
}

// GetNextDayStartTime
// @param: nextDay -> e.g. nextDay is -1, returns yesterday's start time; nextDay is 1 return tomorrow's start time
func GetNextDayStartTime(ctx context.Context, nextDay int) int64 {
	t := Now(ctx)
	nextTime := t.AddDate(0, 0, nextDay)
	nextDate := time.Date(nextTime.Year(), nextTime.Month(), nextTime.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return nextDate.Unix()
}

func GetTodayStartTime(ctx context.Context) int64 {
	t := Now(ctx)
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return today.Unix()
}

func GetStartTimeByString(ctx context.Context, value string) int64 {
	t, _ := ParseLocalTime(ctx, "2006-01-02", value)
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return today.Unix()
}

func GetStartTimeByTime(ctx context.Context, t time.Time) int64 {
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return today.Unix()
}

// GetNextDayStartTimeOfUTC :returns Unix() of UTC time
func GetNextDayStartTimeOfUTC(ctx context.Context, nextDay int) int64 {
	t := Now(ctx)
	nextTime := t.AddDate(0, 0, nextDay)
	nextDate := time.Date(nextTime.Year(), nextTime.Month(), nextTime.Day(), 0, 0, 0, 0, time.UTC)
	return nextDate.Unix()
}

func GetNanoStartTime(ctx context.Context, t time.Time) int64 {
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return today.UnixNano()
}

func GetStartTime(ctx context.Context, t time.Time) int64 {
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone(ctx))
	return today.Unix()
}

func FormatTimestamp(sec uint32, format string) string {
	if sec == 0 {
		return ""
	}
	return time.Unix(int64(sec), 0).Format(format)
}

func FormatDate(dt time.Time) string {
	return dt.Format(DateFormat)
}

func FormatDateTime(dt time.Time) string {
	return dt.Format(DefaultTimeFormat)
}

func Now(ctx context.Context) time.Time {
	if !chassis_config.IsRecordEnable() {
		return time.Now()
	}
	return recorder.Now(ctx)
}
