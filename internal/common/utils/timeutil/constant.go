package timeutil

const (
	IDLocationZone = "Asia/Jakarta"
	VNLocationZone = "Asia/Ho_Chi_Minh"
	THLocationZone = "Asia/Bangkok"
	MYLocationZone = "Asia/Kuala_Lumpur"
	PHLocationZone = "Asia/Manila"
	SGLocationZone = "Asia/Singapore"
	TWLocationZone = "Asia/Taipei"
	// BRLocationZone 巴西夏令时 (DST) 的最近变动的时区数据, 南美洲（圣保罗）区域 – 将时区 由America/Sao_Paulo 设置为 America/Fortaleza
	// ref: https://docs.aws.amazon.com/zh_cn/AmazonRDS/latest/AuroraUserGuide/Concepts.RegionsAndAvailabilityZones.html
	BRLocationZone = "America/Fortaleza"
	MXLocationZone = "America/Mexico_City"
	COLocationZone = "America/Bogota"
	CLLocationZone = "America/Santiago"
	ARLocationZone = "America/Argentina"
	PLLocationZone = "Europe/Warsaw"
	ESLocationZone = "Europe/Madrid"
	FRLocationZone = "Europe/Paris"
	INLocationZone = "Asia/Kolkata"
	CNLocationZone = "Asia/Shanghai"
	KRLocationZone = "Asia/Seoul"
	JPLocationZone = "Asia/Tokyo"
)

const TimeLayout = "20060102"

const (
	SecondPerDay = 24 * 60 * 60
)
