package migration

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/randutil"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

const (
	CompareModuleName = "CompareModule"
	SwitchModuleName  = "SwitchModule"
)

type CompareMethod func(oldVal interface{}, newVal interface{}) bool
type CompareMethodWithMsg func(oldVal interface{}, newVal interface{}) (string, bool)

// IsCompare 对比万分比灰度
func IsCompare(ctx context.Context, accessor config.ConfAccessor, key string) bool {
	if len(accessor.GetMigrationConfig(ctx).CompareConfig) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, CompareModuleName, key, monitor.StatusError, "key not found")
		Logger.CtxLogDebugf(ctx, "%s|key=%s not found", CompareModuleName, key)
		return false
	}
	val, ok := accessor.GetMigrationConfig(ctx).CompareConfig[key]
	if !ok {
		_ = monitor.AwesomeReportEvent(ctx, CompareModuleName, key, monitor.StatusError, "key not found")
		Logger.CtxLogDebugf(ctx, "%s|key=%s not found", CompareModuleName, key)
		return false
	}
	// 如果万分比全开，直接返回true，提升性能
	if val == randutil.MaxGreyPercentage {
		return true
	}
	// 如果万分比全关，直接返回false，提升性能
	if val == randutil.MinGreyPercentage {
		return false
	}
	// 命中灰度 - 同个request id的结果将一致
	hit := common.HitRatioByRequestId(ctx, val)
	Logger.CtxLogDebugf(ctx, "%s|key=%s|grey=%v", CompareModuleName, key, hit)
	return hit
}

// IsSwitch 切换万分比灰度
func IsSwitch(ctx context.Context, accessor config.ConfAccessor, key string) bool {
	if len(accessor.GetMigrationConfig(ctx).SwitchConfig) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, SwitchModuleName, key, monitor.StatusError, "key not found")
		Logger.CtxLogDebugf(ctx, "%s|key=%s not found", SwitchModuleName, key)
		return false
	}
	val, ok := accessor.GetMigrationConfig(ctx).SwitchConfig[key]
	if !ok {
		_ = monitor.AwesomeReportEvent(ctx, SwitchModuleName, key, monitor.StatusError, "key not found")
		Logger.CtxLogDebugf(ctx, "%s|key=%s not found", SwitchModuleName, key)
		return false
	}
	// 如果万分比全开，直接返回true，提升性能
	if val == randutil.MaxGreyPercentage {
		return true
	}
	// 如果万分比全关，直接返回false，提升性能
	if val == randutil.MinGreyPercentage {
		return false
	}
	// 命中灰度 - 同个request id的结果将一致
	hit := common.HitRatioByRequestId(ctx, val)
	Logger.CtxLogDebugf(ctx, "%s|key=%s|grey=%v", SwitchModuleName, key, hit)
	return hit
}

// CommonCompareReport
// CatModuleName, CatInterfaceName 上报到CAT的Module和Interface，分两级上报(可只选一级，另外传递"")
// oldVal, newVal 新老数据
// HowToCompare 对比方法
func CommonCompareReport(ctx context.Context, CatModuleName string, CatInterfaceName string, oldVal interface{}, newVal interface{}, HowToCompare CompareMethod) {
	defer func() {
		if r := recover(); r != nil {
			_ = monitor.AwesomeReportEvent(ctx, CatModuleName, CatInterfaceName, monitor.StatusPanic, fmt.Sprintf("old=%v|new=%v", oldVal, newVal))
			Logger.CtxLogErrorf(ctx, "moduleName=%s|interfaceName=%s|old=%v|new=%v", CatModuleName, CatInterfaceName, oldVal, newVal)
		}
	}()
	// 开始对比
	oldString, newString := Logger.JsonString(oldVal), Logger.JsonString(newVal)
	// 记录对比状态及对比数据
	var status string
	isSame := HowToCompare(oldVal, newVal)
	if isSame {
		status = monitor.StatusSuccess
		Logger.CtxLogDebugf(ctx, "moduleName=%s|interfaceName=%s|old=%s|new=%s", CatModuleName, CatInterfaceName, oldString, newString)
	} else {
		status = monitor.StatusError
		Logger.CtxLogErrorf(ctx, "moduleName=%s|interfaceName=%s|old=%s|new=%s", CatModuleName, CatInterfaceName, oldString, newString)
	}
	// CAT 上报
	_ = monitor.AwesomeReportEvent(ctx, CatModuleName, CatInterfaceName, status, fmt.Sprintf("old=%s|new=%s", oldString, newString))
}
