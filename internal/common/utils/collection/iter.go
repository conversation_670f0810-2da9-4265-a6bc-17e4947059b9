package collection

type Iterable[T any] interface {
	HasNext() bool
	Next() (T, bool)
}

type ArrayIterator[T any] struct {
	index int
	items []T
}

func NewArrayIterator[T any](items []T) *ArrayIterator[T] {
	return &ArrayIterator[T]{
		index: 0,
		items: items,
	}
}

func (it *ArrayIterator[T]) HasNext() bool {
	return len(it.items) > it.index
}

func (it *ArrayIterator[T]) Next() (T, bool) {
	if !it.HasNext() {
		var zero T
		return zero, false
	}
	value := it.items[it.index]
	it.index++
	return value, true
}

func MapRange[M ~map[K]V, K comparable, V any](m M, rangeFn func(key K, value V) (stopped bool)) {
	for key, value := range m {
		if !rangeFn(key, value) {
			break
		}
	}
}

func SliceRange[S ~[]T, T any](slice S, rangeFn func(index int, item T) (stopped bool)) {
	for index, item := range slice {
		if !rangeFn(index, item) {
			break
		}
	}
}
