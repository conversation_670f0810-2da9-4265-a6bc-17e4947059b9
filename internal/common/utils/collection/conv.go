package collection

func MapValues[M ~map[K]V, K comparable, V any](m M) []V {
	slice := make([]V, 0, len(m))
	for _, v := range m {
		slice = append(slice, v)
	}
	return slice
}

func MapKeys[M ~map[K]V, K comparable, V any](m M) []K {
	slice := make([]K, 0, len(m))
	for k := range m {
		slice = append(slice, k)
	}
	return slice
}

func SliceToSet[S ~[]K, K comparable](slice S) Set[K] {
	set := NewSet[K]()
	SliceRange(slice, func(index int, item K) bool {
		set.Add(item)
		return false
	})
	return set
}

func SliceToMap[K comparable, V any](slice []V, keyFunc func(v V) K) map[K]V {
	m := make(map[K]V)
	for _, v := range slice {
		m[keyFunc(v)] = v
	}
	return m
}

func MapToSlice[K comparable, V any](m map[K]V) []V {
	slice := make([]V, 0, len(m))
	for _, v := range m {
		slice = append(slice, v)
	}
	return slice
}

func NewSetFromSlice[T comparable](s []T) Set[T] {
	set := NewSet[T]()
	for _, v := range s {
		set.Add(v)
	}
	return set
}

func IntersectSets[T comparable](sets []Set[T]) Set[T] {
	var intersect Set[T]
	if len(sets) == 0 {
		return NewSet[T]()
	}
	for _, s := range sets {
		if intersect == nil {
			intersect = s
		} else {
			intersect = intersect.Intersection(s)
		}
	}
	return intersect
}
