package randutil

import (
	"context"
	"math/rand"
	"sync"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
)

var randPool = sync.Pool{
	New: func() interface{} {
		return recorder.NewRand(rand.NewSource(recorder.Now(context.Background()).UnixNano()))
	},
}

// getRand 获取 rand 计数器, 最终需要通过 defer PutRand 回收
func getRand() *recorder.Rand {
	return randPool.Get().(*recorder.Rand)
}

// putRand 回收 rand
func putRand(r *recorder.Rand) {
	randPool.Put(r)
}

func RandInt(ctx context.Context) int {
	r := getRand()
	defer putRand(r)
	if !chassis_config.IsRecordEnable() {
		return r.Rand.Int()
	}
	return r.WithContext(ctx).Int()
}

func RandInt63(ctx context.Context) int64 {
	r := getRand()
	defer putRand(r)
	if !chassis_config.IsRecordEnable() {
		return r.Rand.Int63()
	}
	return r.WithContext(ctx).Int63()
}

func RandFloat64(ctx context.Context) float64 {
	r := getRand()
	defer putRand(r)
	if !chassis_config.IsRecordEnable() {
		return r.Rand.Float64()
	}
	return r.WithContext(ctx).Float64()
}

func RandIntN(ctx context.Context, n int) int {
	r := getRand()
	defer putRand(r)
	if !chassis_config.IsRecordEnable() {
		return r.Rand.Intn(n)
	}
	return r.WithContext(ctx).Intn(n)
}

func RandInt63N(ctx context.Context, n int64) int64 {
	r := getRand()
	defer putRand(r)
	if !chassis_config.IsRecordEnable() {
		return r.Rand.Int63n(n)
	}
	return r.WithContext(ctx).Int63n(n)
}
