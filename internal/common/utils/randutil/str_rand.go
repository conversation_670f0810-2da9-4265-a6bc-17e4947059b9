package randutil

import (
	"context"
	"math"
	"strings"

	"github.com/howeyc/crc16"
)

const (
	MinGreyPercentage        int = 0
	MaxGreyHundredPercentage int = 100
	MaxGreyPercentage        int = 10000
)

// RandByString 根据字符串计算随机数，输入内容一样，输出一样，算法：CRC16，区间：[0, 10000)
//
//	@param str 字符串
//	@return 10000内的随机数，0 ~ 10000
//	<AUTHOR> <PERSON> | SLS BE | <EMAIL>
func RandByString(str string) int {
	// 因为crc16.Checksum返回uint16，范围为0 ~ 65535（math.MaxUint16）
	checkSum := float64(crc16.Checksum([]byte(str), crc16.IBMTable))

	// 需要将数值压缩至0 ~ 10000，然后取余，由于math.MaxUint16=65535，直接取余分布不均匀
	return int(checkSum/float64(math.MaxUint16)*float64(MaxGreyPercentage)) % MaxGreyPercentage
}

// RandByStringHundred 根据字符串计算随机数，输入内容一样，输出一样，算法：CRC16，区间：[0, 10000)
//
//	@param str 字符串
//	@return 10000内的随机数，0 ~ 100
//	<AUTHOR> WenLong | SLS BE | <EMAIL>
func RandByStringHundred(str string) int {
	// 因为crc16.Checksum返回uint16，范围为0 ~ 65535（math.MaxUint16）
	checkSum := float64(crc16.Checksum([]byte(str), crc16.IBMTable))

	// 需要将数值压缩至0 ~ 10000，然后取余，由于math.MaxUint16=65535，直接取余分布不均匀
	return int(checkSum/float64(math.MaxUint16)*float64(MaxGreyHundredPercentage)) % MaxGreyHundredPercentage
}

const (
	alphaNumBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
)

func RandString(ctx context.Context, n int) string {
	sb := strings.Builder{}
	sb.Grow(n)
	for i := 0; i < n; i++ {
		sb.WriteByte(alphaNumBytes[RandInt63(ctx)%int64(len(alphaNumBytes))])
	}
	return sb.String()
}
