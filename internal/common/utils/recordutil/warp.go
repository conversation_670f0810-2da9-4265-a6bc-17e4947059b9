package recordutil

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
)

// Wrap F 函数 返回一个支持流量录制的函数
func Wrap[F any](f F) F {
	return recorder.Wrap(f).(F)
}

func RegisterGlobalVar[T any](name string, model *T) func(ctx context.Context) *T {
	f := recorder.RegisterGlobalVar(name, model)
	return func(ctx context.Context) (v *T) {
		defer func() {
			if r := recover(); r != nil {
				v = nil
			}
		}()
		var ok bool
		v, ok = f(ctx).(*T)
		if !ok {
			return nil
		}
		return v
	}
}
