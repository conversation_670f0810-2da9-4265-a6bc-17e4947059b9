package concurrency

import (
	"fmt"
	"sync"
)

type Task[T any] func() (T, error)

type ConcurrencyTaskGroup[T any] struct {
	tasks []Task[T]
}

func NewConcurrencyTaskGroup[T any]() *ConcurrencyTaskGroup[T] {
	return &ConcurrencyTaskGroup[T]{}
}

func (t *ConcurrencyTaskGroup[T]) Add(task Task[T]) {
	t.tasks = append(t.tasks, task)
}

func (t *ConcurrencyTaskGroup[T]) Run() ([]T, error) {
	if len(t.tasks) == 0 {
		return nil, nil
	}

	outcomes := make([]T, 0, len(t.tasks))
	if len(t.tasks) == 1 {
		var outcome T
		var err error

		func() {
			defer func() {
				if r := recover(); r != nil {
					if recoverErr, ok := r.(error); ok {
						err = recoverErr
					} else {
						err = fmt.Errorf("panic: %v", r)
					}
				}
			}()
			outcome, err = t.tasks[0]()
		}()

		if err != nil {
			return nil, err
		}
		outcomes = append(outcomes, outcome)
		return outcomes, nil
	}

	outcomeCh := make(chan T, len(t.tasks))
	errCh := make(chan error, len(t.tasks))
	wg := &sync.WaitGroup{}
	wg.Add(len(t.tasks))

	for _, task := range t.tasks {
		go func(task Task[T]) {
			defer func() {
				if r := recover(); r != nil {
					if err, ok := r.(error); ok {
						errCh <- err
					} else {
						errCh <- fmt.Errorf("panic: %v", r)
					}
				}
				wg.Done()
			}()

			outcome, err := task()
			if err != nil {
				errCh <- err
				return
			}
			outcomeCh <- outcome
		}(task)
	}
	wg.Wait()
	close(outcomeCh)
	close(errCh)

	if len(errCh) > 0 {
		err := <-errCh
		return nil, err
	}
	for outcome := range outcomeCh {
		outcomes = append(outcomes, outcome)
	}
	return outcomes, nil
}
