package concurrency

import (
	"errors"
	"runtime"
	"strconv"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewConcurrencyTaskGroup(t *testing.T) {
	t.Run("整数类型", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()
		assert.NotNil(t, tg)
		assert.Empty(t, tg.tasks)
	})

	t.Run("字符串类型", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[string]()
		assert.NotNil(t, tg)
		assert.Empty(t, tg.tasks)
	})

	t.Run("结构体类型", func(t *testing.T) {
		type TestStruct struct {
			ID int
		}
		tg := NewConcurrencyTaskGroup[TestStruct]()
		assert.NotNil(t, tg)
		assert.Empty(t, tg.tasks)
	})
}

func TestConcurrencyTaskGroup_Add(t *testing.T) {
	tg := NewConcurrencyTaskGroup[int]()

	// 添加第一个任务
	task1 := func() (int, error) {
		return 1, nil
	}
	tg.Add(task1)
	assert.Len(t, tg.tasks, 1)

	// 添加第二个任务
	task2 := func() (int, error) {
		return 2, nil
	}
	tg.Add(task2)
	assert.Len(t, tg.tasks, 2)

	// 添加第三个任务
	task3 := func() (int, error) {
		return 3, nil
	}
	tg.Add(task3)
	assert.Len(t, tg.tasks, 3)
}

func TestConcurrencyTaskGroup_Run_Empty(t *testing.T) {
	tg := NewConcurrencyTaskGroup[int]()

	results, err := tg.Run()
	assert.NoError(t, err)
	assert.Nil(t, results)
}

func TestConcurrencyTaskGroup_Run_SingleTask(t *testing.T) {
	t.Run("成功的单任务", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()
		tg.Add(func() (int, error) {
			return 42, nil
		})

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 1)
		assert.Equal(t, 42, results[0])
	})

	t.Run("失败的单任务", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()
		tg.Add(func() (int, error) {
			return 0, errors.New("单任务失败")
		})

		results, err := tg.Run()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "单任务失败", err.Error())
	})
}

func TestConcurrencyTaskGroup_Run_MultipleTasks(t *testing.T) {
	t.Run("多个成功任务", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		// 添加多个任务
		for i := 1; i <= 5; i++ {
			value := i
			tg.Add(func() (int, error) {
				return value, nil
			})
		}

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 5)

		// 验证结果包含所有值（顺序可能不同）
		resultSet := make(map[int]bool)
		for _, result := range results {
			resultSet[result] = true
		}

		for i := 1; i <= 5; i++ {
			assert.True(t, resultSet[i], "结果中缺少值: %d", i)
		}
	})

	t.Run("其中一个任务失败", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		// 添加成功的任务
		tg.Add(func() (int, error) {
			return 1, nil
		})

		// 添加失败的任务
		tg.Add(func() (int, error) {
			return 0, errors.New("任务失败")
		})

		// 添加另一个成功的任务
		tg.Add(func() (int, error) {
			return 3, nil
		})

		results, err := tg.Run()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "任务失败", err.Error())
	})
}

func TestConcurrencyTaskGroup_Run_PanicHandling(t *testing.T) {
	t.Run("任务panic - 错误类型", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		tg.Add(func() (int, error) {
			panic(errors.New("panic错误"))
		})

		results, err := tg.Run()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "panic错误", err.Error())
	})

	t.Run("任务panic - 字符串类型", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		tg.Add(func() (int, error) {
			panic("字符串panic")
		})

		results, err := tg.Run()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "panic: 字符串panic", err.Error())
	})

	t.Run("任务panic - 数字类型", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		tg.Add(func() (int, error) {
			panic(42)
		})

		results, err := tg.Run()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "panic: 42", err.Error())
	})

	t.Run("多个任务其中一个panic", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		// 正常任务
		tg.Add(func() (int, error) {
			return 1, nil
		})

		// panic任务
		tg.Add(func() (int, error) {
			panic("panic了")
		})

		// 另一个正常任务
		tg.Add(func() (int, error) {
			return 3, nil
		})

		results, err := tg.Run()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "panic: panic了", err.Error())
	})
}

func TestConcurrencyTaskGroup_Run_Concurrency(t *testing.T) {
	t.Run("并发执行验证", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		var counter int64
		var wg sync.WaitGroup

		// 添加多个任务，每个任务都会增加计数器
		for i := 0; i < 10; i++ {
			wg.Add(1)
			tg.Add(func() (int, error) {
				defer wg.Done()
				// 模拟一些工作
				time.Sleep(10 * time.Millisecond)
				atomic.AddInt64(&counter, 1)
				return int(atomic.LoadInt64(&counter)), nil
			})
		}

		start := time.Now()
		results, err := tg.Run()
		duration := time.Since(start)

		assert.NoError(t, err)
		assert.Len(t, results, 10)
		assert.Equal(t, int64(10), atomic.LoadInt64(&counter))

		// 如果是并发执行，应该比串行执行快很多
		// 串行执行需要至少 100ms，并发执行应该接近 10ms
		assert.Less(t, duration, 50*time.Millisecond, "并发执行应该更快")
	})

	t.Run("并发安全测试", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		var sharedCounter int64

		// 添加大量任务，每个任务都会操作共享变量
		for i := 0; i < 100; i++ {
			tg.Add(func() (int, error) {
				// 安全地增加计数器
				return int(atomic.AddInt64(&sharedCounter, 1)), nil
			})
		}

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 100)
		assert.Equal(t, int64(100), atomic.LoadInt64(&sharedCounter))
	})
}

func TestConcurrencyTaskGroup_Run_DifferentTypes(t *testing.T) {
	t.Run("字符串类型", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[string]()

		tg.Add(func() (string, error) {
			return "hello", nil
		})

		tg.Add(func() (string, error) {
			return "world", nil
		})

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 2)

		// 验证结果包含两个字符串
		resultSet := make(map[string]bool)
		for _, result := range results {
			resultSet[result] = true
		}

		assert.True(t, resultSet["hello"])
		assert.True(t, resultSet["world"])
	})

	t.Run("结构体类型", func(t *testing.T) {
		type TestStruct struct {
			ID   int
			Name string
		}

		tg := NewConcurrencyTaskGroup[TestStruct]()

		tg.Add(func() (TestStruct, error) {
			return TestStruct{ID: 1, Name: "test1"}, nil
		})

		tg.Add(func() (TestStruct, error) {
			return TestStruct{ID: 2, Name: "test2"}, nil
		})

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 2)

		// 验证结果
		idSet := make(map[int]bool)
		for _, result := range results {
			idSet[result.ID] = true
		}

		assert.True(t, idSet[1])
		assert.True(t, idSet[2])
	})
}

func TestConcurrencyTaskGroup_Run_Performance(t *testing.T) {
	t.Run("大量任务性能测试", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		// 添加大量任务
		taskCount := 1000
		for i := 0; i < taskCount; i++ {
			value := i
			tg.Add(func() (int, error) {
				// 模拟少量工作
				time.Sleep(time.Microsecond)
				return value, nil
			})
		}

		start := time.Now()
		results, err := tg.Run()
		duration := time.Since(start)

		assert.NoError(t, err)
		assert.Len(t, results, taskCount)

		// 验证结果完整性
		resultSet := make(map[int]bool)
		for _, result := range results {
			resultSet[result] = true
		}

		for i := 0; i < taskCount; i++ {
			assert.True(t, resultSet[i], "结果中缺少值: %d", i)
		}

		t.Logf("执行 %d 个任务耗时: %v", taskCount, duration)
	})
}

func TestConcurrencyTaskGroup_Run_EdgeCases(t *testing.T) {
	t.Run("任务返回零值", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		tg.Add(func() (int, error) {
			return 0, nil
		})

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 1)
		assert.Equal(t, 0, results[0])
	})

	t.Run("任务返回空字符串", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[string]()

		tg.Add(func() (string, error) {
			return "", nil
		})

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 1)
		assert.Equal(t, "", results[0])
	})

	t.Run("任务返回nil指针", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[*int]()

		tg.Add(func() (*int, error) {
			return nil, nil
		})

		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 1)
		assert.Nil(t, results[0])
	})
}

func TestConcurrencyTaskGroup_Run_ErrorPriority(t *testing.T) {
	t.Run("错误优先于结果", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		// 添加一个快速成功的任务
		tg.Add(func() (int, error) {
			return 1, nil
		})

		// 添加一个快速失败的任务
		tg.Add(func() (int, error) {
			return 0, errors.New("快速失败")
		})

		// 添加一个慢速成功的任务
		tg.Add(func() (int, error) {
			time.Sleep(100 * time.Millisecond)
			return 3, nil
		})

		results, err := tg.Run()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "快速失败", err.Error())
	})
}

func TestConcurrencyTaskGroup_Run_MemoryLeaks(t *testing.T) {
	t.Run("内存泄漏测试", func(t *testing.T) {
		// 获取初始内存统计
		var m1 runtime.MemStats
		runtime.GC()
		runtime.GC() // 多次GC确保清理
		runtime.ReadMemStats(&m1)

		// 执行大量任务
		for i := 0; i < 100; i++ {
			tg := NewConcurrencyTaskGroup[string]()

			for j := 0; j < 10; j++ {
				value := strconv.Itoa(j)
				tg.Add(func() (string, error) {
					return value, nil
				})
			}

			results, err := tg.Run()
			assert.NoError(t, err)
			assert.Len(t, results, 10)
		}

		// 强制垃圾回收
		runtime.GC()
		runtime.GC()

		// 获取最终内存统计
		var m2 runtime.MemStats
		runtime.ReadMemStats(&m2)

		// 使用 int64 来避免负数问题，检查内存增长是否合理
		memoryIncrease := int64(m2.HeapInuse) - int64(m1.HeapInuse)
		t.Logf("内存增长: %d bytes", memoryIncrease)

		// 内存增长应该相对较小（允许合理的波动，包括负增长）
		// 这里主要检查是否有异常大的内存增长
		if memoryIncrease > 1024*1024 {
			t.Errorf("内存增长过大，可能存在内存泄漏: %d bytes", memoryIncrease)
		}
		// 如果内存减少或增长在合理范围内，测试通过
	})
}

func TestConcurrencyTaskGroup_Run_ChannelHandling(t *testing.T) {
	t.Run("验证channel正确关闭", func(t *testing.T) {
		tg := NewConcurrencyTaskGroup[int]()

		// 添加多个任务
		for i := 0; i < 5; i++ {
			value := i
			tg.Add(func() (int, error) {
				return value, nil
			})
		}

		// 这个测试主要验证没有死锁或channel泄漏
		results, err := tg.Run()
		assert.NoError(t, err)
		assert.Len(t, results, 5)
	})
}
