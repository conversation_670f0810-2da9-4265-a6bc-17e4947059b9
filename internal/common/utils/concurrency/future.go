package concurrency

import (
	"context"
	"fmt"
	"sync"
)

type futureError interface {
	Err(ctx context.Context) error
}

type Future[T any] interface {
	futureError
	// Set sets the result or error of the future
	Set(value T, err error)
	// Val returns the result of the future
	Val(ctx context.Context) T
}

type futureImpl[T any] struct {
	result  T
	err     error
	done    bool
	doneCh  chan struct{}
	doneMu  sync.Mutex
	setOnce sync.Once
}

func NewFuture[T any]() Future[T] {
	return &futureImpl[T]{
		doneCh: make(chan struct{}),
	}
}

func (f *futureImpl[T]) isDone() bool {
	f.doneMu.Lock()
	defer f.doneMu.Unlock()
	return f.done
}

func (f *futureImpl[T]) setDone() {
	f.doneMu.Lock()
	defer f.doneMu.Unlock()
	f.done = true
	close(f.doneCh)
}

func (f *futureImpl[T]) Set(value T, err error) {
	f.setOnce.Do(func() {
		f.result = value
		f.err = err
		f.setDone()
	})
}

func (f *futureImpl[T]) Val(ctx context.Context) T {
	if f.isDone() {
		return f.result
	}

	select {
	case <-ctx.Done():
		var zero T
		return zero
	case <-f.doneCh:
		return f.result
	}
}

func (f *futureImpl[T]) Err(ctx context.Context) error {
	if f.isDone() {
		return f.err
	}

	select {
	case <-ctx.Done():
		return ctx.Err()
	case <-f.doneCh:
		return f.err
	}
}

func GoFuture[T any](fn func() (T, error)) Future[T] {
	f := NewFuture[T]()
	go func() {
		defer func() {
			r := recover()
			if r == nil {
				return
			}
			var val T
			var err error
			if e, ok := r.(error); ok {
				err = e
			} else {
				err = fmt.Errorf("panic: %v", r)
			}
			f.Set(val, err)
		}()

		value, err := fn()
		f.Set(value, err)
	}()
	return f
}

func FutureFirstError(ctx context.Context, futureErrs ...futureError) error {
	for _, futureErr := range futureErrs {
		err := futureErr.Err(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

// FutureFirstErrorConcurrent 并发版本的 FutureFirstError，支持快速失败
// 不会被单个慢任务阻塞，任何一个 future 返回错误就立即返回
func FutureFirstErrorConcurrent(ctx context.Context, futureErrs ...futureError) error {
	if len(futureErrs) == 0 {
		return nil
	}

	if len(futureErrs) == 1 {
		return futureErrs[0].Err(ctx)
	}

	errCh := make(chan error, 1) // 只需要第一个错误
	doneCh := make(chan struct{})

	var wg sync.WaitGroup
	wg.Add(len(futureErrs))

	// 并发检查所有 future 的错误
	for _, future := range futureErrs {
		go func(f futureError) {
			defer wg.Done()

			err := f.Err(ctx)
			if err != nil {
				select {
				case errCh <- err:
				default: // 如果已经有错误，忽略后续错误
				}
			}
		}(future)
	}

	// 等待所有检查完成
	go func() {
		wg.Wait()
		close(doneCh)
	}()

	// 等待第一个错误或所有检查完成
	select {
	case <-ctx.Done():
		return ctx.Err()
	case err := <-errCh:
		return err // 快速失败：遇到第一个错误立即返回
	case <-doneCh:
		return nil // 所有 future 都没有错误
	}
}

// FutureAllErrors 等待所有 future 完成并收集所有错误
func FutureAllErrors(ctx context.Context, futureErrs ...futureError) []error {
	if len(futureErrs) == 0 {
		return nil
	}

	errorsCh := make(chan error, len(futureErrs))
	var wg sync.WaitGroup
	wg.Add(len(futureErrs))

	// 并发检查所有 future 的错误
	for _, future := range futureErrs {
		go func(f futureError) {
			defer wg.Done()

			err := f.Err(ctx)
			if err != nil {
				errorsCh <- err
			}
		}(future)
	}

	go func() {
		wg.Wait()
		close(errorsCh)
	}()

	var errors []error
	for {
		select {
		case <-ctx.Done():
			return append(errors, ctx.Err())
		case err, ok := <-errorsCh:
			if !ok {
				return errors // 所有检查完成
			}
			errors = append(errors, err)
		}
	}
}
