package concurrency

import (
	"context"
	"errors"
	"testing"
	"time"
)

func TestFutureFirstErrorConcurrent_FastFail(t *testing.T) {
	// 创建一个快速失败的 future
	fastFuture := GoFuture(func() (string, error) {
		return "", errors.New("fast error")
	})

	// 创建一个慢 future
	slowFuture := GoFuture(func() (string, error) {
		time.Sleep(5 * time.Second)
		return "slow result", nil
	})

	start := time.Now()
	err := FutureFirstErrorConcurrent(context.Background(), fastFuture, slowFuture)
	duration := time.Since(start)

	// 应该快速失败，不会等待慢 future 完成
	if err == nil {
		t.Error("Expected error but got nil")
	}

	if err.Error() != "fast error" {
		t.<PERSON><PERSON><PERSON>("Expected 'fast error' but got %v", err)
	}

	if duration > 1*time.Second {
		t.Errorf("Expected fast fail but took %v", duration)
	}
}

func TestFutureFirstErrorConcurrent_AllSuccess(t *testing.T) {
	// 创建多个成功的 future
	future1 := GoFuture(func() (string, error) {
		time.Sleep(100 * time.Millisecond)
		return "result1", nil
	})

	future2 := GoFuture(func() (string, error) {
		time.Sleep(200 * time.Millisecond)
		return "result2", nil
	})

	future3 := GoFuture(func() (string, error) {
		time.Sleep(150 * time.Millisecond)
		return "result3", nil
	})

	start := time.Now()
	err := FutureFirstErrorConcurrent(context.Background(), future1, future2, future3)
	duration := time.Since(start)

	// 应该没有错误
	if err != nil {
		t.Errorf("Expected no error but got %v", err)
	}

	// 应该等待所有 future 完成，大约 200ms
	if duration < 200*time.Millisecond || duration > 300*time.Millisecond {
		t.Errorf("Expected around 200ms but took %v", duration)
	}
}

func TestFutureFirstErrorConcurrent_ContextTimeout(t *testing.T) {
	// 创建一个慢 future
	slowFuture := GoFuture(func() (string, error) {
		time.Sleep(5 * time.Second)
		return "slow result", nil
	})

	// 使用超时 context
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	start := time.Now()
	err := FutureFirstErrorConcurrent(ctx, slowFuture)
	duration := time.Since(start)

	// 应该因为超时而失败
	if err == nil {
		t.Error("Expected timeout error but got nil")
	}

	if !errors.Is(err, context.DeadlineExceeded) {
		t.Errorf("Expected context.DeadlineExceeded but got %v", err)
	}

	if duration > 200*time.Millisecond {
		t.Errorf("Expected timeout around 100ms but took %v", duration)
	}
}
