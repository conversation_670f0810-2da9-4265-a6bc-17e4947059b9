package concurrency

import (
	"context"
	"errors"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewConcurrencySplitCaller(t *testing.T) {
	caller := NewConcurrencySplitCaller[int, string]()
	assert.NotNil(t, caller)
}

func TestConcurrencySplitCaller_Call(t *testing.T) {
	caller := NewConcurrencySplitCaller[int, string]()
	ctx := context.Background()

	// 成功的调用函数
	successCaller := func(ctx context.Context, queries []int) ([]string, error) {
		var results []string
		for _, q := range queries {
			results = append(results, strconv.Itoa(q))
		}
		return results, nil
	}

	// 失败的调用函数
	failCaller := func(ctx context.Context, queries []int) ([]string, error) {
		return nil, errors.New("test error")
	}

	t.Run("正常情况", func(t *testing.T) {
		queries := []int{1, 2, 3, 4, 5}
		results, err := caller.Call(ctx, queries, 2, successCaller)
		assert.NoError(t, err)
		assert.Len(t, results, 5)
		assert.Equal(t, []string{"1", "2", "3", "4", "5"}, results)
	})

	t.Run("空查询", func(t *testing.T) {
		queries := []int{}
		results, err := caller.Call(ctx, queries, 2, successCaller)
		assert.NoError(t, err)
		assert.Nil(t, results)
	})

	t.Run("无效大小 - 零", func(t *testing.T) {
		queries := []int{1, 2, 3}
		results, err := caller.Call(ctx, queries, 0, successCaller)
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "split caller query size must be greater than 0", err.Error())
	})

	t.Run("无效大小 - 负数", func(t *testing.T) {
		queries := []int{1, 2, 3}
		results, err := caller.Call(ctx, queries, -1, successCaller)
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "split caller query size must be greater than 0", err.Error())
	})

	t.Run("调用失败", func(t *testing.T) {
		queries := []int{1, 2, 3}
		results, err := caller.Call(ctx, queries, 2, failCaller)
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "test error", err.Error())
	})

	t.Run("单个元素", func(t *testing.T) {
		queries := []int{1}
		results, err := caller.Call(ctx, queries, 1, successCaller)
		assert.NoError(t, err)
		assert.Len(t, results, 1)
		assert.Equal(t, []string{"1"}, results)
	})

	t.Run("大小大于查询数量", func(t *testing.T) {
		queries := []int{1, 2, 3}
		results, err := caller.Call(ctx, queries, 10, successCaller)
		assert.NoError(t, err)
		assert.Len(t, results, 3)
		assert.Equal(t, []string{"1", "2", "3"}, results)
	})

	t.Run("大小等于查询数量", func(t *testing.T) {
		queries := []int{1, 2, 3}
		results, err := caller.Call(ctx, queries, 3, successCaller)
		assert.NoError(t, err)
		assert.Len(t, results, 3)
		assert.Equal(t, []string{"1", "2", "3"}, results)
	})

	t.Run("大批量数据", func(t *testing.T) {
		queries := make([]int, 100)
		for i := range queries {
			queries[i] = i
		}
		results, err := caller.Call(ctx, queries, 10, successCaller)
		assert.NoError(t, err)
		assert.Len(t, results, 100)

		// 验证结果顺序
		for i, result := range results {
			assert.Equal(t, strconv.Itoa(i), result)
		}
	})

	t.Run("Context 传递", func(t *testing.T) {
		ctxWithValue := context.WithValue(ctx, "test", "value")
		contextCheckCaller := func(ctx context.Context, queries []int) ([]string, error) {
			value := ctx.Value("test")
			assert.Equal(t, "value", value)
			return []string{"ok"}, nil
		}

		queries := []int{1}
		results, err := caller.Call(ctxWithValue, queries, 1, contextCheckCaller)
		assert.NoError(t, err)
		assert.Equal(t, []string{"ok"}, results)
	})
}

func TestSplitQueries(t *testing.T) {
	t.Run("正常分割", func(t *testing.T) {
		queries := []int{1, 2, 3, 4, 5}
		groups := splitQueries(queries, 2)
		assert.Len(t, groups, 3)
		assert.Equal(t, []int{1, 2}, groups[0])
		assert.Equal(t, []int{3, 4}, groups[1])
		assert.Equal(t, []int{5}, groups[2])
	})

	t.Run("空切片", func(t *testing.T) {
		queries := []int{}
		groups := splitQueries(queries, 2)
		assert.Len(t, groups, 0)
	})

	t.Run("单个元素", func(t *testing.T) {
		queries := []int{1}
		groups := splitQueries(queries, 2)
		assert.Len(t, groups, 1)
		assert.Equal(t, []int{1}, groups[0])
	})

	t.Run("大小为1", func(t *testing.T) {
		queries := []int{1, 2, 3}
		groups := splitQueries(queries, 1)
		assert.Len(t, groups, 3)
		assert.Equal(t, []int{1}, groups[0])
		assert.Equal(t, []int{2}, groups[1])
		assert.Equal(t, []int{3}, groups[2])
	})

	t.Run("大小等于切片长度", func(t *testing.T) {
		queries := []int{1, 2, 3}
		groups := splitQueries(queries, 3)
		assert.Len(t, groups, 1)
		assert.Equal(t, []int{1, 2, 3}, groups[0])
	})

	t.Run("大小大于切片长度", func(t *testing.T) {
		queries := []int{1, 2, 3}
		groups := splitQueries(queries, 10)
		assert.Len(t, groups, 1)
		assert.Equal(t, []int{1, 2, 3}, groups[0])
	})

	t.Run("均匀分割", func(t *testing.T) {
		queries := []int{1, 2, 3, 4, 5, 6}
		groups := splitQueries(queries, 3)
		assert.Len(t, groups, 2)
		assert.Equal(t, []int{1, 2, 3}, groups[0])
		assert.Equal(t, []int{4, 5, 6}, groups[1])
	})

	t.Run("不均匀分割", func(t *testing.T) {
		queries := []int{1, 2, 3, 4, 5, 6, 7}
		groups := splitQueries(queries, 3)
		assert.Len(t, groups, 3)
		assert.Equal(t, []int{1, 2, 3}, groups[0])
		assert.Equal(t, []int{4, 5}, groups[1])
		assert.Equal(t, []int{6, 7}, groups[2])
	})

	t.Run("均匀分布优化", func(t *testing.T) {
		// 测试 51 个元素，最大批处理大小为 50
		queries := make([]int, 51)
		for i := range queries {
			queries[i] = i
		}
		groups := splitQueries(queries, 50)
		assert.Len(t, groups, 2)
		assert.Len(t, groups[0], 26)
		assert.Len(t, groups[1], 25)
	})
}

func TestEvenSplitBatchSize(t *testing.T) {
	t.Run("正常情况", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(10, 3)
		assert.Equal(t, 2, baseSize)
		assert.Equal(t, 2, extra)
	})

	t.Run("完全整除", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(12, 3)
		assert.Equal(t, 3, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("总数小于最大批处理大小", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(5, 10)
		assert.Equal(t, 5, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("总数等于最大批处理大小", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(10, 10)
		assert.Equal(t, 10, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("均匀分布示例", func(t *testing.T) {
		// 51 个元素，最大批处理大小为 50
		baseSize, extra := evenSplitBatchSize(51, 50)
		assert.Equal(t, 25, baseSize)
		assert.Equal(t, 1, extra)
		// 第一个批次：25 + 1 = 26
		// 第二个批次：25
		// 总计：26 + 25 = 51
	})

	t.Run("边界情况 - 1个元素", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(1, 1)
		assert.Equal(t, 1, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("边界情况 - 大量元素", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(1000, 100)
		assert.Equal(t, 100, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("不均匀分布示例", func(t *testing.T) {
		// 101 个元素，最大批处理大小为 50
		baseSize, extra := evenSplitBatchSize(101, 50)
		assert.Equal(t, 33, baseSize)
		assert.Equal(t, 2, extra)
		// 第一个批次：33 + 1 = 34
		// 第二个批次：33 + 1 = 34
		// 第三个批次：33
		// 总计：34 + 34 + 33 = 101
	})

	t.Run("无效输入 - 零总数", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(0, 10)
		assert.Equal(t, 0, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("无效输入 - 负总数", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(-1, 10)
		assert.Equal(t, 0, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("无效输入 - 零批处理大小", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(10, 0)
		assert.Equal(t, 0, baseSize)
		assert.Equal(t, 0, extra)
	})

	t.Run("无效输入 - 负批处理大小", func(t *testing.T) {
		baseSize, extra := evenSplitBatchSize(10, -1)
		assert.Equal(t, 0, baseSize)
		assert.Equal(t, 0, extra)
	})
}

func TestSplitCallerIntegration(t *testing.T) {
	caller := NewConcurrencySplitCaller[int, string]()
	ctx := context.Background()

	t.Run("完整流程测试", func(t *testing.T) {
		// 模拟一个实际的调用场景
		queries := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

		// 模拟一个将数字转换为字符串的服务
		mockService := func(ctx context.Context, queries []int) ([]string, error) {
			var results []string
			for _, q := range queries {
				results = append(results, "result-"+strconv.Itoa(q))
			}
			return results, nil
		}

		results, err := caller.Call(ctx, queries, 3, mockService)
		assert.NoError(t, err)
		assert.Len(t, results, 10)

		// 验证结果的正确性和顺序
		for i, result := range results {
			expected := "result-" + strconv.Itoa(i+1)
			assert.Equal(t, expected, result)
		}
	})

	t.Run("带错误处理的完整流程", func(t *testing.T) {
		queries := []int{1, 2, 3, 4, 5}

		// 模拟一个会失败的服务
		failingService := func(ctx context.Context, queries []int) ([]string, error) {
			// 模拟处理第一个查询时失败
			if len(queries) > 0 && queries[0] == 1 {
				return nil, errors.New("服务不可用")
			}
			return []string{"ok"}, nil
		}

		results, err := caller.Call(ctx, queries, 2, failingService)
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.Equal(t, "服务不可用", err.Error())
	})

	t.Run("并发安全测试", func(t *testing.T) {
		queries := make([]int, 100)
		for i := range queries {
			queries[i] = i
		}

		// 模拟一个可能有并发问题的服务
		concurrentService := func(ctx context.Context, queries []int) ([]string, error) {
			var results []string
			for _, q := range queries {
				results = append(results, strconv.Itoa(q))
			}
			return results, nil
		}

		results, err := caller.Call(ctx, queries, 10, concurrentService)
		assert.NoError(t, err)
		assert.Len(t, results, 100)

		// 验证所有结果都存在
		resultSet := make(map[string]bool)
		for _, result := range results {
			resultSet[result] = true
		}

		for i := 0; i < 100; i++ {
			assert.True(t, resultSet[strconv.Itoa(i)], "结果中缺少: %d", i)
		}
	})
}

func TestSplitCallerWithDifferentTypes(t *testing.T) {
	t.Run("字符串到整数", func(t *testing.T) {
		caller := NewConcurrencySplitCaller[string, int]()
		ctx := context.Background()

		service := func(ctx context.Context, queries []string) ([]int, error) {
			var results []int
			for _, q := range queries {
				if val, err := strconv.Atoi(q); err == nil {
					results = append(results, val)
				}
			}
			return results, nil
		}

		queries := []string{"1", "2", "3", "4", "5"}
		results, err := caller.Call(ctx, queries, 2, service)
		assert.NoError(t, err)
		assert.Equal(t, []int{1, 2, 3, 4, 5}, results)
	})

	t.Run("结构体类型", func(t *testing.T) {
		type Request struct {
			ID int
		}
		type Response struct {
			Data string
		}

		caller := NewConcurrencySplitCaller[Request, Response]()
		ctx := context.Background()

		service := func(ctx context.Context, queries []Request) ([]Response, error) {
			var results []Response
			for _, q := range queries {
				results = append(results, Response{Data: "response-" + strconv.Itoa(q.ID)})
			}
			return results, nil
		}

		queries := []Request{{ID: 1}, {ID: 2}, {ID: 3}}
		results, err := caller.Call(ctx, queries, 2, service)
		assert.NoError(t, err)
		assert.Len(t, results, 3)
		assert.Equal(t, "response-1", results[0].Data)
		assert.Equal(t, "response-2", results[1].Data)
		assert.Equal(t, "response-3", results[2].Data)
	})
}
