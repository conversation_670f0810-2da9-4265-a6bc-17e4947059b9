package concurrency

import (
	"context"
	"errors"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
)

type SplitCallerFunc[P, T any] func(ctx context.Context, queries []P) ([]T, error)

type ConcurrencySplitCaller[P, T any] struct{}

func NewConcurrencySplitCaller[P, T any]() *ConcurrencySplitCaller[P, T] {
	return &ConcurrencySplitCaller[P, T]{}
}

// Call 执行并发分批调用
func (c *ConcurrencySplitCaller[P, T]) Call(
	ctx context.Context,
	queries []P,
	size int,
	caller SplitCallerFunc[P, T],
) ([]T, error) {
	if size <= 0 {
		return nil, errors.New("split caller query size must be greater than 0")
	}

	if len(queries) == 0 {
		return nil, nil
	}

	groups := splitQueries(queries, size)

	// 使用优化后的 ConcurrencyTaskGroup
	tg := NewConcurrencyTaskGroup[collection.Pair[int, []T]]()
	for i, g := range groups {
		index := i
		group := g
		tg.Add(func() (collection.Pair[int, []T], error) {
			result, err := caller(ctx, group)
			if err != nil {
				return collection.Pair[int, []T]{}, err
			}
			return collection.NewPair(index, result), nil
		})
	}

	results, err := tg.Run()
	if err != nil {
		return nil, err
	}

	indexToResult := collection.SliceToMap(results, func(p collection.Pair[int, []T]) int {
		return p.Key()
	})

	var dst []T
	for i := 0; i < len(groups); i++ {
		dst = append(dst, indexToResult[i].Val()...)
	}

	return dst, nil
}

func splitQueries[T any](src []T, size int) [][]T {
	reqLen := len(src)
	realBatchSize, extra := evenSplitBatchSize(reqLen, size)
	if realBatchSize == 0 {
		return [][]T{}
	}
	splitCount := reqLen / realBatchSize
	dst := make([][]T, 0, splitCount)
	for i := 0; i < reqLen; {
		j := i + realBatchSize
		if extra > 0 {
			j++
			extra--
		}
		if j > reqLen {
			j = reqLen
		}
		dst = append(dst, src[i:j])
		i = j
	}
	return dst
}

// evenSplitBatchSize 将 total 均匀分成 n 份，n 尽可能小，并且保证 size 不超过 maxBatchSize, 保证分发请求时每个请求的计算量大致相同
//
//	例如 total 是 51, maxBatchSize 是 50 时，会拆分成 [26, 25]，非 [50, 1]
//
// @return baseSize: 最终的批处理大小
// @return extra:    前 extra 次的批处理大小需要额外加 1
func evenSplitBatchSize(total int, maxBatchSize int) (baseSize int, extra int) {
	if total <= 0 || maxBatchSize <= 0 {
		return 0, 0
	}
	numBatches := (total + maxBatchSize - 1) / maxBatchSize
	baseSize = total / numBatches
	extra = total % numBatches
	return baseSize, extra
}
