package ctxutils

import (
	"context"
	"sync/atomic"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
)

type (
	traceNumCtxKey  string
	traceInfoCtxKey string

	TraceEntity struct {
		LogTrace  uint32               `json:"log_trace"`
		Name      string               `json:"name"`
		FromCache bool                 `json:"-"`
		StartTime string               `json:"start_time"`
		Cost      string               `json:"cost"`
		System    constant.SystemCode  `json:"system"`
		Service   constant.ServiceCode `json:"service"`
		Request   string               `json:"request"`
		Response  string               `json:"response"`
		Error     string               `json:"error"`
		ExtraData string               `json:"extra_data"`
	}
)

const (
	traceNumKey  traceNumCtxKey  = "log_trace_num"
	traceInfoKey traceInfoCtxKey = "log_trace_info"
)

func GetLogTraceNum(ctx context.Context) *uint32 {
	var tmpLogOrder uint32
	if logOrderObj := ctx.Value(traceNumKey); logOrderObj != nil {
		if logOrder, ok := logOrderObj.(*uint32); ok {
			return logOrder
		}
	}
	return &tmpLogOrder
}

func SetLogTraceNum(ctx context.Context) (context.Context, uint32) {
	logTrace := GetLogTraceNum(ctx)
	newLogTrace := atomic.AddUint32(logTrace, 1)
	newCtx := context.WithValue(ctx, traceNumKey, &newLogTrace)

	return newCtx, newLogTrace
}

func GetLogTraceInfo(ctx context.Context) *[]TraceEntity {
	tmpLogTraceInfo := make([]TraceEntity, 0)
	if logTraceInfo := ctx.Value(traceInfoKey); logTraceInfo != nil {
		if logTraceObj, ok := logTraceInfo.(*[]TraceEntity); ok {
			return logTraceObj
		}
	}

	return &tmpLogTraceInfo
}

func SetLogTraceInfo(ctx context.Context, logInfo TraceEntity) context.Context {
	logTraceInfo := GetLogTraceInfo(ctx)
	if logTraceInfo == nil {
		tmpLogTraceInfo := make([]TraceEntity, 0)
		logTraceInfo = &tmpLogTraceInfo
	}
	*logTraceInfo = append(*logTraceInfo, logInfo)

	newCtx := context.WithValue(ctx, traceInfoKey, logTraceInfo)

	return newCtx
}
