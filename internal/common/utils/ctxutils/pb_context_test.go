package ctxutils

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/reflect/protoreflect"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

func TestPBContextData(t *testing.T) {
	ctx := context.Background()

	// 测试设置请求
	req := &mockProtoMessage{data: "test request"}
	ctx = SetPBRequest(ctx, req)

	data := GetPBContextData(ctx)
	require.NotNil(t, data)
	assert.Equal(t, req, data.Request)
	assert.Nil(t, data.Response)
	assert.Nil(t, data.Error)

	// 测试设置响应
	resp := &mockProtoMessage{data: "test response"}
	ctx = SetPBResponse(ctx, resp)

	data = GetPBContextData(ctx)
	require.NotNil(t, data)
	assert.Equal(t, resp, data.Response)
	assert.Nil(t, data.Error)

	// 测试设置错误
	testErr := fsserr.New(fsserr.ParamErr, "test error")

	data = GetPBContextData(ctx)
	require.NotNil(t, data)
	assert.Equal(t, testErr, data.Error)

	// 测试同时设置响应和错误
	resp2 := &mockProtoMessage{data: "test response 2"}
	testErr2 := fsserr.New(fsserr.ServerErr, "test error 2")

	data = GetPBContextData(ctx)
	require.NotNil(t, data)
	assert.Equal(t, resp2, data.Response)
	assert.Equal(t, testErr2, data.Error)

	// 测试清理数据
	ctx = ClearPBContextData(ctx)
	data = GetPBContextData(ctx)
	assert.Nil(t, data)
}

func TestSetPBRequest_NewContext(t *testing.T) {
	ctx := context.Background()

	// 测试在没有现有数据的情况下设置请求
	req := &mockProtoMessage{data: "new request"}
	ctx = SetPBRequest(ctx, req)

	data := GetPBContextData(ctx)
	require.NotNil(t, data)
	assert.Equal(t, req, data.Request)
}

func TestSetPBResponse_NoExistingData(t *testing.T) {
	ctx := context.Background()

	// 测试在没有现有数据的情况下设置响应
	resp := &mockProtoMessage{data: "new response"}
	ctx = SetPBResponse(ctx, resp)

	data := GetPBContextData(ctx)
	assert.Nil(t, data) // 应该返回 nil，因为没有现有数据
}

func TestSetPBError_NoExistingData(t *testing.T) {
	ctx := context.Background()

	// 测试在没有现有数据的情况下设置错误

	data := GetPBContextData(ctx)
	assert.Nil(t, data) // 应该返回 nil，因为没有现有数据
}

// mockProtoMessage 用于测试的 mock protobuf 消息
type mockProtoMessage struct {
	data string
}

func (m *mockProtoMessage) Reset() {
	m.data = ""
}

func (m *mockProtoMessage) String() string {
	return m.data
}

func (m *mockProtoMessage) ProtoMessage() {}

func (m *mockProtoMessage) ProtoReflect() protoreflect.Message {
	return nil
}
