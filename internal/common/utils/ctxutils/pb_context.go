package ctxutils

import (
	"context"

	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type pbContextKey struct{}

type PBContextData struct {
	Request   proto.Message
	Response  proto.Message
	Error     fsserr.Error
	Operation string
}

func InitPBContextData(ctx context.Context) context.Context {
	data := &PBContextData{
		Request:  nil,
		Response: nil,
		Error:    nil,
	}
	return context.WithValue(ctx, pbContextKey{}, data)
}

// SetPBRequest 将 protobuf 请求存储到 context 中
func SetPBRequest(ctx context.Context, req proto.Message) context.Context {
	if data, ok := ctx.Value(pbContextKey{}).(*PBContextData); ok {
		data.Request = req
		return context.WithValue(ctx, pbContextKey{}, data)
	}
	return ctx
}

// SetPBResponse 将 protobuf 响应存储到 context 中
func SetPBResponse(ctx context.Context, resp proto.Message) context.Context {
	if data, ok := ctx.Value(pbContextKey{}).(*PBContextData); ok {
		data.Response = resp
		return context.WithValue(ctx, pbContextKey{}, data)
	}
	return ctx
}

func IsProtobufRequest(ctx context.Context) bool {
	_, ok := ctx.Value(pbContextKey{}).(*PBContextData)
	return ok
}

// GetPBContextData 从 context 中获取 protobuf 数据
func GetPBContextData(ctx context.Context) *PBContextData {
	if data, ok := ctx.Value(pbContextKey{}).(*PBContextData); ok {
		return data
	}
	return nil
}

// ClearPBContextData 清除 context 中的 protobuf 数据
func ClearPBContextData(ctx context.Context) context.Context {
	return context.WithValue(ctx, pbContextKey{}, nil)
}
