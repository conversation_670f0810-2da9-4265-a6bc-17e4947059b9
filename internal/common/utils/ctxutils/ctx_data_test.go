package ctxutils

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
)

func TestNewCtxData(t *testing.T) {
	ctxData := NewCtxData()
	assert.NotNil(t, ctxData)
	assert.Equal(t, Option(0), ctxData.contextOption)
	assert.Equal(t, constant.DefaultTypeMark, ctxData.requestTypeMark)
	assert.Equal(t, "", ctxData.requestId)
	assert.Equal(t, "", ctxData.url)
}

func TestCtxData_Has(t *testing.T) {
	ctxData := NewCtxData()

	// 测试空的 CtxData
	assert.False(t, ctxData.Has(ShadowTraffic))
	assert.False(t, ctxData.Has(TrafficRecord))

	// 测试设置选项后
	ctxData.SetContextOption(ShadowTraffic)
	assert.True(t, ctxData.Has(ShadowTraffic))
	assert.False(t, ctxData.Has(TrafficRecord))

	// 测试设置多个选项
	ctxData.SetContextOption(TrafficRecord)
	assert.True(t, ctxData.Has(ShadowTraffic))
	assert.True(t, ctxData.Has(TrafficRecord))

	// 测试 nil 情况
	var nilCtxData *CtxData
	assert.False(t, nilCtxData.Has(ShadowTraffic))
}

func TestCtxData_SetContextOption(t *testing.T) {
	ctxData := NewCtxData()

	// 测试正常设置
	result := ctxData.SetContextOption(ShadowTraffic)
	assert.Equal(t, ctxData, result)
	assert.True(t, ctxData.Has(ShadowTraffic))

	// 测试 nil 情况
	var nilCtxData *CtxData
	result = nilCtxData.SetContextOption(ShadowTraffic)
	assert.Nil(t, result)
}

func TestCtxData_RequestId(t *testing.T) {
	ctxData := NewCtxData()

	// 测试默认值
	assert.Equal(t, "", ctxData.GetRequestId())

	// 测试设置值
	testId := "test-request-id"
	result := ctxData.SetRequestId(testId)
	assert.Equal(t, ctxData, result)
	assert.Equal(t, testId, ctxData.GetRequestId())

	// 测试 nil 情况
	var nilCtxData *CtxData
	assert.Equal(t, "", nilCtxData.GetRequestId())
	result = nilCtxData.SetRequestId(testId)
	assert.Nil(t, result)
}

func TestCtxData_Url(t *testing.T) {
	ctxData := NewCtxData()

	// 测试默认值
	assert.Equal(t, "", ctxData.GetUrl())

	// 测试设置值
	testUrl := "http://test.com"
	result := ctxData.SetUrl(testUrl)
	assert.Equal(t, ctxData, result)
	assert.Equal(t, testUrl, ctxData.GetUrl())

	// 测试 nil 情况
	var nilCtxData *CtxData
	assert.Equal(t, "", nilCtxData.GetUrl())
	result = nilCtxData.SetUrl(testUrl)
	assert.Nil(t, result)
}

func TestCtxData_RequestTypeMark(t *testing.T) {
	ctxData := NewCtxData()

	// 测试默认值
	assert.Equal(t, constant.DefaultTypeMark, ctxData.GetRequestTypeMark())
	assert.Equal(t, "8", ctxData.GetRequestTypeMarkString())

	// 测试设置值
	result := ctxData.SetRequestTypeMark(constant.DebugRequestMark)
	assert.Equal(t, ctxData, result)
	assert.Equal(t, constant.DebugRequestMark, ctxData.GetRequestTypeMark())
	assert.Equal(t, "2", ctxData.GetRequestTypeMarkString())

	// 测试 nil 情况
	var nilCtxData *CtxData
	assert.Equal(t, constant.DefaultTypeMark, nilCtxData.GetRequestTypeMark())
	assert.Equal(t, "8", nilCtxData.GetRequestTypeMarkString())
	result = nilCtxData.SetRequestTypeMark(constant.DebugRequestMark)
	assert.Nil(t, result)
}

func TestCtxData_RandomByRequest(t *testing.T) {
	ctxData := NewCtxData()

	// 测试默认值
	assert.False(t, ctxData.IsRandomByRequest(0))
	assert.True(t, ctxData.IsRandomByRequest(100))

	// 测试设置值
	result := ctxData.SetRandomByRequest(50)
	assert.Equal(t, ctxData, result)
	assert.True(t, ctxData.IsRandomByRequest(100))
	assert.False(t, ctxData.IsRandomByRequest(10))

	// 测试边界情况
	assert.False(t, ctxData.IsRandomByRequest(-1))

	// 测试 nil 情况
	var nilCtxData *CtxData
	assert.False(t, nilCtxData.IsRandomByRequest(100))
	result = nilCtxData.SetRandomByRequest(50)
	assert.Nil(t, result)
}

func TestCtxData_RandomHundredByRequest(t *testing.T) {
	ctxData := NewCtxData()

	// 测试默认值
	assert.False(t, ctxData.IsRandomHundredByRequest(0))
	assert.True(t, ctxData.IsRandomHundredByRequest(100))

	// 测试设置值
	result := ctxData.SetRandomHundredByRequest(50)
	assert.Equal(t, ctxData, result)
	assert.True(t, ctxData.IsRandomHundredByRequest(100))
	assert.False(t, ctxData.IsRandomHundredByRequest(10))

	// 测试边界情况
	assert.False(t, ctxData.IsRandomHundredByRequest(-1))

	// 测试 nil 情况
	var nilCtxData *CtxData
	assert.False(t, nilCtxData.IsRandomHundredByRequest(100))
	result = nilCtxData.SetRandomHundredByRequest(50)
	assert.Nil(t, result)
}

func TestCtxData_Clone(t *testing.T) {
	// 创建原始 CtxData
	original := NewCtxData()
	original.SetContextOption(ShadowTraffic | TrafficRecord)
	original.SetRequestId("test-id")
	original.SetUrl("http://test.com")
	original.SetRequestTypeMark(constant.DebugRequestMark)
	original.SetRandomByRequest(100)
	original.SetRandomHundredByRequest(50)

	// 测试克隆
	cloned := original.Clone()

	// 验证克隆结果
	assert.NotSame(t, original, cloned)
	assert.Equal(t, original.contextOption, cloned.contextOption)
	assert.Equal(t, original.requestTypeMark, cloned.requestTypeMark)
	assert.Equal(t, original.requestTypeMarkString, cloned.requestTypeMarkString)
	assert.Equal(t, original.randomByRequest, cloned.randomByRequest)
	assert.Equal(t, original.randomHundredByRequest, cloned.randomHundredByRequest)
	assert.Equal(t, original.requestId, cloned.requestId)
	assert.Equal(t, original.url, cloned.url)

	// 验证克隆后的修改不影响原始对象
	cloned.SetRequestId("new-id")
	assert.NotEqual(t, original.GetRequestId(), cloned.GetRequestId())
}

func TestSetCtxData(t *testing.T) {
	ctx := context.Background()
	ctxData := NewCtxData()
	ctxData.SetRequestId("test-id")

	// 测试设置 CtxData
	newCtx := SetCtxData(ctx, ctxData)
	assert.NotEqual(t, ctx, newCtx)

	// 验证可以取回数据
	retrieved := GetCtxData(newCtx)
	assert.Equal(t, "test-id", retrieved.GetRequestId())
}

func TestGetCtxData(t *testing.T) {
	ctx := context.Background()

	// 测试空上下文
	ctxData := GetCtxData(ctx)
	assert.NotNil(t, ctxData)
	assert.Equal(t, "", ctxData.GetRequestId())

	// 测试设置了 CtxData 的上下文
	originalCtxData := NewCtxData()
	originalCtxData.SetRequestId("test-id")
	ctxWithData := SetCtxData(ctx, originalCtxData)

	retrieved := GetCtxData(ctxWithData)
	assert.Equal(t, "test-id", retrieved.GetRequestId())
}

func TestTryGetCtxData(t *testing.T) {
	ctx := context.Background()

	// 测试空上下文
	ctxData, ok := TryGetCtxData(ctx)
	assert.False(t, ok)
	assert.Nil(t, ctxData)

	// 测试设置了 CtxData 的上下文
	originalCtxData := NewCtxData()
	originalCtxData.SetRequestId("test-id")
	ctxWithData := SetCtxData(ctx, originalCtxData)

	retrieved, ok := TryGetCtxData(ctxWithData)
	assert.True(t, ok)
	assert.Equal(t, "test-id", retrieved.GetRequestId())
}

func TestCtxDataCloneFunc(t *testing.T) {
	srcCtx := context.Background()
	dstCtx := context.Background()

	// 测试源上下文没有 CtxData
	result := CtxDataCloneFunc(srcCtx, dstCtx)
	assert.Equal(t, dstCtx, result)

	// 测试源上下文有 CtxData
	srcCtxData := NewCtxData()
	srcCtxData.SetRequestId("src-id")
	srcCtxWithData := SetCtxData(srcCtx, srcCtxData)

	result = CtxDataCloneFunc(srcCtxWithData, dstCtx)
	assert.NotEqual(t, dstCtx, result)

	resultCtxData := GetCtxData(result)
	assert.Equal(t, "src-id", resultCtxData.GetRequestId())

	// 测试目标上下文已有 CtxData
	dstCtxData := NewCtxData()
	dstCtxData.SetRequestId("dst-id")
	dstCtxWithData := SetCtxData(dstCtx, dstCtxData)

	result = CtxDataCloneFunc(srcCtxWithData, dstCtxWithData)
	assert.Equal(t, dstCtxWithData, result)

	resultCtxData = GetCtxData(result)
	assert.Equal(t, "dst-id", resultCtxData.GetRequestId())
}

func TestIsShadow(t *testing.T) {
	ctx := context.Background()

	// 测试空上下文
	assert.False(t, IsShadow(ctx))

	// 测试设置了 ShadowTraffic 的上下文
	ctxData := NewCtxData()
	ctxData.SetContextOption(ShadowTraffic)
	ctxWithShadow := SetCtxData(ctx, ctxData)

	assert.True(t, IsShadow(ctxWithShadow))

	// 测试没有设置 ShadowTraffic 的上下文
	ctxData2 := NewCtxData()
	ctxWithoutShadow := SetCtxData(ctx, ctxData2)

	assert.False(t, IsShadow(ctxWithoutShadow))
}

func TestIsCtxRecordEnabled(t *testing.T) {
	ctx := context.Background()

	// 测试空上下文
	assert.False(t, IsCtxRecordEnabled(ctx))

	// 测试设置了 TrafficRecord 的上下文
	ctxData := NewCtxData()
	ctxData.SetContextOption(TrafficRecord)
	ctxWithRecord := SetCtxData(ctx, ctxData)

	assert.True(t, IsCtxRecordEnabled(ctxWithRecord))

	// 测试没有设置 TrafficRecord 的上下文
	ctxData2 := NewCtxData()
	ctxWithoutRecord := SetCtxData(ctx, ctxData2)

	assert.False(t, IsCtxRecordEnabled(ctxWithoutRecord))
}

func TestIsReportDisable(t *testing.T) {
	ctx := context.Background()

	// 测试空上下文
	assert.False(t, IsReportDisable(ctx))

	// 测试设置了 BusinessReportDegraded 的上下文
	ctxData := NewCtxData()
	ctxData.SetContextOption(BusinessReportDegraded)
	ctxWithDisableReport := SetCtxData(ctx, ctxData)

	assert.True(t, IsReportDisable(ctxWithDisableReport))

	// 测试没有设置 BusinessReportDegraded 的上下文
	ctxData2 := NewCtxData()
	ctxWithoutDisableReport := SetCtxData(ctx, ctxData2)

	assert.False(t, IsReportDisable(ctxWithoutDisableReport))
}

func TestCtxDataNilSafety(t *testing.T) {
	var nilCtxData *CtxData

	// 测试所有方法的 nil 安全性
	assert.False(t, nilCtxData.Has(ShadowTraffic))
	assert.Nil(t, nilCtxData.SetContextOption(ShadowTraffic))
	assert.Equal(t, "", nilCtxData.GetRequestId())
	assert.Nil(t, nilCtxData.SetRequestId("test"))
	assert.Equal(t, "", nilCtxData.GetUrl())
	assert.Nil(t, nilCtxData.SetUrl("test"))
	assert.Equal(t, constant.DefaultTypeMark, nilCtxData.GetRequestTypeMark())
	assert.Equal(t, "8", nilCtxData.GetRequestTypeMarkString())
	assert.Nil(t, nilCtxData.SetRequestTypeMark(constant.DebugRequestMark))
	assert.False(t, nilCtxData.IsRandomByRequest(100))
	assert.Nil(t, nilCtxData.SetRandomByRequest(50))
	assert.False(t, nilCtxData.IsRandomHundredByRequest(100))
	assert.Nil(t, nilCtxData.SetRandomHundredByRequest(50))
}
