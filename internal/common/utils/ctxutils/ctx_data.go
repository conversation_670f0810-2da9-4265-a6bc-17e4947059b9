package ctxutils

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

func CtxDataCloneFunc(src context.Context, dst context.Context) (dstClone context.Context) {
	_, ok := TryGetCtxData(dst)
	if ok {
		return dst // 如果目标上下文已经有 CtxData，则不需要克隆
	}
	ctxData, ok := TryGetCtxData(src)
	if !ok || ctxData == nil {
		return dst // 如果源上下文没有 CtxData，则返回目标上下文
	}
	newCtxData := ctxData.Clone()
	return SetCtxData(dst, newCtxData)
}

func GetCtxData(ctx context.Context) *CtxData {
	ctxData, _ := ctx.Value(ctxDataKey{}).(*CtxData)
	if ctxData == nil {
		ctxData = NewCtxData()
	}
	return ctxData
}

func SetCtxData(ctx context.Context, ctxData *CtxData) context.Context {
	return context.WithValue(ctx, ctxDataKey{}, ctxData)
}

func TryGetCtxData(ctx context.Context) (*CtxData, bool) {
	ctxData, ok := ctx.Value(ctxDataKey{}).(*CtxData)
	if ctxData == nil || !ok {
		// 如果 ctxData 为 nil 或者 ok 为 false，说明没有设置 CtxData
		return nil, false
	}
	return ctxData, true
}

func IsShadow(ctx context.Context) bool {
	ctxData, ok := TryGetCtxData(ctx)
	if !ok {
		return ctxhelper.IsShadow(ctx)
	}
	return ctxData.Has(ShadowTraffic)
}

func IsCtxRecordEnabled(ctx context.Context) bool {
	ctxData, ok := TryGetCtxData(ctx)
	if !ok {
		return chassis_config.IsRecordEnable() && (recorder.IsRecorderContext(ctx) || recorder.ExtractReplayId(ctx) != "")
	}
	return ctxData.Has(TrafficRecord)
}

func IsReportDisable(ctx context.Context) bool {
	ctxData, ok := TryGetCtxData(ctx)
	if !ok {
		return false
	}
	return ctxData.Has(BusinessReportDegraded)
}

type ctxDataKey struct{}

type CtxData struct {
	contextOption         Option
	requestTypeMark       constant.RequestTypeMark
	requestTypeMarkString string
	// randomByRequest 0~10000 根据 request id 随机数
	randomByRequest uint16
	// randomHundredByRequest 0 ~ 100 根据 request id 随机数
	randomHundredByRequest uint16
	requestId              string
	url                    string
}

func NewCtxData() *CtxData {
	return &CtxData{
		requestTypeMark:       constant.DefaultTypeMark,
		requestTypeMarkString: constant.DefaultTypeMark.String(),
	}
}

func (c *CtxData) Has(f Option) bool {
	if c == nil {
		return false
	}
	return typ.ContainsFlag(c.contextOption, f)
}

func (c *CtxData) SetContextOption(option Option) *CtxData {
	if c == nil {
		return nil
	}
	c.contextOption |= option
	return c
}

func (c *CtxData) GetRequestId() string {
	if c == nil {
		return ""
	}
	return c.requestId
}

func (c *CtxData) SetRequestId(requestId string) *CtxData {
	if c == nil {
		return nil
	}
	c.requestId = requestId
	return c
}

func (c *CtxData) GetUrl() string {
	if c == nil {
		return ""
	}
	return c.url
}

func (c *CtxData) SetUrl(url string) *CtxData {
	if c == nil {
		return nil
	}
	c.url = url
	return c
}

func (c *CtxData) GetRequestTypeMark() constant.RequestTypeMark {
	if c == nil {
		return constant.DefaultTypeMark
	}
	return c.requestTypeMark
}

func (c *CtxData) GetRequestTypeMarkString() string {
	if c == nil {
		return "8"
	}
	return c.requestTypeMarkString
}

func (c *CtxData) SetRequestTypeMark(requestTypeMark constant.RequestTypeMark) *CtxData {
	if c == nil {
		return nil
	}
	c.requestTypeMark = requestTypeMark
	c.requestTypeMarkString = requestTypeMark.String()
	return c
}

func (c *CtxData) IsRandomByRequest(percent int) bool {
	if c == nil || percent < 0 {
		return false
	}
	return int(c.randomByRequest) < percent
}

func (c *CtxData) SetRandomByRequest(randomByRequest uint16) *CtxData {
	if c == nil {
		return nil
	}
	c.randomByRequest = randomByRequest
	return c
}

func (c *CtxData) IsRandomHundredByRequest(percent int) bool {
	if c == nil || percent < 0 {
		return false
	}
	return int(c.randomHundredByRequest) < percent
}

func (c *CtxData) SetRandomHundredByRequest(randomHundredByRequestId uint16) *CtxData {
	if c == nil {
		return nil
	}
	c.randomHundredByRequest = randomHundredByRequestId
	return c
}

func (c *CtxData) Clone() *CtxData {
	return &CtxData{
		contextOption:          c.contextOption,
		requestTypeMark:        c.requestTypeMark,
		requestTypeMarkString:  c.requestTypeMarkString,
		randomByRequest:        c.randomByRequest,
		randomHundredByRequest: c.randomHundredByRequest,
		requestId:              c.requestId,
		url:                    c.url,
	}
}
