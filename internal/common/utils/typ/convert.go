package typ

import (
	"errors"
	"reflect"
	"strconv"
	"strings"
	"unsafe"
)

func SnakeCase(s string) string {
	var b []byte
	for i := 0; i < len(s); i++ { // proto identifiers are always ASCII
		c := s[i]
		if isASCIIUpper(c) {
			b = append(b, '_')
			c += 'a' - 'A' // convert to lowercase
		}
		b = append(b, c)
	}
	return string(b)
}

func isASCIIUpper(c byte) bool {
	return 'A' <= c && c <= 'Z'
}

func BytesToString(b []byte) string {
	return unsafe.String(unsafe.SliceData(b), len(b))
}

func StringToBytes(s string) []byte {
	return unsafe.Slice(unsafe.StringData(s), len(s))
}

func ReceiverZeroValue[V any]() V {
	var zeroValue V

	if reflect.TypeOf(zeroValue).Kind() == reflect.Ptr {
		baseType := reflect.TypeOf(zeroValue).Elem()
		newValue := reflect.New(baseType).Interface()
		return newValue.(V)
	}

	return zeroValue
}

func ConvertIntegerSlices[F Integer, T Integer](input []F) []T {
	if input == nil {
		return make([]T, 0)
	}
	result := make([]T, len(input))
	for idx := range input {
		result[idx] = T(input[idx])
	}
	return result
}

func NumberToString[N Number](num N) string {
	switch v := any(num).(type) {
	case int, int8, int16, int32, int64:
		return strconv.FormatInt(int64(num), 10)
	case uint, uint8, uint16, uint32, uint64:
		return strconv.FormatUint(uint64(num), 10)
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	}
	return "" // Unsupported type
}

var (
	convertErr = errors.New("convert error")
)

func StringToNumber[N Number](s string) (N, error) {
	var num N
	switch any(num).(type) {
	case int, int8, int16, int32, int64:
		n, err := strconv.ParseInt(s, 10, 64)
		if err != nil {
			return num, err
		}
		return N(n), nil
	case uint, uint8, uint16, uint32, uint64:
		n, err := strconv.ParseUint(s, 10, 64)
		if err != nil {
			return num, err
		}
		return N(n), nil
	case float32:
		n, err := strconv.ParseFloat(s, 32)
		if err != nil {
			return num, err
		}
		return N(n), nil
	case float64:
		n, err := strconv.ParseFloat(s, 64)
		if err != nil {
			return num, err
		}
		return N(n), nil
	}
	return num, convertErr // Unsupported type
}

func BoolToString[B Bool](b B) string {
	if b {
		return "true"
	}
	return "false"
}

func StringToBool[B Bool](s string) (B, error) {
	if s == "1" || strings.ToLower(s) == "true" {
		return true, nil
	}
	if s == "" || s == "0" || strings.ToLower(s) == "false" {
		return false, nil
	}
	return false, convertErr
}
