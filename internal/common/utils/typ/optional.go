package typ

import (
	"github.com/bytedance/sonic"
)

type Optional[T any] struct {
	value  T
	exists bool
}

// Empty creates a new Optional without a value.
func Empty[T any]() Optional[T] {
	var empty T
	return Optional[T]{empty, false}
}

// NewOptional creates a new Optional with a value.
func NewOptional[T any](value T) Optional[T] {
	return Optional[T]{value, true}
}

// Exists Returns true if the optional value has been set
func (o Optional[T]) Exists() bool {
	return o.exists
}

func (o Optional[T]) Get() T {
	return o.value
}

// GetAndExists returns the value and whether it exists.
// It's invalid to use the returned value if the bool is false.
func (o Optional[T]) GetAndExists() (T, bool) {
	return o.value, o.exists
}

// GetOrElse returns the value if it exists and returns defaultValue otherwise.
func (o Optional[T]) GetOrElse(defaultValue T) T {
	if !o.exists {
		return defaultValue
	}
	return o.value
}

func (o *Optional[T]) Set(value T) {
	if !o.exists {
		o.exists = true
	}
	o.value = value
}

func (o *Optional[T]) Unset() {
	o.exists = false
	var empty T
	o.value = empty
}

// MustGet returns the value if it exists and panics otherwise.
func (o Optional[T]) MustGet() T {
	if !o.exists {
		panic(".MustGet() called on optional Optional value that doesn't exist.")
	}
	return o.value
}

func (o *Optional[T]) GetPointer() *T {
	if !o.exists {
		return nil
	}
	return &o.value
}

// MarshalJSON implements the json.Marshaller interface. Optionals wil
// marshall and unmarshall as a nullable json field. Item type must also
// implement json.Marshaller.
func (o *Optional[T]) MarshalJSON() ([]byte, error) {
	if !o.exists {
		return sonic.Marshal(nil)
	}
	return sonic.Marshal(&o.value)
}

// UnmarshalJSON implements the json.Unmarshaler interface. Optionals will
// marshall and unmarshall as a nullable json field. Item type must also
// implement json.Unmarshaler.
func (o *Optional[T]) UnmarshalJSON(data []byte) error {
	var v *T

	if err := sonic.Unmarshal(data, &v); err != nil {
		return err
	}

	if v == nil {
		o.exists = false
		return nil
	}

	o.exists = true
	o.value = *v
	return nil
}

func ZeroValue[T any]() T {
	var zero T
	return zero
}
