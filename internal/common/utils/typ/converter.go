package typ

// ConvertUint64ToInt64 converts a slice of uint64 to a slice of int64.
func ConvertUint64ToInt64(input []uint64) []int64 {
	if input == nil {
		return nil
	}
	result := make([]int64, len(input))
	for i, v := range input {
		result[i] = int64(v)
	}
	return result
}

// ConvertInt64ToInt converts a slice of int64 to a slice of int.
func ConvertInt64ToInt(input []int64) []int {
	if input == nil {
		return nil
	}
	result := make([]int, len(input))
	for i, v := range input {
		result[i] = int(v)
	}
	return result
}
