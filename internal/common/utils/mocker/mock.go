package mocker

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
)

// MockContext is used to create a mock context from the restful context.
func MockContext(ctx *restful.Context) context.Context {
	if mockFlag := ctx.ReadHeader(constant.MockAutoTestKey); mockFlag != constant.DoMockFlag {
		return ctx.Ctx
	}

	mockValuesList := []string{
		constant.MockRequestID,
		constant.MockSystemsKey,
		constant.MockTypeKey,
		constant.MockSpexKey,
		constant.SkipCacheKey,
	}

	var mockValues = map[string]string{}

	for _, mockValue := range mockValuesList {
		if header := ctx.ReadHeader(mockValue); header != "" {
			mockValues[mockValue] = header
		}
	}

	return context.WithValue(ctx.Ctx, constant.CtxMock, mockValues)
}

func Mock<PERSON>al<PERSON>(ctx context.Context) map[string]string {
	var headers map[string]string
	if mockOption, ok := ctx.Value(constant.CtxMock).(map[string]string); ok {
		headers = make(map[string]string)
		for mockKey, mockValue := range mockOption {
			headers[mockKey] = mockValue
			if mockKey == constant.MockRequestID && len(mockValue) > 20 && strings.Contains(mockValue, "mock") {
				mockValues := strings.Split(mockValue, ":")
				headers[mockKey] = mockValues[len(mockValues)-1]
			}
		}
	}
	return headers
}

func IsUseMock(ctx context.Context, key string) bool {
	headers := MockValues(ctx)
	if len(headers) == 0 {
		return false
	}
	systemKeys, ok := headers[constant.MockSystemsKey]
	if !ok {
		return false
	}
	for _, systemKey := range strings.Split(systemKeys, ",") {
		if key == systemKey {
			return true
		}
	}
	return false
}

func IsUseSpexMock(ctx context.Context, command string) bool {
	if envvar.IsLive(ctx) {
		return false
	}
	headers := MockValues(ctx)
	if len(headers) == 0 {
		return false
	}
	commands, ok := headers[constant.MockSpexKey]
	if !ok {
		return false
	}
	// 移除 cid
	mockCommand := strings.Split(command, "?")[0]
	for _, c := range strings.Split(commands, ",") {
		if mockCommand == c {
			return true
		}
	}
	return false
}

// IsSkipCache for test only
func IsSkipCache(ctx context.Context) bool {
	if envvar.IsLive(ctx) {
		return false
	}
	headers := MockValues(ctx)
	if len(headers) == 0 {
		return false
	}
	skipValue, ok := headers[constant.SkipCacheKey]
	if !ok {
		return false
	}
	if skipValue == "true" {
		return true
	}
	return false
}
