package common

import (
	"context"
	"encoding/hex"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/randutil"
)

const (
	traceIDSize          = 16
	typeMarkerMask uint8 = 7 << 5
	// indicates the total number of bits being used for components of the flag
	flagBitsTotal                 = 8
	flagBitsTypeMarker            = 3
	flagBitsNonTypeMarker         = flagBitsTotal - flagBitsTypeMarker
	traceIDSizeStringSize         = traceIDSize * 2
	SpanContextEncodingFormatSize = 3
	traceIDPos                    = 0
)

func GetReqTypeMakerFromXRequestId(ctx context.Context, requestId string) constant.RequestTypeMark {
	//设置typeMaker默认为空
	typeMaker := constant.DefaultTypeMark
	splitRequestId := strings.Split(requestId, ":")
	if len(splitRequestId) != SpanContextEncodingFormatSize || (len(splitRequestId) > 0 && len(splitRequestId[traceIDPos]) != traceIDSizeStringSize) {
		return typeMaker
	}

	specialFlagID, err := hex.DecodeString(splitRequestId[traceIDPos])
	if err != nil || len(specialFlagID) != traceIDSize {
		return typeMaker
	}

	typeMaker = constant.RequestTypeMark((specialFlagID[traceIDSize-1] & typeMarkerMask) >> flagBitsNonTypeMarker)
	return typeMaker
}

func HitRatioByRequestId(ctx context.Context, p int) bool {
	if p <= randutil.MinGreyPercentage {
		return false
	}
	if p >= randutil.MaxGreyPercentage {
		return true
	}
	return ctxutils.GetCtxData(ctx).IsRandomByRequest(p)
}

func HitRatioHundredByRequestId(ctx context.Context, p int) bool {
	if p <= randutil.MinGreyPercentage {
		return false
	}
	if p >= randutil.MaxGreyHundredPercentage {
		return true
	}
	return ctxutils.GetCtxData(ctx).IsRandomHundredByRequest(p)
}

func FormatUrl(url string) string {
	if len(url) == 0 {
		return ""
	}
	url = strings.Trim(strings.TrimSpace(url), "/")
	url = "/" + url
	return url
}

func InitRequestContext(
	ctx context.Context,
	confAccessor config.ConfAccessor,
	requestId string,
	logHit bool,
	requestTypeMark constant.RequestTypeMark,
	endpoint string,
) context.Context {
	logger.SetLogId(requestId)
	defer logger.UnsetLogId()
	ctx = logger.NewLogContext(ctx, requestId)
	if logHit {
		ctx = logger.WithForceLog(ctx)
	}
	ctxData := ctxutils.NewCtxData()
	ctxData.SetRequestId(requestId)
	ctxData.SetRequestTypeMark(requestTypeMark)
	ctxData.SetUrl(FormatUrl(endpoint))

	randomByRequestId := randutil.RandByString(requestId)
	ctxData.SetRandomByRequest(uint16(randomByRequestId))
	randHundredByRequestId := randutil.RandByStringHundred(requestId)
	ctxData.SetRandomHundredByRequest(uint16(randHundredByRequestId))

	contextOption := initContextOption(ctx, confAccessor, logHit, requestTypeMark, endpoint)
	ctxData.SetContextOption(contextOption)
	return ctxutils.SetCtxData(ctx, ctxData)
}

func initContextOption(ctx context.Context, confAccessor config.ConfAccessor, logHit bool,
	requestTypeMark constant.RequestTypeMark, endpoint string) ctxutils.Option {
	var contextOption ctxutils.Option
	if logHit {
		contextOption = contextOption.Set(ctxutils.LogHit)
	}
	isShadowTraffic := ctxhelper.IsShadow(ctx) || requestTypeMark.IsShadow()
	if isShadowTraffic {
		contextOption = contextOption.Set(ctxutils.ShadowTraffic)
	}
	if isShadowTraffic && !chassis_config.IsShadowLogEnabled() {
		contextOption = contextOption.Set(ctxutils.LogDisabledPrint)
	}

	isTrafficRecord := chassis_config.IsRecordEnable() && (recorder.IsRecorderContext(ctx) || recorder.ExtractReplayId(ctx) != "")
	if isTrafficRecord {
		contextOption = contextOption.Set(ctxutils.TrafficRecord)
	}

	reportDegradePercent := confAccessor.GetReportDegradeConfig(ctx).Percent
	if reportDegradePercent >= randutil.MaxGreyPercentage {
		contextOption = contextOption.Set(ctxutils.BusinessReportDegraded)
	} else if reportDegradePercent > 0 {
		reportDegradeRand := randutil.RandIntN(ctx, randutil.MaxGreyPercentage)
		if reportDegradePercent > reportDegradeRand {
			contextOption = contextOption.Set(ctxutils.BusinessReportDegraded)
		}
	}
	return contextOption
}
