package common

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
)

// GenKeyWithRegion generates a key with the region (CID) prepended to the provided keys.
// 当前会存在不同大区公用一个 cache cloud 的情况, 需要加上 cid 前缀
func GenKeyWithRegion(ctx context.Context, sep string, keys ...string) string {
	if len(keys) == 0 {
		return ""
	}
	return strings.Join(append([]string{envvar.GetCID(ctx)}, keys...), sep)
}

func <PERSON><PERSON>ey(sep string, keys ...string) string {
	if len(keys) == 0 {
		return ""
	}
	return strings.Join(keys, sep)
}
