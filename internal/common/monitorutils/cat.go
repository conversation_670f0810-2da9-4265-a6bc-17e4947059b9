package monitorutils

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
)

// BusinessAwesomeReportEvent 百分比上报
func BusinessAwesomeReportEvent(ctx context.Context, moduleName, interfaceName, status, data string) error {
	if ctxutils.IsReportDisable(ctx) {
		return nil
	}
	return monitor.AwesomeReportEvent(ctx, moduleName, interfaceName, status, data)
}

var (
	disableReportFunc monitor.ReportTransactionEndFunc = func(moduleName, interfaceName, status, data string) bool {
		return false
	}
)

func BusinessAwesomeReportStarted(ctx context.Context) (context.Context, monitor.ReportTransactionEndFunc) {
	if ctxutils.IsReportDisable(ctx) {
		return ctx, disableReportFunc
	}
	return monitor.AwesomeReportTransactionStart2(ctx)
}
