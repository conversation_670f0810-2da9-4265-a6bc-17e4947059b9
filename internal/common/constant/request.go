package constant

// Common Request Header
const (
	HeaderRequestID   = "X-Request-Id"
	HeaderRequestIP   = "X-FORWARDED-FOR"
	HeaderAccount     = "X-Account"
	HeaderToken       = "X-Token"
	HeaderJWTToken    = "JWT-Token"
	HeaderRetCode     = "Ret-Code"
	HeaderShadowToken = "X-Shadow-Token"
)

// Custom Request Header
const (
	LogHitKey = "Log-Hit"
)

// RequestTypeMark 5 - 7 上游保留字段
type RequestTypeMark uint32

const (
	// OldFormatMark To be compatible with debug flag in previous protocol
	OldFormatMark     RequestTypeMark = 0
	NormalRequestMark RequestTypeMark = 1
	// DebugRequestMark Debug request would be sampled forever and would be reported via HTTP protocol.
	DebugRequestMark RequestTypeMark = 2
	// StressTestMark It indicates the request is from normal stress test which uses the recorded traffic.
	StressTestMark RequestTypeMark = 3
	// ShadowRequest It indicates the request is from Full-Chain-Stress-Test
	ShadowRequest RequestTypeMark = 4

	// DefaultTypeMark default mark
	DefaultTypeMark RequestTypeMark = 8
)

var (
	requestTypeMarkNameMap = map[RequestTypeMark]string{
		OldFormatMark:     "0",
		NormalRequestMark: "1",
		DebugRequestMark:  "2",
		StressTestMark:    "3",
		ShadowRequest:     "4",
		DefaultTypeMark:   "8",
	}
)

func (mark RequestTypeMark) IsShadow() bool {
	return mark == StressTestMark || mark == ShadowRequest
}

func (mark RequestTypeMark) String() string {
	return requestTypeMarkNameMap[mark]
}

type Scene string

const (
	MultiAlive Scene = "multi-alive"
	DR         Scene = "dr"
)

const (
	MockHost        = "https://mockserver.test.shopeemobile.com" // nolint
	MockRequestID   = "X-Request-Id"
	MockAutoTestKey = "testMock"
	MockSystemsKey  = "sysMock"
	MockTypeKey     = "typeMock"
	MockSpexKey     = "spexMock"
	DoMockFlag      = "mock"
	CtxMock         = "ctxMock"
	MockGrpcTarget  = "grpcmock.ssc.test.shopee.sg:80"
	MockSpexTarget  = "https://mockserver.test.shopeemobile.com/" // nolint

	MockApiCheckLane = "checkLane"
	SkipCacheKey     = "skipCache"
)
