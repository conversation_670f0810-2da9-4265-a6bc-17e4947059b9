package constant

// IGS Order
const (
	IGSOrderMonitor                    = "igs_order_monitor"
	IGSOrderTypePFFShopee              = "pff_shopee"
	IGSOrderTypePFFSeller              = "pff_seller"
	IGSOrderType3PFHybrid              = "3pf_hybrid"
	IGSOrderType3PFOnly                = "3pf_only"
	IGSOrderTypeShopeeNormal           = "shopee_normal"
	IGSOrderTypeSellerNormal           = "seller_normal"
	IGSOrderTypeSellerDecoupled        = "seller_decoupled"
	IGSOrderTypeSellerMultiWarehouse   = "seller_multi_warehouse"
	IGSOrderTypeAdvanceBooking         = "advance_booking"
	IGSOrderTypeGroupShipment          = "group_shipment"
	IGSOrderTypeSplitByWeightDimension = "split_by_weight_dimension"
	IGSOrderTypeSplitToWeightDimension = "split_to_weight_dimension"
	IGSOrderTypePFFSellerMultiWH       = "pff_seller_multi_wh"
)
