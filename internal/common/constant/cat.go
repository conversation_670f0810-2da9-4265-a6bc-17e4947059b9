package constant

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
)

const (
	StatusSuccess                = monitor.StatusSuccess
	StatusError                  = monitor.StatusError
	StatusPanic                  = monitor.StatusPanic
	StatusExpired                = "expired"
	StatusNotExist               = "not-exist"
	StatusEmpty                  = "empty"
	StatusEviction               = "eviction"
	StatusNotEnable              = "not-enable"
	StatusMiss                   = "miss"
	StatusLoaderEmpty            = "load-empty"
	StatusHotKey                 = "hot-key"
	StatusNamespaceNotFound      = "namespace-not-found"
	StatusNamespaceGroupNotFound = "namespace-group-not-found"
	StatusConvertError           = "convert-error"
	StatusKeyConverterNotFound   = "convert"
	StatusUnmarshalFuncNotFound  = "unmarshal"
	StatusUnmarshalFuncError     = "unmarshal-error"
	StatusMarshalFuncNotFound    = "marshal"
	StatusMarshalFuncError       = "marshal-error"
)

// 技术模块
const (
	CatModuleRPC            = "RPC.Client"
	CatModuleURL            = "URL"
	CatModulePanic          = "Panic"
	CatModuleAPI            = "API"
	CatModuleRedis          = "Redis"
	CatModuleKafka          = "Kafka"
	CatModuleCache          = "cache"
	CatModuleLocalCache     = "LocalCache"
	CatModuleSpex           = "SpexService"
	CatModuleLruCache       = "LruCache"
	CatModuleLruCacheSet    = "LruCacheAdd"
	CatModuleRemoteCache    = "RemoteCache"
	CatModuleRemoteCacheSet = "RemoteCacheAdd"
	CatModuleLayeredCache   = "LayeredCache"
	CatModuleCacheLoader    = "CacheLoader"
)

// 业务模块
const (
	ShippingOrderProcess      = "ShippingOrderProcess"
	ShippingOrderProcessTrace = "ShippingOrderProcessTrace"
)

const (
	SalesOrderVolumeControl        = "SalesOrderVolume"
	MetricMarshalOrderPbError      = "MarshalOrderPbError"
	MetricOrderMsgPayloadIsNil     = "OrderMsgPayloadIsNil"
	MetricCountShopSalesOrderError = "CountShopSalesOrderError"
)
