package constant

const (
	FullyCoverShippingFee   = 1
	PartialCoverShippingFee = 2
	NoCoverShippingFee      = 3
)

type FulfilmentType int

const (
	FulfilmentTypeUnknown        FulfilmentType = 0
	FulfilmentTypeShopee         FulfilmentType = 1
	FulfilmentTypeSeller         FulfilmentType = 2
	FulfilmentTypeCacheSeller    FulfilmentType = 4
	FulfilmentTypeCacheWarehouse FulfilmentType = 8
)

func (f FulfilmentType) IsCacheStockFulfilmentType() bool {
	return f == FulfilmentTypeCacheSeller || f == FulfilmentTypeCacheWarehouse
}

func (f FulfilmentType) Eligible(requiredFulfilmentType FulfilmentType) bool {
	return f == requiredFulfilmentType
}
