package constant

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
)

const (
	ShopTagSBS                       = "sbs_shop"
	ShopTagFBS                       = "fbs_shop"
	ShopTagLocalRetailCB             = "local_retail_cb"
	ShopTagLocalRetail               = "retail_shop"
	ShopTagMinParcel                 = "min_parcel"
	ShopTagSellerLogisticsOrderSplit = "seller_logistics_order_split"
	ShopTagSellerPresaleVoucher      = "shopeefood_presale_voucher"
)

var (
	ShopTagPFFImprove = "pff_custom_allocation_" + envvar.GetCIDLower(context.Background())
)
