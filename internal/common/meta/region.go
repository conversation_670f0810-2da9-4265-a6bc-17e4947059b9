package meta

import (
	"strings"
)

type Region string

const (
	BR Region = "BR"
	ID Region = "ID"
	MY Region = "MY"
	PH Region = "PH"
	SG Region = "SG"
	TH Region = "TH"
	TW Region = "TW"
	VN Region = "VN"
	MX Region = "MX"
	AR Region = "AR"
	PL Region = "PL"
	FR Region = "FR"
	ES Region = "ES"
	IN Region = "IN"
	CO Region = "CO"
	CL Region = "CL"
)

var (
	shopeeDomainSuffix = map[Region]string{
		SG: "sg",
		VN: "vn",
		PH: "ph",
		TW: "tw",
		ID: "co.id",
		TH: "co.th",
		MY: "com.my",
		BR: "com.br",
		MX: "com.mx",
		AR: "com.ar",
		PL: "pl",
		ES: "es",
		FR: "fr",
		IN: "in",
		CO: "com.co",
		CL: "cl",
	}
)

func (r Region) Lower() string {
	return strings.ToLower(string(r))
}

func GetSupportedRegion() []Region {
	return []Region{BR, ID, MY, PH, SG, TH, TW, VN, MX, AR, PL, FR, ES, IN, CO, CL}
}

func GetDomainFromRegion(region Region) string {
	return shopeeDomainSuffix[region]
}
