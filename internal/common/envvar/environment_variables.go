package envvar

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/recordutil"
)

var (
	cid      meta.Region // 对应环境变量中的 CID
	cidLower meta.Region // 对应环境变量中的 CID
	env      meta.Env    // 对应环境变量中的 ENV
	envLower meta.Env    // 对应环境变量中的 ENV
	module   meta.Module // 对应环境变量中的 MODULE_NAME
	idc      string      // 对应环境变量中的 IDC
)

func init() {
	ctx := context.Background()
	env = strings.ToUpper(recorder.Getenv(ctx, "ENV"))
	envLower = strings.ToLower(env)
	cid = meta.Region(strings.ToUpper(recorder.Getenv(ctx, "CID")))
	cidLower = meta.Region(strings.ToLower(string(cid)))
	module = strings.ToUpper(recorder.Getenv(ctx, "MODULE_NAME"))
	idc = strings.ToLower(recorder.Getenv(ctx, "IDC"))
}

var (
	getEnvWithCtx         = recordutil.Wrap(getEnv)
	getEnvLowerWithCtx    = recordutil.Wrap(getEnvLower)
	getRegionWithCtx      = recordutil.Wrap(getRegion)
	getRegionLowerWithCtx = recordutil.Wrap(getRegionLower)
	getModuleNameWithCtx  = recordutil.Wrap(getModuleName)
	getIdcWithCtx         = recordutil.Wrap(getIdc)
)

func GetEnv(ctx context.Context) meta.Env {
	if chassis_config.IsRecordEnable() {
		return getEnvWithCtx(ctx)
	}
	return env
}

func GetEnvLower(ctx context.Context) meta.Env {
	if chassis_config.IsRecordEnable() {
		return getEnvLowerWithCtx(ctx)
	}
	return envLower
}

func getEnv(ctx context.Context) meta.Env {
	return env
}

func getEnvLower(ctx context.Context) meta.Env {
	return envLower
}

func IsLive(ctx context.Context) bool {
	return GetEnv(ctx) == meta.LIVE
}

func IsLivetest(ctx context.Context) bool {
	return strings.Contains(GetModuleName(ctx), "LIVETEST")
}

func GetModuleName(ctx context.Context) meta.Module {
	if chassis_config.IsRecordEnable() {
		return getModuleNameWithCtx(ctx)
	}
	return getModuleName(ctx)
}

func getModuleName(ctx context.Context) meta.Module {
	return module
}

func GetCID(ctx context.Context) string {
	if chassis_config.IsRecordEnable() {
		return string(getRegionWithCtx(ctx))
	}
	return string(cid)
}

func GetCIDLower(ctx context.Context) string {
	if chassis_config.IsRecordEnable() {
		return string(getRegionLowerWithCtx(ctx))
	}
	return string(cidLower)
}

func GetRegion(ctx context.Context) meta.Region {
	if chassis_config.IsRecordEnable() {
		return getRegionWithCtx(ctx)
	}
	return cid
}

func getRegion(ctx context.Context) meta.Region {
	return cid
}

func getRegionLower(context.Context) meta.Region {
	return cidLower
}

func IsIDCUs3(ctx context.Context) bool {
	return GetIDC(ctx) == "us3"
}

func GetIDC(ctx context.Context) string {
	if chassis_config.IsRecordEnable() {
		return getIdcWithCtx(ctx)
	}
	return idc
}

func getIdc(ctx context.Context) string {
	return idc
}
