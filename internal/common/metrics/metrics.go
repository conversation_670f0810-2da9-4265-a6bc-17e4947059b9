package metrics

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
)

type IGSOrderMetrics struct {
	OrderType string `json:"order_type"`
}

func GenerateIGSOrderMetrics(ctx context.Context, orderType string, count int) {
	f := func(ctx context.Context, orderType string) map[string]string {
		return map[string]string{
			"order_type": orderType,
		}
	}
	_ = CounterAdd(ctx, constant.IGSOrderMonitor, float64(count), f(ctx, orderType))
}
