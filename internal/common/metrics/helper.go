package metrics

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
)

func BusinessCounterAdd(ctx context.Context, name string, val float64, labels map[string]string) error {
	if ctxutils.IsReportDisable(ctx) {
		return nil
	}
	return CounterAdd(ctx, name, val, labels)
}

func BusinessCounterIncr(ctx context.Context, name string, labels map[string]string) error {
	if ctxutils.IsReportDisable(ctx) {
		return nil
	}
	return CounterIncr(ctx, name, labels)
}

func BusinessGaugeSet(ctx context.Context, name string, val float64, labels map[string]string) error {
	if ctxutils.IsReportDisable(ctx) {
		return nil
	}
	return GaugeSet(ctx, name, val, labels)
}

func BusinessHistogramObserve(ctx context.Context, name string, val float64, labels map[string]string) error {
	if ctxutils.IsReportDisable(ctx) {
		return nil
	}
	return HistogramObserve(ctx, name, val, labels)
}

func CounterAdd(ctx context.Context, name string, val float64, labels map[string]string) error {
	labels = getLabels(ctx, labels)
	return metrics.CounterAdd(name, val, labels)
}

func CounterIncr(ctx context.Context, name string, labels map[string]string) error {
	labels = getLabels(ctx, labels)
	return metrics.CounterIncr(name, labels)
}

func GaugeSet(ctx context.Context, name string, val float64, labels map[string]string) error {
	labels = getLabels(ctx, labels)
	return metrics.GaugeSet(name, val, labels)
}

func HistogramObserve(ctx context.Context, name string, val float64, labels map[string]string) error {
	labels = getLabels(ctx, labels)
	return metrics.HistogramObserve(name, val, labels)
}

func getLabels(ctx context.Context, labels map[string]string) map[string]string {
	if len(labels) == 0 {
		labels = make(map[string]string)
	}
	if len(labels["type_mark"]) == 0 {
		labels["type_mark"] = ctxutils.GetCtxData(ctx).GetRequestTypeMarkString()
	}
	return labels
}
