package fsserr

type Error interface {
	error
	Code() int
	Message() string
	Detail() string
	Unwrap() error
	Reference() Reference
	WithReference(r Reference) Error
	ExtraData() interface{}
	WithExtraData(data interface{}) Error
	Use(code FssCode) bool
}

func ExtraData[T any](e Error) (T, bool) {
	if e == nil || e.Code() == 0 {
		var zero T
		return zero, false
	}
	data, ok := e.ExtraData().(T)
	if !ok {
		var zero T
		return zero, false
	}
	return data, true
}
