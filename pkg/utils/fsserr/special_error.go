package fsserr

import (
	"errors"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

func IsSpecialError[T error](err Error) (T, bool) {
	if err == nil {
		var t T
		return t, false
	}
	var t T
	ok := errors.As(err, &t)
	return t, ok
}

type (
	OutOfStockErrorData struct {
		ItemId             typ.ItemIdType
		IsPackagePromotion bool
		ItemStockInfos     []ItemStockInfo
	}

	ItemStockInfo struct {
		ItemId         typ.ItemIdType
		ModelId        typ.ModelIdType
		ItemGroupId    uint64
		AvailableStock uint32
		ShopId         uint64
	}
)

func NewOutOfStockError(errorData OutOfStockErrorData) Error {
	code := OutOfStockErr
	if errorData.IsPackagePromotion {
		code = PackagePromotionOutOfStockErr
	}
	return New(code, "out of stock, item_id: %d, is_package_Promotion: %v",
		errorData.ItemId, errorData.IsPackagePromotion).WithExtraData(errorData)
}

func (e *OutOfStockErrorData) Error() string {
	return fmt.Sprintf(
		"isPackagePromotion=%t",
		e.IsPackagePromotion,
	)
}

var (
	Err3PFShopNoWH = New(ThreePFShopNoWarehouseErr, "3pf shop has no warehouse")

	ErrSellerWHAddressNotFound = New(SellerWarehouseAddressNotFoundErr,
		"seller WH address_id not found from dependency data")

	ErrWeightAndDimensionLimit = New(WeightAndDimensionLimitErr, "One item exceeds weight or height limitation")
)
