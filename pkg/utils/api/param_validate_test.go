package api

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 测试用的请求结构体
type TestRequest struct {
	UserID   uint64   `json:"user_id" validate:"required"`
	Name     string   `json:"name" validate:"required,min=1,max=100"`
	Age      int      `json:"age" validate:"min=0,max=150"`
	Email    string   `json:"email" validate:"omitempty,email"`
	IsActive bool     `json:"is_active"`
	Tags     []string `json:"tags"`
	Score    float64  `json:"score"`
}

type TestRequestWithTime struct {
	CreatedAt time.Time `time_format:"2006-01-02 15:04:05"`
	UpdatedAt time.Time `time_format:"2006-01-02" time_utc:"true"`
}

func TestConvertBaseType(t *testing.T) {
	testCases := []struct {
		name     string
		input    interface{}
		expected interface{}
		hasError bool
	}{
		{"string to string", "test", "test", false},
		{"string to int", "123", 123, false},
		{"string to int64", "123", int64(123), false},
		{"string to uint64", "123", uint64(123), false},
		{"string to float64", "123.45", 123.45, false},
		{"string to bool true", "true", true, false},
		{"string to bool false", "false", false, false},
		{"invalid int", "abc", 0, true},
		{"invalid float", "abc", 0.0, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var result interface{}
			var err error

			switch tc.expected.(type) {
			case string:
				result, err = ConvertBaseType(reflect.TypeOf(""), tc.input)
			case int:
				result, err = ConvertBaseType(reflect.TypeOf(0), tc.input)
			case int64:
				result, err = ConvertBaseType(reflect.TypeOf(int64(0)), tc.input)
			case uint64:
				result, err = ConvertBaseType(reflect.TypeOf(uint64(0)), tc.input)
			case float64:
				result, err = ConvertBaseType(reflect.TypeOf(0.0), tc.input)
			case bool:
				result, err = ConvertBaseType(reflect.TypeOf(false), tc.input)
			}

			if tc.hasError {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
				assert.Equal(t, tc.expected, result)
			}
		})
	}
}

func TestReadURLQuery(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name     string
		queries  map[string][]string
		expected TestRequest
	}{
		{
			name: "基本查询参数解析",
			queries: map[string][]string{
				"user_id":   {"123"},
				"name":      {"John"},
				"age":       {"25"},
				"email":     {"<EMAIL>"},
				"is_active": {"true"},
				"tags":      {"tag1", "tag2"},
				"score":     {"85.5"},
			},
			expected: TestRequest{
				UserID:   123,
				Name:     "John",
				Age:      25,
				Email:    "<EMAIL>",
				IsActive: true,
				Tags:     []string{"tag1", "tag2"},
				Score:    85.5,
			},
		},
		{
			name: "部分参数",
			queries: map[string][]string{
				"user_id":   {"456"},
				"name":      {"Jane"},
				"is_active": {"false"},
			},
			expected: TestRequest{
				UserID:   456,
				Name:     "Jane",
				IsActive: false,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var req TestRequest
			err := readURLQuery(ctx, tc.queries, &req)

			assert.Nil(t, err)
			assert.Equal(t, tc.expected.UserID, req.UserID)
			assert.Equal(t, tc.expected.Name, req.Name)
			assert.Equal(t, tc.expected.Age, req.Age)
			assert.Equal(t, tc.expected.Email, req.Email)
			assert.Equal(t, tc.expected.IsActive, req.IsActive)
			assert.Equal(t, tc.expected.Tags, req.Tags)
			assert.Equal(t, tc.expected.Score, req.Score)
		})
	}
}

func TestValidation(t *testing.T) {
	testCases := []struct {
		name        string
		req         TestRequest
		expectError bool
		errorField  string
	}{
		{
			name: "有效数据",
			req: TestRequest{
				UserID: 123,
				Name:   "Test",
				Age:    25,
				Email:  "<EMAIL>",
			},
			expectError: false,
		},
		{
			name: "缺少必填字段user_id",
			req: TestRequest{
				Name: "Test",
			},
			expectError: true,
			errorField:  "UserID",
		},
		{
			name: "缺少必填字段name",
			req: TestRequest{
				UserID: 123,
			},
			expectError: true,
			errorField:  "Name",
		},
		{
			name: "年龄超出范围",
			req: TestRequest{
				UserID: 123,
				Name:   "Test",
				Age:    200,
			},
			expectError: true,
			errorField:  "Age",
		},
		{
			name: "邮箱格式错误",
			req: TestRequest{
				UserID: 123,
				Name:   "Test",
				Email:  "invalid-email",
			},
			expectError: true,
			errorField:  "Email",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := validate.Struct(&tc.req)

			if tc.expectError {
				assert.NotNil(t, err)
				formattedErr := arrangeValidateError(err)
				assert.Contains(t, formattedErr.Error(), tc.errorField)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func TestMapForm(t *testing.T) {
	form := map[string][]string{
		"user_id":   {"101"},
		"name":      {"Bob"},
		"age":       {"35"},
		"email":     {"<EMAIL>"},
		"is_active": {"false"},
		"tags":      {"admin", "manager"},
		"score":     {"88.0"},
	}

	var req TestRequest
	err := mapForm(&req, form, "json")

	assert.Nil(t, err)
	assert.Equal(t, uint64(101), req.UserID)
	assert.Equal(t, "Bob", req.Name)
	assert.Equal(t, 35, req.Age)
	assert.Equal(t, "<EMAIL>", req.Email)
	assert.False(t, req.IsActive)
	assert.Equal(t, []string{"admin", "manager"}, req.Tags)
	assert.Equal(t, 88.0, req.Score)
}

func TestSetFieldsWithProperType(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected interface{}
		setter   func(string, reflect.Value) error
	}{
		{
			name:     "设置整型字段",
			input:    "123",
			expected: int64(123),
			setter: func(val string, field reflect.Value) error {
				return setIntField(val, 64, field)
			},
		},
		{
			name:     "设置无符号整型字段",
			input:    "456",
			expected: uint64(456),
			setter: func(val string, field reflect.Value) error {
				return setUintField(val, 64, field)
			},
		},
		{
			name:     "设置布尔型字段",
			input:    "true",
			expected: true,
			setter: func(val string, field reflect.Value) error {
				return setBoolField(val, field)
			},
		},
		{
			name:     "设置浮点型字段",
			input:    "123.45",
			expected: 123.45,
			setter: func(val string, field reflect.Value) error {
				return setFloatField(val, 64, field)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var result interface{}

			switch tc.expected.(type) {
			case int64:
				var val int64
				field := reflect.ValueOf(&val).Elem()
				err := tc.setter(tc.input, field)
				assert.Nil(t, err)
				result = val
			case uint64:
				var val uint64
				field := reflect.ValueOf(&val).Elem()
				err := tc.setter(tc.input, field)
				assert.Nil(t, err)
				result = val
			case bool:
				var val bool
				field := reflect.ValueOf(&val).Elem()
				err := tc.setter(tc.input, field)
				assert.Nil(t, err)
				result = val
			case float64:
				var val float64
				field := reflect.ValueOf(&val).Elem()
				err := tc.setter(tc.input, field)
				assert.Nil(t, err)
				result = val
			}

			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestSetTimeField(t *testing.T) {
	// 测试时间字段设置
	timeFormat := "2006-01-02 15:04:05"
	timeValue := "2023-12-01 15:30:45"

	// 创建结构体字段
	var testStruct TestRequestWithTime
	structType := reflect.TypeOf(testStruct)
	field, _ := structType.FieldByName("CreatedAt")
	value := reflect.ValueOf(&testStruct.CreatedAt).Elem()

	err := setTimeField(timeValue, field, value)

	assert.Nil(t, err)
	expectedTime, _ := time.ParseInLocation(timeFormat, timeValue, time.Local)
	assert.Equal(t, expectedTime, testStruct.CreatedAt)
}

func TestConvert(t *testing.T) {
	testCases := []struct {
		name     string
		input    []string
		expected interface{}
		typeKind reflect.Kind
		hasError bool
	}{
		{
			name:     "转换整数",
			input:    []string{"123"},
			expected: 123,
			typeKind: reflect.Int,
			hasError: false,
		},
		{
			name:     "转换字符串数组",
			input:    []string{"tag1", "tag2"},
			expected: []string{"tag1", "tag2"},
			typeKind: reflect.Slice,
			hasError: false,
		},
		{
			name:     "转换布尔值",
			input:    []string{"true"},
			expected: true,
			typeKind: reflect.Bool,
			hasError: false,
		},
		{
			name:     "转换浮点数",
			input:    []string{"123.45"},
			expected: 123.45,
			typeKind: reflect.Float64,
			hasError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var targetValue reflect.Value

			switch tc.typeKind {
			case reflect.Int:
				var val int
				targetValue = reflect.ValueOf(&val).Elem()
			case reflect.Bool:
				var val bool
				targetValue = reflect.ValueOf(&val).Elem()
			case reflect.Float64:
				var val float64
				targetValue = reflect.ValueOf(&val).Elem()
			case reflect.Slice:
				var val []string
				targetValue = reflect.ValueOf(&val).Elem()
			}

			err := convert(tc.input, targetValue)

			if tc.hasError {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
				result := targetValue.Interface()
				assert.Equal(t, tc.expected, result)
			}
		})
	}
}

func TestArrangeValidateError(t *testing.T) {
	// 测试正常错误（非validator错误）
	normalErr := assert.AnError
	result := arrangeValidateError(normalErr)
	assert.Equal(t, normalErr, result)

	// 测试validator错误需要实际的validator.ValidationErrors
	req := TestRequest{} // 空请求，会触发required验证错误
	err := validate.Struct(&req)

	if err != nil {
		formattedErr := arrangeValidateError(err)
		assert.NotNil(t, formattedErr)
		assert.Contains(t, formattedErr.Error(), "param:")
	}
}

func TestSetWithProperType(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected interface{}
		testType reflect.Type
	}{
		{"设置字符串", "test", "test", reflect.TypeOf("")},
		{"设置整数", "123", int64(123), reflect.TypeOf(int64(0))},
		{"设置浮点数", "123.45", 123.45, reflect.TypeOf(float64(0))},
		{"设置布尔值", "true", true, reflect.TypeOf(false)},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			switch tc.testType.Kind() {
			case reflect.String:
				var val string
				field := reflect.ValueOf(&val).Elem()
				err := setWithProperType(tc.testType, tc.input, field)
				assert.Nil(t, err)
				assert.Equal(t, tc.expected, val)
			case reflect.Int64:
				var val int64
				field := reflect.ValueOf(&val).Elem()
				err := setWithProperType(tc.testType, tc.input, field)
				assert.Nil(t, err)
				assert.Equal(t, tc.expected, val)
			case reflect.Float64:
				var val float64
				field := reflect.ValueOf(&val).Elem()
				err := setWithProperType(tc.testType, tc.input, field)
				assert.Nil(t, err)
				assert.Equal(t, tc.expected, val)
			case reflect.Bool:
				var val bool
				field := reflect.ValueOf(&val).Elem()
				err := setWithProperType(tc.testType, tc.input, field)
				assert.Nil(t, err)
				assert.Equal(t, tc.expected, val)
			}
		})
	}
}

// 基准测试
func BenchmarkConvertBaseType(b *testing.B) {
	for i := 0; i < b.N; i++ {
		ConvertBaseType(reflect.TypeOf(0), "123")
	}
}

func BenchmarkValidation(b *testing.B) {
	req := TestRequest{
		UserID:   123,
		Name:     "Test",
		Age:      25,
		Email:    "<EMAIL>",
		IsActive: true,
		Tags:     []string{"tag1", "tag2"},
		Score:    85.5,
	}

	for i := 0; i < b.N; i++ {
		validate.Struct(&req)
	}
}

func BenchmarkReadURLQuery(b *testing.B) {
	ctx := context.Background()
	queries := map[string][]string{
		"user_id":   {"123"},
		"name":      {"John"},
		"age":       {"25"},
		"email":     {"<EMAIL>"},
		"is_active": {"true"},
		"tags":      {"tag1", "tag2"},
		"score":     {"85.5"},
	}

	for i := 0; i < b.N; i++ {
		var req TestRequest
		readURLQuery(ctx, queries, &req)
	}
}

// 新增测试函数

func TestSchemaValidate(t *testing.T) {
	t.Run("验证成功", func(t *testing.T) {
		// 创建一个简单的测试结构体
		testStruct := &TestRequest{
			UserID: 123,
			Name:   "test",
		}

		// 由于TestRequest没有实现PBSchema接口，我们需要创建一个简单的测试
		// 这里我们直接测试验证逻辑
		err := validate.Struct(testStruct)
		assert.Nil(t, err)
	})

	t.Run("验证失败", func(t *testing.T) {
		// 创建一个无效的测试结构体
		testStruct := &TestRequest{
			// 缺少必填字段
		}

		err := validate.Struct(testStruct)
		assert.NotNil(t, err)
	})
}

func TestParseURLQuery(t *testing.T) {
	t.Run("解析URL查询参数", func(t *testing.T) {
		ctx := context.Background()
		queries := map[string][]string{
			"user_id": {"123"},
			"name":    {"test"},
			"age":     {"25"},
		}

		var req TestRequest
		err := readURLQuery(ctx, queries, &req)

		assert.Nil(t, err)
		assert.Equal(t, uint64(123), req.UserID)
		assert.Equal(t, "test", req.Name)
		assert.Equal(t, 25, req.Age)
	})
}

func TestParseJSONData(t *testing.T) {
	t.Run("解析JSON数据", func(t *testing.T) {
		// 这个测试需要真实的restful context，我们只测试基本逻辑
		// 在实际项目中，这应该使用集成测试
		assert.True(t, true) // 占位符测试
	})
}

func TestParseFormData(t *testing.T) {
	t.Run("解析form数据", func(t *testing.T) {
		// 这个测试需要真实的restful context，我们只测试基本逻辑
		// 在实际项目中，这应该使用集成测试
		assert.True(t, true) // 占位符测试
	})
}

func TestParseProtobufRequest(t *testing.T) {
	t.Run("解析protobuf请求", func(t *testing.T) {
		// 这个测试需要真实的protobuf消息，我们只测试基本逻辑
		// 在实际项目中，这应该使用集成测试
		assert.True(t, true) // 占位符测试
	})
}
