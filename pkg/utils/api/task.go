package api

import (
	"context"

	"git.garena.com/shopee/bg-logistics/service/saturn-rpc-job/go/lib/entity"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

func WriteSaturnReply(ctx context.Context, err fsserr.Error) *entity.SaturnReply {
	if err != nil {
		return &entity.SaturnReply{
			Retcode: err.Code(),
			Message: err.Message(),
		}
	}
	return &entity.SaturnReply{
		Retcode: 0,
		Message: "success",
	}
}
