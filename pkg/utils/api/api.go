package api

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

const (
	ContentTypeJson string = "application/json; charset=utf-8"

	ApplicationJSON = "application/json"
	ApplicationForm = "application/x-www-form-urlencoded"
)

type HttpResponse struct {
	Retcode     int         `json:"retcode"`
	Message     string      `json:"message"`
	Detail      string      `json:"detail"`
	Data        interface{} `json:"data,omitempty"`
	Token       string      `json:"token,omitempty"`
	TraceNumber string      `json:"trace_number,omitempty"`
}

func WriteJsonResp(ctx *restful.Context, data interface{}, err fsserr.Error) {
	if err != nil {
		resp := WrapErrRsp(ctx.Ctx, err, nil)
		_ = ctx.WriteJSON(resp, ContentTypeJson)
		return
	}
	resp := WrapSuccessRsp(data)
	_ = ctx.WriteJSON(resp, ContentTypeJson)
}

func WrapSuccessRsp(data interface{}) *HttpResponse {
	return &HttpResponse{
		Retcode: 0,
		Message: "success",
		Data:    data,
	}
}

func WrapErrRsp(ctx context.Context, err fsserr.Error, data interface{}) *HttpResponse {
	resp := &HttpResponse{
		Retcode: err.Code(),
		Message: err.Message(),
		Detail:  err.Error(),
		Data:    data,
	}
	return resp
}
