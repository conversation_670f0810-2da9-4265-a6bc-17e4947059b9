package remote_cache

import (
	"context"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/go-redis"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

// BenchmarkData 用于 benchmark 测试的结构体
type BenchmarkData struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Age  int    `json:"age"`
}

// BenchmarkProtoMessage 用于 benchmark 测试的 protobuf 消息
type BenchmarkProtoMessage struct {
	ID   int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (m *BenchmarkProtoMessage) Reset()         { *m = BenchmarkProtoMessage{} }
func (m *BenchmarkProtoMessage) String() string { return "" }
func (m *BenchmarkProtoMessage) ProtoMessage()  {}

// createBenchmarkRedisClient 创建用于 benchmark 的 Redis 客户端
func createBenchmarkRedisClient() *redis.Client {
	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	return client
}

// BenchmarkRemoteCache_StringSet 字符串类型 Set 操作性能测试
func BenchmarkRemoteCache_StringSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewStrRemoteCache[string, string](
		"bench_string_set",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i)
		value := "bench_value_" + key
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_StringGet 字符串类型 Get 操作性能测试
func BenchmarkRemoteCache_StringGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewStrRemoteCache[string, string](
		"bench_string_get",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	for i := 0; i < 1000; i++ {
		key := typ.NumberToString(i)
		value := "bench_value_" + key
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i % 1000)
		_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_StringMSet 字符串类型 MSet 操作性能测试
func BenchmarkRemoteCache_StringMSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewStrRemoteCache[string, string](
		"bench_string_mset",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		values := make(map[string]string, 10)
		for j := 0; j < 10; j++ {
			key := typ.NumberToString(i*10 + j)
			values[key] = "bench_value_" + key
		}

		_, err := c.MSet(ctx, values, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_StringMGet 字符串类型 MGet 操作性能测试
func BenchmarkRemoteCache_StringMGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewStrRemoteCache[string, string](
		"bench_string_mget",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	for i := 0; i < 1000; i++ {
		key := typ.NumberToString(i)
		value := "bench_value_" + key
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := make([]string, 10)
		for j := 0; j < 10; j++ {
			keys[j] = typ.NumberToString((i*10 + j) % 1000)
		}

		_, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_NumberSet 数字类型 Set 操作性能测试
func BenchmarkRemoteCache_NumberSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewNumberRemoteCache[string, int](
		"bench_number_set",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i)
		value := i
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, int](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_NumberGet 数字类型 Get 操作性能测试
func BenchmarkRemoteCache_NumberGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewNumberRemoteCache[string, int](
		"bench_number_get",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	for i := 0; i < 1000; i++ {
		key := typ.NumberToString(i)
		err := c.Set(ctx, key, i, cache.WithKeyConvertor[string, int](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i % 1000)
		_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, int](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_BoolSet 布尔类型 Set 操作性能测试
func BenchmarkRemoteCache_BoolSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewBoolRemoteCache[string, bool](
		"bench_bool_set",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i)
		value := i%2 == 0
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, bool](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_BoolGet 布尔类型 Get 操作性能测试
func BenchmarkRemoteCache_BoolGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewBoolRemoteCache[string, bool](
		"bench_bool_get",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	for i := 0; i < 1000; i++ {
		key := typ.NumberToString(i)
		value := i%2 == 0
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, bool](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i % 1000)
		_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, bool](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_GenericSet 通用类型 Set 操作性能测试
func BenchmarkRemoteCache_GenericSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewRemoteCache[string, BenchmarkData](
		"bench_generic_set",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i)
		value := BenchmarkData{
			ID:   i,
			Name: "bench_name_" + typ.NumberToString(i),
			Age:  20 + i%50,
		}
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, BenchmarkData](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_GenericGet 通用类型 Get 操作性能测试
func BenchmarkRemoteCache_GenericGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewRemoteCache[string, BenchmarkData](
		"bench_generic_get",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	for i := 0; i < 1000; i++ {
		key := typ.NumberToString(i)
		value := BenchmarkData{
			ID:   i,
			Name: "bench_name_" + typ.NumberToString(i),
			Age:  20 + i%50,
		}
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, BenchmarkData](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i % 1000)
		_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, BenchmarkData](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_ProtoSet Protobuf 类型 Set 操作性能测试
func BenchmarkRemoteCache_ProtoSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewProtoRemoteCache[string, *BenchmarkProtoMessage](
		"bench_proto_set",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i)
		value := &BenchmarkProtoMessage{
			ID:   int32(i),
			Name: "bench_proto_name_" + typ.NumberToString(i),
		}
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, *BenchmarkProtoMessage](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_ProtoGet Protobuf 类型 Get 操作性能测试
func BenchmarkRemoteCache_ProtoGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewProtoRemoteCache[string, *BenchmarkProtoMessage](
		"bench_proto_get",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	for i := 0; i < 1000; i++ {
		key := typ.NumberToString(i)
		value := &BenchmarkProtoMessage{
			ID:   int32(i),
			Name: "bench_proto_name_" + typ.NumberToString(i),
		}
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, *BenchmarkProtoMessage](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		key := typ.NumberToString(i % 1000)
		_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, *BenchmarkProtoMessage](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRemoteCache_ConcurrentSet 并发 Set 操作性能测试
func BenchmarkRemoteCache_ConcurrentSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewStrRemoteCache[string, string](
		"bench_concurrent_set",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := typ.NumberToString(i)
			value := "concurrent_value_" + key
			err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
			if err != nil {
				b.Fatal(err)
			}
			i++
		}
	})
}

// BenchmarkRemoteCache_ConcurrentGet 并发 Get 操作性能测试
func BenchmarkRemoteCache_ConcurrentGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewStrRemoteCache[string, string](
		"bench_concurrent_get",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	for i := 0; i < 10000; i++ {
		key := typ.NumberToString(i)
		value := "concurrent_value_" + key
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := typ.NumberToString(i % 10000)
			_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
			if err != nil {
				b.Fatal(err)
			}
			i++
		}
	})
}

// BenchmarkRemoteCache_MixedOperations 混合操作性能测试
func BenchmarkRemoteCache_MixedOperations(b *testing.B) {
	client := createBenchmarkRedisClient()
	defer client.Close()

	c := NewStrRemoteCache[string, string](
		"bench_mixed_ops",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// 混合 Set 和 Get 操作
		key := typ.NumberToString(i)
		value := "mixed_value_" + key

		// Set 操作
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}

		// Get 操作
		_, err = c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatal(err)
		}

		// 批量操作
		if i%10 == 0 {
			batchValues := make(map[string]string, 5)
			for j := 0; j < 5; j++ {
				batchKey := typ.NumberToString(i + j)
				batchValues[batchKey] = "batch_value_" + batchKey
			}

			_, err = c.MSet(ctx, batchValues, cache.WithKeyConvertor[string, string](keyConverter))
			if err != nil {
				b.Fatal(err)
			}
		}
	}
}
