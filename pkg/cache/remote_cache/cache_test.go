package remote_cache

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

// TestData 用于测试的结构体
type TestData struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Age  int    `json:"age"`
}

// TestProtoMessage 用于测试的 protobuf 消息
type TestProtoMessage struct {
	ID   int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (m *TestProtoMessage) Reset()         { *m = TestProtoMessage{} }
func (m *TestProtoMessage) String() string { return proto.CompactTextString(m) }
func (m *TestProtoMessage) ProtoMessage()  {}

// createMockRedisClient 创建一个简单的 mock Redis 客户端
func createMockRedisClient() *redis.Client {
	// 使用标准的 redis.NewClient 而不是通过配置系统
	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	return client
}

func TestNewRemoteCache(t *testing.T) {
	// 创建简单的 mock Redis 客户端
	client := createMockRedisClient()
	require.NotNil(t, client)

	t.Run("创建字符串缓存", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_string_cache",
			client,
		)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_string_cache"), c.Name())
	})

	t.Run("创建数字缓存", func(t *testing.T) {
		c := NewNumberRemoteCache[string, int](
			"test_number_cache",
			client,
		)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_number_cache"), c.Name())
	})

	t.Run("创建布尔缓存", func(t *testing.T) {
		c := NewBoolRemoteCache[string, bool](
			"test_bool_cache",
			client,
		)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_bool_cache"), c.Name())
	})

	t.Run("创建 protobuf 缓存", func(t *testing.T) {
		c := NewProtoRemoteCache[string, *TestProtoMessage](
			"test_proto_cache",
			client,
		)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_proto_cache"), c.Name())
	})

	t.Run("创建通用缓存", func(t *testing.T) {
		c := NewRemoteCache[string, TestData](
			"test_generic_cache",
			client,
		)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_generic_cache"), c.Name())
	})
}

func TestRemoteCache_StringOperations(t *testing.T) {
	client := createMockRedisClient()

	c := NewStrRemoteCache[string, string](
		"test_string_ops",
		client,
		WithTimeout[string](5*time.Second),
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("Set 和 Get 操作", func(t *testing.T) {
		key := "test_key"
		value := "test_value"

		// 设置值
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		// 获取值
		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("获取不存在的键", func(t *testing.T) {
		key := "non_existent_key"
		_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrKeyNotFound, err)
	})

	t.Run("MGet 和 MSet 操作", func(t *testing.T) {
		keys := []string{"key1", "key2", "key3"}
		values := map[string]string{
			"key1": "value1",
			"key2": "value2",
			"key3": "value3",
		}

		// 批量设置
		successCount, err := c.MSet(ctx, values, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 3, successCount)

		// 批量获取
		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, values, result)
	})
}

func TestRemoteCache_NumberOperations(t *testing.T) {
	client := createMockRedisClient()

	c := NewNumberRemoteCache[string, int](
		"test_number_ops",
		client,
		WithTimeout[int](5*time.Second),
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("数字类型 Set 和 Get", func(t *testing.T) {
		key := "number_key"
		value := 42

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("数字类型 MGet 和 MSet", func(t *testing.T) {
		keys := []string{"num1", "num2", "num3"}
		values := map[string]int{
			"num1": 100,
			"num2": 200,
			"num3": 300,
		}

		successCount, err := c.MSet(ctx, values, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 3, successCount)

		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, values, result)
	})
}

func TestRemoteCache_BoolOperations(t *testing.T) {
	client := createMockRedisClient()

	c := NewBoolRemoteCache[string, bool](
		"test_bool_ops",
		client,
		WithTimeout[bool](5*time.Second),
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("布尔类型 Set 和 Get", func(t *testing.T) {
		key := "bool_key"
		value := true

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, bool](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, bool](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("布尔类型 MGet 和 MSet", func(t *testing.T) {
		keys := []string{"bool1", "bool2"}
		values := map[string]bool{
			"bool1": true,
			"bool2": false,
		}

		successCount, err := c.MSet(ctx, values, cache.WithKeyConvertor[string, bool](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 2, successCount)

		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, bool](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, values, result)
	})
}

func TestRemoteCache_ProtoOperations(t *testing.T) {
	client := createMockRedisClient()

	c := NewProtoRemoteCache[string, *TestProtoMessage](
		"test_proto_ops",
		client,
		WithTimeout[*TestProtoMessage](5*time.Second),
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("Protobuf 类型 Set 和 Get", func(t *testing.T) {
		key := "proto_key"
		value := &TestProtoMessage{
			ID:   123,
			Name: "test_proto",
		}

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, value.ID, result.ID)
		assert.Equal(t, value.Name, result.Name)
	})

	t.Run("Protobuf 类型 MGet 和 MSet", func(t *testing.T) {
		keys := []string{"proto1", "proto2"}
		values := map[string]*TestProtoMessage{
			"proto1": {ID: 1, Name: "message1"},
			"proto2": {ID: 2, Name: "message2"},
		}

		successCount, err := c.MSet(ctx, values, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 2, successCount)

		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.Len(t, result, 2)

		for key, expectedValue := range values {
			actualValue, exists := result[key]
			assert.True(t, exists, "Key %s should exist", key)
			assert.NotNil(t, actualValue)
			assert.Equal(t, expectedValue.ID, actualValue.ID)
			assert.Equal(t, expectedValue.Name, actualValue.Name)
		}
	})

	t.Run("Protobuf 空值处理", func(t *testing.T) {
		key := "empty_proto_key"
		value := &TestProtoMessage{
			ID:   0,
			Name: "",
		}

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, value.ID, result.ID)
		assert.Equal(t, value.Name, result.Name)
	})
}

func TestRemoteCache_GenericOperations(t *testing.T) {
	client := createMockRedisClient()

	c := NewRemoteCache[string, TestData](
		"test_generic_ops",
		client,
		WithTimeout[TestData](5*time.Second),
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("通用类型 Set 和 Get", func(t *testing.T) {
		key := "generic_key"
		value := TestData{
			ID:   1,
			Name: "test_name",
			Age:  25,
		}

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, TestData](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, TestData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("通用类型 MGet 和 MSet", func(t *testing.T) {
		keys := []string{"gen1", "gen2"}
		values := map[string]TestData{
			"gen1": {ID: 1, Name: "name1", Age: 20},
			"gen2": {ID: 2, Name: "name2", Age: 30},
		}

		successCount, err := c.MSet(ctx, values, cache.WithKeyConvertor[string, TestData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 2, successCount)

		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, TestData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, values, result)
	})
}

func TestRemoteCache_ErrorCases(t *testing.T) {
	client := createMockRedisClient()

	c := NewStrRemoteCache[string, string](
		"test_error_cases",
		client,
	)

	ctx := context.Background()

	t.Run("缺少 KeyConvertor", func(t *testing.T) {
		_, err := c.Get(ctx, "test_key")
		assert.Error(t, err)
		assert.Equal(t, cache.ErrKeyConvertorNotFound, err)
	})

	t.Run("缺少 MarshalFunc", func(t *testing.T) {
		genericCache := NewRemoteCache[string, TestData](
			"test_marshal_error",
			nil, // 使用 nil client 来测试
		)

		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		err := genericCache.Set(ctx, "test_key", TestData{}, cache.WithKeyConvertor[string, TestData](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)
	})

	t.Run("缺少 UnmarshalFunc", func(t *testing.T) {
		genericCache := NewRemoteCache[string, TestData](
			"test_unmarshal_error",
			nil, // 使用 nil client 来测试
		)

		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		_, err := genericCache.Get(ctx, "test_key", cache.WithKeyConvertor[string, TestData](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)
	})
}

func TestRemoteCache_TimeoutOptions(t *testing.T) {
	client := createMockRedisClient()

	t.Run("自定义超时时间", func(t *testing.T) {
		customTimeout := 10 * time.Second
		c := NewStrRemoteCache[string, string](
			"test_timeout",
			client,
			WithTimeout[string](customTimeout),
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		key := "timeout_key"
		value := "timeout_value"

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("不使用远程超时", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_no_timeout",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		key := "no_timeout_key"
		value := "no_timeout_value"

		err := c.Set(ctx, key, value,
			cache.WithKeyConvertor[string, string](keyConverter),
			cache.WithoutRemoteTimeout[string, string](),
		)
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})
}

func TestRemoteCache_NamespaceOptions(t *testing.T) {
	client := createMockRedisClient()

	t.Run("带命名空间", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_namespace",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		key := "ns_key"
		value := "ns_value"

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("不带命名空间", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_no_namespace",
			client,
			WithoutNamespace[string](),
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		key := "no_ns_key"
		value := "no_ns_value"

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})
}

func TestRemoteCache_ConcurrentOperations(t *testing.T) {
	client := createMockRedisClient()

	c := NewStrRemoteCache[string, string](
		"test_concurrent",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("并发 Set 操作", func(t *testing.T) {
		const numGoroutines = 10
		done := make(chan bool, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				key := typ.NumberToString(id)
				value := "value_" + key
				err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
				assert.NoError(t, err)
				done <- true
			}(i)
		}

		for i := 0; i < numGoroutines; i++ {
			<-done
		}
	})

	t.Run("并发 Get 操作", func(t *testing.T) {
		const numGoroutines = 10
		done := make(chan bool, numGoroutines)

		// 先设置一些值
		for i := 0; i < numGoroutines; i++ {
			key := typ.NumberToString(i)
			value := "value_" + key
			err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
			assert.NoError(t, err)
		}

		// 并发获取
		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				key := typ.NumberToString(id)
				expectedValue := "value_" + key
				result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
				assert.NoError(t, err)
				assert.Equal(t, expectedValue, result)
				done <- true
			}(i)
		}

		for i := 0; i < numGoroutines; i++ {
			<-done
		}
	})
}

func TestRemoteCache_EdgeCases(t *testing.T) {
	client := createMockRedisClient()

	c := NewStrRemoteCache[string, string](
		"test_edge_cases",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("空字符串值", func(t *testing.T) {
		key := "empty_value_key"
		value := ""

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("空键列表 MGet", func(t *testing.T) {
		// Redis MGET 命令不接受空参数列表，所以这里应该返回错误
		result, err := c.MGet(ctx, []string{}, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("空键值映射 MSet", func(t *testing.T) {
		successCount, err := c.MSet(ctx, map[string]string{}, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 0, successCount)
	})

	t.Run("包含部分不存在键的 MGet", func(t *testing.T) {
		// 先设置一个键
		key1 := "existing_key"
		value1 := "existing_value"
		err := c.Set(ctx, key1, value1, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		// 获取包含存在和不存在键的列表
		keys := []string{key1, "non_existing_key"}
		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, value1, result[key1])
	})
}

func TestRemoteCache_AdvancedErrorCases(t *testing.T) {
	client := createMockRedisClient()

	t.Run("Redis 连接错误处理", func(t *testing.T) {
		// 使用 nil client 来测试连接错误
		c := NewStrRemoteCache[string, string](
			"test_connection_error",
			nil,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		// 测试 Get 操作
		_, err := c.Get(ctx, "test_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)

		// 测试 Set 操作
		err = c.Set(ctx, "test_key", "test_value", cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)

		// 测试 MGet 操作
		_, err = c.MGet(ctx, []string{"key1", "key2"}, cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)

		// 测试 MSet 操作
		_, err = c.MSet(ctx, map[string]string{"key1": "value1"}, cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)
	})

	t.Run("序列化错误处理", func(t *testing.T) {
		c := NewRemoteCache[string, TestData](
			"test_serialization_error",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		// 使用会导致序列化错误的 marshal 函数
		badMarshal := func(ctx context.Context, data TestData) (string, error) {
			return "", errors.New("serialization error")
		}

		err := c.Set(ctx, "test_key", TestData{},
			cache.WithKeyConvertor[string, TestData](keyConverter),
			cache.WithMarshalFunc[string, TestData](badMarshal),
		)
		assert.Error(t, err)
	})

	t.Run("反序列化错误处理", func(t *testing.T) {
		c := NewRemoteCache[string, TestData](
			"test_deserialization_error",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		// 先设置一个正常的值
		err := c.Set(ctx, "test_key", TestData{ID: 1, Name: "test"},
			cache.WithKeyConvertor[string, TestData](keyConverter),
		)
		assert.NoError(t, err)

		// 使用会导致反序列化错误的 unmarshal 函数
		badUnmarshal := func(ctx context.Context, data string) (TestData, error) {
			return TestData{}, errors.New("deserialization error")
		}

		_, err = c.Get(ctx, "test_key",
			cache.WithKeyConvertor[string, TestData](keyConverter),
			cache.WithUnmarshalFunc[string, TestData](badUnmarshal),
		)
		assert.Error(t, err)
	})
}

func TestRemoteCache_MSetWithoutTimeout(t *testing.T) {
	client := createMockRedisClient()

	c := NewStrRemoteCache[string, string](
		"test_mset_without_timeout",
		client,
	)

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "test:" + key
	}

	t.Run("MSet 不使用超时", func(t *testing.T) {
		values := map[string]string{
			"key1": "value1",
			"key2": "value2",
			"key3": "value3",
		}

		successCount, err := c.MSet(ctx, values,
			cache.WithKeyConvertor[string, string](keyConverter),
			cache.WithoutRemoteTimeout[string, string](),
		)
		assert.NoError(t, err)
		assert.Equal(t, 3, successCount)

		// 验证值是否正确设置
		result, err := c.MGet(ctx, []string{"key1", "key2", "key3"},
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Equal(t, values, result)
	})

	t.Run("MSet 部分序列化失败", func(t *testing.T) {
		customCache := NewRemoteCache[string, TestData](
			"test_partial_serialization_failure",
			client,
		)

		// 创建一个会导致部分序列化失败的 marshal 函数
		partialBadMarshal := func(ctx context.Context, data TestData) (string, error) {
			if data.ID == 2 {
				return "", errors.New("serialization error for ID 2")
			}
			return fmt.Sprintf(`{"id":%d,"name":"%s","age":%d}`, data.ID, data.Name, data.Age), nil
		}

		values := map[string]TestData{
			"key1": {ID: 1, Name: "test1", Age: 20},
			"key2": {ID: 2, Name: "test2", Age: 25}, // 这个会序列化失败
			"key3": {ID: 3, Name: "test3", Age: 30},
		}

		successCount, err := customCache.MSet(ctx, values,
			cache.WithKeyConvertor[string, TestData](keyConverter),
			cache.WithMarshalFunc[string, TestData](partialBadMarshal),
		)
		assert.NoError(t, err)
		assert.Equal(t, 2, successCount) // 只有 2 个成功，1 个失败
	})
}

func TestRemoteCache_TimeoutHandling(t *testing.T) {
	client := createMockRedisClient()

	t.Run("自定义远程超时", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_custom_remote_timeout",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		customTimeout := 30 * time.Second
		err := c.Set(ctx, "timeout_key", "timeout_value",
			cache.WithKeyConvertor[string, string](keyConverter),
			cache.WithRemoteTimeout[string, string](customTimeout),
		)
		assert.NoError(t, err)

		result, err := c.Get(ctx, "timeout_key",
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Equal(t, "timeout_value", result)
	})

	t.Run("默认超时处理", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_default_timeout",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		err := c.Set(ctx, "default_timeout_key", "default_timeout_value",
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)

		result, err := c.Get(ctx, "default_timeout_key",
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Equal(t, "default_timeout_value", result)
	})
}

func TestRemoteCache_OptionFunctions(t *testing.T) {
	client := createMockRedisClient()

	t.Run("WithTimeout 选项", func(t *testing.T) {
		customTimeout := 15 * time.Second
		c := NewStrRemoteCache[string, string](
			"test_with_timeout_option",
			client,
			WithTimeout[string](customTimeout),
		)

		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_with_timeout_option"), c.Name())
	})

	t.Run("WithoutNamespace 选项", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_without_namespace",
			client,
			WithoutNamespace[string](),
		)

		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_without_namespace"), c.Name())
	})

	t.Run("WithMarshalFunc 选项", func(t *testing.T) {
		customMarshal := func(ctx context.Context, data string) (string, error) {
			return "custom:" + data, nil
		}

		c := NewRemoteCache[string, string](
			"test_with_marshal_func",
			client,
			WithMarshalFunc[string](customMarshal),
		)

		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_with_marshal_func"), c.Name())
	})

	t.Run("WithUnmarshalFunc 选项", func(t *testing.T) {
		customUnmarshal := func(ctx context.Context, data string) (string, error) {
			if strings.HasPrefix(data, "custom:") {
				return strings.TrimPrefix(data, "custom:"), nil
			}
			return data, nil
		}

		c := NewRemoteCache[string, string](
			"test_with_unmarshal_func",
			client,
			WithUnmarshalFunc[string](customUnmarshal),
		)

		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_with_unmarshal_func"), c.Name())
	})
}

func TestRemoteCache_ComplexScenarios(t *testing.T) {
	client := createMockRedisClient()

	t.Run("混合操作场景", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_mixed_operations",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		// 1. 单个 Set 操作
		err := c.Set(ctx, "single_key", "single_value",
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)

		// 2. 批量 Set 操作
		batchValues := map[string]string{
			"batch1": "value1",
			"batch2": "value2",
		}
		successCount, err := c.MSet(ctx, batchValues,
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Equal(t, 2, successCount)

		// 3. 混合 Get 操作
		allKeys := []string{"single_key", "batch1", "batch2", "non_existent"}
		result, err := c.MGet(ctx, allKeys,
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Len(t, result, 3) // 只有 3 个存在的键
		assert.Equal(t, "single_value", result["single_key"])
		assert.Equal(t, "value1", result["batch1"])
		assert.Equal(t, "value2", result["batch2"])
	})

	t.Run("并发读写场景", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_concurrent_rw",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		const numGoroutines = 20
		done := make(chan bool, numGoroutines*2)

		// 先设置所有数据
		for i := 0; i < numGoroutines; i++ {
			key := fmt.Sprintf("concurrent_key_%d", i)
			value := fmt.Sprintf("concurrent_value_%d", i)
			err := c.Set(ctx, key, value,
				cache.WithKeyConvertor[string, string](keyConverter),
			)
			assert.NoError(t, err)
		}

		// 并发读取
		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				key := fmt.Sprintf("concurrent_key_%d", id)
				expectedValue := fmt.Sprintf("concurrent_value_%d", id)
				result, err := c.Get(ctx, key,
					cache.WithKeyConvertor[string, string](keyConverter),
				)
				assert.NoError(t, err)
				assert.Equal(t, expectedValue, result)
				done <- true
			}(i)
		}

		// 并发写入新数据
		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				key := fmt.Sprintf("concurrent_write_key_%d", id)
				value := fmt.Sprintf("concurrent_write_value_%d", id)
				err := c.Set(ctx, key, value,
					cache.WithKeyConvertor[string, string](keyConverter),
				)
				assert.NoError(t, err)
				done <- true
			}(i)
		}

		// 等待所有 goroutine 完成
		for i := 0; i < numGoroutines*2; i++ {
			<-done
		}
	})
}

func TestRemoteCache_EdgeCasesExtended(t *testing.T) {
	client := createMockRedisClient()

	t.Run("特殊字符键值", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_special_chars",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		specialKeys := []string{
			"key with spaces",
			"key-with-dashes",
			"key_with_underscores",
			"key:with:colons",
			"key.with.dots",
			"key@with@at@signs",
			"key#with#hash#signs",
			"key$with$dollar$signs",
			"key%with%percent%signs",
			"key^with^caret^signs",
		}

		specialValues := []string{
			"value with spaces",
			"value-with-dashes",
			"value_with_underscores",
			"value:with:colons",
			"value.with.dots",
			"value@with@at@signs",
			"value#with#hash#signs",
			"value$with$dollar$signs",
			"value%with%percent%signs",
			"value^with^caret^signs",
		}

		// 设置特殊字符的键值对
		for i, key := range specialKeys {
			err := c.Set(ctx, key, specialValues[i],
				cache.WithKeyConvertor[string, string](keyConverter),
			)
			assert.NoError(t, err)
		}

		// 获取并验证
		result, err := c.MGet(ctx, specialKeys,
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Len(t, result, len(specialKeys))

		for i, key := range specialKeys {
			assert.Equal(t, specialValues[i], result[key])
		}
	})

	t.Run("大值处理", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_large_values",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		// 创建一个较大的字符串值
		largeValue := strings.Repeat("large_value_content_", 1000)
		key := "large_value_key"

		err := c.Set(ctx, key, largeValue,
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)

		result, err := c.Get(ctx, key,
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Equal(t, largeValue, result)
	})

	t.Run("Unicode 字符处理", func(t *testing.T) {
		c := NewStrRemoteCache[string, string](
			"test_unicode",
			client,
		)

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		unicodeKeys := []string{
			"中文键",
			"🔑emoji键",
			"ключ_на_русском",
			"κλειδί_στα_ελληνικά",
			"مفتاح_بالعربية",
		}

		unicodeValues := []string{
			"中文值",
			"🔑emoji值",
			"значение_на_русском",
			"τιμή_στα_ελληνικά",
			"قيمة_بالعربية",
		}

		// 设置 Unicode 键值对
		for i, key := range unicodeKeys {
			err := c.Set(ctx, key, unicodeValues[i],
				cache.WithKeyConvertor[string, string](keyConverter),
			)
			assert.NoError(t, err)
		}

		// 获取并验证
		result, err := c.MGet(ctx, unicodeKeys,
			cache.WithKeyConvertor[string, string](keyConverter),
		)
		assert.NoError(t, err)
		assert.Len(t, result, len(unicodeKeys))

		for i, key := range unicodeKeys {
			assert.Equal(t, unicodeValues[i], result[key])
		}
	})
}
