package remote_cache

import (
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type (
	InitOption[V any] struct {
		Timeout          time.Duration
		WithoutNamespace bool
		MarshalFunc      cache.MarshalFunc[V]
		UnmarshalFunc    cache.UnmarshalFunc[V]
	}

	InitOptions[V any] func(options *InitOption[V])
)

func WithTimeout[V any](timeout time.Duration) InitOptions[V] {
	return func(options *InitOption[V]) {
		if options == nil {
			options = new(InitOption[V])
		}
		options.Timeout = timeout
	}
}

func WithoutNamespace[V any]() InitOptions[V] {
	return func(options *InitOption[V]) {
		if options == nil {
			options = new(InitOption[V])
		}
		options.WithoutNamespace = true
	}
}

func WithMarshalFunc[V any](marshalFunc cache.MarshalFunc[V]) InitOptions[V] {
	return func(options *InitOption[V]) {
		if options == nil {
			options = new(InitOption[V])
		}
		if marshalFunc == nil {
			return
		}
		options.MarshalFunc = marshalFunc
	}
}

func WithUnmarshalFunc[V any](unmarshalFunc cache.UnmarshalFunc[V]) InitOptions[V] {
	return func(options *InitOption[V]) {
		if options == nil {
			options = new(InitOption[V])
		}
		if unmarshalFunc == nil {
			return
		}
		options.UnmarshalFunc = unmarshalFunc
	}
}
