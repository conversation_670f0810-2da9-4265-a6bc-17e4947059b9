package remote_cache

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type RemoteCache[K comparable, V any] interface {
	Name() cache.Name
	Get(ctx context.Context, key K, opts ...cache.Options[K, V]) (V, error)
	Set(ctx context.Context, key K, val V, opts ...cache.Options[K, V]) error
	MGet(ctx context.Context, keys []K, opts ...cache.Options[K, V]) (map[K]V, error)
	MSet(ctx context.Context, kvMap map[K]V, opts ...cache.Options[K, V]) (int, error)
}

func NewNumberRemoteCache[K comparable, V typ.Number](
	name cache.Name,
	remoteCache *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[K, V] {
	opts = append(opts, WithUnmarshalFunc[V](cache.NumberUnmarshal[V]), WithMarshalFunc[V](cache.NumberMarshal[V]))
	return NewRemoteCache[K, V](name, remoteCache, opts...)
}

func NewBoolRemoteCache[K comparable, V typ.Bool](
	name cache.Name,
	remoteCache *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[K, V] {
	opts = append(opts, WithUnmarshalFunc[V](cache.BoolUnmarshal[V]), WithMarshalFunc[V](cache.BoolMarshal[V]))
	return NewRemoteCache[K, V](name, remoteCache, opts...)
}

func NewStrRemoteCache[K comparable, V typ.String](
	name cache.Name,
	remoteCache *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[K, V] {
	opts = append(opts, WithUnmarshalFunc[V](cache.StringUnmarshal[V]), WithMarshalFunc[V](cache.StringMarshal[V]))
	return NewRemoteCache[K, V](name, remoteCache, opts...)
}

func NewProtoRemoteCache[K comparable, V proto.Message](
	name cache.Name,
	remoteCache *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[K, V] {
	opts = append(opts, WithUnmarshalFunc[V](cache.PbUnmarshal[V]), WithMarshalFunc[V](cache.PbMarshal[V]))
	return NewRemoteCache[K, V](name, remoteCache, opts...)
}

func NewRemoteCache[K comparable, V any](
	name cache.Name,
	client *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[K, V] {
	// 默认使用 json
	opt := InitOption[V]{
		UnmarshalFunc: cache.JsonUnmarshal[V],
		MarshalFunc:   cache.JsonMarshal[V],
	}
	for _, o := range opts {
		o(&opt)
	}

	timeout := cache.WarmCacheDuration
	if opt.Timeout > 0 {
		timeout = opt.Timeout
	}

	return &remoteCache[K, V]{
		name:             name,
		client:           client,
		marshal:          opt.MarshalFunc,
		unmarshal:        opt.UnmarshalFunc,
		withoutNamespace: opt.WithoutNamespace,
		timeout:          timeout,
	}
}

type remoteCache[K comparable, V any] struct {
	name             cache.Name
	client           *redis.Client
	marshal          cache.MarshalFunc[V]
	unmarshal        cache.UnmarshalFunc[V]
	withoutNamespace bool
	timeout          time.Duration
}

func (r *remoteCache[K, V]) Name() cache.Name {
	return r.name
}

func (r *remoteCache[K, V]) Get(ctx context.Context, key K, opts ...cache.Options[K, V]) (V, error) {
	if r.client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusNotEnable, "")
		return typ.ZeroValue[V](), cache.ErrCacheNotEnable
	}

	opt := cache.InitOption(opts...)
	converter, err := r.getKeyConvertor(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusKeyConverterNotFound, "")
		return typ.ZeroValue[V](), err
	}

	unmarshal, err := r.getUnmarshalFunc(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusUnmarshalFuncNotFound, "")
		return typ.ZeroValue[V](), err
	}

	value, gErr := r.client.Get(ctx, r.getRemoteKey(ctx, key, converter)).Result()
	if gErr != nil {
		var empty V
		if gErr == redis.Nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusMiss, "")
			return empty, cache.ErrKeyNotFound
		}
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusError, "")
		return empty, gErr
	}

	val, err := unmarshal(ctx, value)
	if err != nil {
		var empty V
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusUnmarshalFuncError, "")
		return empty, err
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusSuccess, "")
	return val, nil
}

func (r *remoteCache[K, V]) Set(ctx context.Context, key K, val V, opts ...cache.Options[K, V]) error {
	if r.client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusNotEnable, "")
		return cache.ErrCacheNotEnable
	}

	opt := cache.InitOption(opts...)
	converter, err := r.getKeyConvertor(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusKeyConverterNotFound, "")
		return err
	}

	marshal, err := r.getMarshalFunc(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusMarshalFuncNotFound, "")
		return err
	}

	valStr, err := marshal(ctx, val)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusMarshalFuncError, "")
		return err
	}

	var timeout time.Duration
	if !opt.WithoutRemoteTimeout {
		timeout = r.getTimeout(opt)
	}
	err = r.client.Set(ctx, r.getRemoteKey(ctx, key, converter), valStr, timeout).Err()
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusError, "")
		return err
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusSuccess, "")
	return nil
}

func (r *remoteCache[K, V]) MGet(ctx context.Context, keys []K, opts ...cache.Options[K, V]) (map[K]V, error) {
	if r.client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusNotEnable, "")
		return nil, cache.ErrCacheNotEnable
	}

	if len(keys) == 0 {
		return make(map[K]V), nil
	}

	opt := cache.InitOption(opts...)
	converter, err := r.getKeyConvertor(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusKeyConverterNotFound, "")
		return nil, err
	}

	unmarshal, err := r.getUnmarshalFunc(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusUnmarshalFuncNotFound, "")
		return nil, err
	}

	remoteKeys := make([]string, len(keys))
	for index, key := range keys {
		remoteKeys[index] = r.getRemoteKey(ctx, key, converter)
	}
	values, gErr := r.client.MGet(ctx, remoteKeys...).Result()
	if gErr != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusError, "")
		return nil, gErr
	}

	ret := make(map[K]V, len(keys))
	for i := range values {
		valueI := values[i]
		if valueI == nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusMiss, "")
			continue
		}
		value, ok := valueI.(string)
		if !ok {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusConvertError, "")
			continue
		}
		val, err := unmarshal(ctx, value)
		if err != nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCache, string(r.name), constant.StatusUnmarshalFuncError, "")
			continue
		}
		ret[keys[i]] = val
	}
	return ret, nil
}

func (r *remoteCache[K, V]) MSet(ctx context.Context, kvMap map[K]V, opts ...cache.Options[K, V]) (int, error) {
	if r.client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusNotEnable, "")
		return 0, cache.ErrCacheNotEnable
	}

	if len(kvMap) == 0 {
		return 0, nil
	}

	opt := cache.InitOption(opts...)
	if opt.WithoutRemoteTimeout {
		return r.mSet(ctx, kvMap, opt)
	}
	return r.mSetWithTimeout(ctx, kvMap, opt)
}

func (r *remoteCache[K, V]) mSet(ctx context.Context, kvMap map[K]V, option cache.Option[K, V]) (int, error) {
	converter, err := r.getKeyConvertor(option)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusKeyConverterNotFound, "")
		return 0, err
	}

	marshal, err := r.getMarshalFunc(option)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusMarshalFuncNotFound, "")
		return 0, err
	}

	values := make([]interface{}, 0, len(kvMap)*2)
	successCount := 0
	for key, val := range kvMap {
		valStr, marshalErr := marshal(ctx, val)
		if marshalErr != nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusMarshalFuncError, "")
			continue
		}
		successCount++
		values = append(values, r.getRemoteKey(ctx, key, converter))
		values = append(values, valStr)
	}
	if redisErr := r.client.MSet(ctx, values...).Err(); redisErr != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusError, "")
		return 0, redisErr
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusSuccess, "")
	return successCount, nil
}

func (r *remoteCache[K, V]) mSetWithTimeout(ctx context.Context, kvMap map[K]V, option cache.Option[K, V]) (int, error) {
	converter, err := r.getKeyConvertor(option)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusKeyConverterNotFound, "")
		return 0, err
	}

	marshal, err := r.getMarshalFunc(option)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusMarshalFuncNotFound, "")
		return 0, err
	}

	successCount := 0
	timeout := r.getTimeout(option)

	pipeline := r.client.Pipeline()
	for key, value := range kvMap {
		valStr, marshalErr := marshal(ctx, value)
		if marshalErr != nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusMarshalFuncError, "")
			continue
		}
		successCount++
		remoteKey := r.getRemoteKey(ctx, key, converter)
		pipeline.Set(ctx, remoteKey, valStr, timeout)
	}
	_, execErr := pipeline.Exec(ctx)
	if execErr != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusError, "")
		return 0, execErr
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleRemoteCacheSet, string(r.name), constant.StatusSuccess, "")
	return successCount, nil
}

func (r *remoteCache[K, V]) getRemoteKey(ctx context.Context, key K, keyConverter cache.KeyConvertor[K]) string {
	remoteKey := keyConverter(ctx, key)
	if r.withoutNamespace {
		return remoteKey
	}
	return string(r.name) + "." + remoteKey
}

func (r *remoteCache[K, V]) getKeyConvertor(opt cache.Option[K, V]) (cache.KeyConvertor[K], error) {
	if opt.KeyConvertor != nil {
		return opt.KeyConvertor, nil
	}
	return nil, cache.ErrKeyConvertorNotFound
}

func (r *remoteCache[K, V]) getMarshalFunc(opt cache.Option[K, V]) (cache.MarshalFunc[V], error) {
	if opt.MarshalFunc != nil {
		return opt.MarshalFunc, nil
	}
	if r.marshal != nil {
		return r.marshal, nil
	}
	return nil, cache.ErrMarshalFuncNotFound
}

func (r *remoteCache[K, V]) getUnmarshalFunc(opt cache.Option[K, V]) (cache.UnmarshalFunc[V], error) {
	if opt.UnmarshalFunc != nil {
		return opt.UnmarshalFunc, nil
	}
	if r.unmarshal != nil {
		return r.unmarshal, nil
	}
	return nil, cache.ErrUnmarshalFuncNotFound
}

func (r *remoteCache[K, V]) getTimeout(opt cache.Option[K, V]) time.Duration {
	if opt.RemoteTimeout > 0 {
		return opt.RemoteTimeout
	}
	if r.timeout > 0 {
		return r.timeout
	}
	return cache.DefaultCacheDuration
}
