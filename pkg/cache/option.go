package cache

import (
	"context"
	"errors"
	"time"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

var (
	ErrCacheNotFound         = errors.New("cache not found")
	ErrCacheNotEnable        = errors.New("cache not enable")
	ErrKeyNotFound           = errors.New("key not found")
	ErrKeyExpired            = errors.New("key expired")
	ErrKeyConvertorNotFound  = errors.New("key not convertor not found")
	ErrMarshalFuncNotFound   = errors.New("marshal function not found")
	ErrUnmarshalFuncNotFound = errors.New("unmarshal function not found")
	ErrEviction              = errors.New("eviction")
)

const (
	DefaultCacheDuration = 60 * time.Second
	ShortCacheDuration   = 1 * time.Minute
	LongCacheDuration    = 30 * time.Minute
	WarmCacheDuration    = 5 * time.Minute
)

type (
	KeyConvertor[K any]  func(context.Context, K) string
	UnmarshalFunc[V any] func(ctx context.Context, data string) (V, error)
	MarshalFunc[V any]   func(ctx context.Context, data V) (string, error)
)

type Option[K comparable, V any] struct {
	// Local
	Timeout              time.Duration
	WithoutRemoteTimeout bool

	// Remote
	RemoteTimeout time.Duration
	MarshalFunc   MarshalFunc[V]
	UnmarshalFunc UnmarshalFunc[V]
	KeyConvertor  KeyConvertor[K]
}

func InitOption[K comparable, V any](opts ...Options[K, V]) Option[K, V] {
	var option Option[K, V]
	if len(opts) > 0 {
		for _, opt := range opts {
			opt(&option)
		}
	}
	return option
}

type Options[K comparable, V any] func(options *Option[K, V])

func WithOptions[K comparable, V any](opts Option[K, V]) Options[K, V] {
	return func(options *Option[K, V]) {
		if options == nil {
			options = new(Option[K, V])
		}
		*options = opts
	}
}

func WithTimeout[K comparable, V any](timeout time.Duration) Options[K, V] {
	return func(options *Option[K, V]) {
		if options == nil {
			options = new(Option[K, V])
		}
		options.Timeout = timeout
	}
}

func WithDefaultTimeout[K comparable, V any]() Options[K, V] {
	return WithTimeout[K, V](DefaultCacheDuration)
}

func WithShortTimeout[K comparable, V any]() Options[K, V] {
	return WithTimeout[K, V](ShortCacheDuration)
}

func WithWarnTimeout[K comparable, V any]() Options[K, V] {
	return WithTimeout[K, V](WarmCacheDuration)
}

func WithLongTimeout[K comparable, V any]() Options[K, V] {
	return WithTimeout[K, V](LongCacheDuration)
}

func WithRemoteTimeout[K comparable, V any](timeout time.Duration) Options[K, V] {
	return func(options *Option[K, V]) {
		if options == nil {
			options = new(Option[K, V])
		}
		options.RemoteTimeout = timeout
	}
}

func WithDefaultRemoteTimeout[K comparable, V any]() Options[K, V] {
	return WithRemoteTimeout[K, V](DefaultCacheDuration)
}

func WithRemoteShortTimeout[K comparable, V any]() Options[K, V] {
	return WithTimeout[K, V](ShortCacheDuration)
}

func WithRemoteWarnTimeout[K comparable, V any]() Options[K, V] {
	return WithTimeout[K, V](WarmCacheDuration)
}

func WithRemoteLongTimeout[K comparable, V any]() Options[K, V] {
	return WithTimeout[K, V](LongCacheDuration)
}

func WithoutRemoteTimeout[K comparable, V any]() Options[K, V] {
	return func(options *Option[K, V]) {
		if options == nil {
			options = new(Option[K, V])
		}
		options.WithoutRemoteTimeout = true
	}
}

func WithMarshalFunc[K comparable, V any](marshalFunc MarshalFunc[V]) Options[K, V] {
	return func(options *Option[K, V]) {
		if options == nil {
			options = new(Option[K, V])
		}
		options.MarshalFunc = marshalFunc
	}
}

func WithUnmarshalFunc[K comparable, V any](unmarshalFunc UnmarshalFunc[V]) Options[K, V] {
	return func(options *Option[K, V]) {
		if options == nil {
			options = new(Option[K, V])
		}
		options.UnmarshalFunc = unmarshalFunc
	}
}

func WithKeyConvertor[K comparable, V any](keyConvertor KeyConvertor[K]) Options[K, V] {
	return func(options *Option[K, V]) {
		if options == nil {
			options = new(Option[K, V])
		}
		options.KeyConvertor = keyConvertor
	}
}

func JsonUnmarshal[V any](ctx context.Context, data string) (V, error) {
	var v V
	if err := sonic.UnmarshalString(data, &v); err != nil {
		return v, err
	}
	return v, nil
}

// PbUnmarshalFromBytes 默认使用 proto 反序列化， redis 存储的是 pb 字符串
func PbUnmarshal[V proto.Message](ctx context.Context, data string) (V, error) {
	v := receiverZeroValue[V]()
	if err := proto.Unmarshal(typ.StringToBytes(data), v); err != nil {
		return v, err
	}
	return v, nil
}

func StringUnmarshal[V typ.String](ctx context.Context, data string) (V, error) {
	return V(data), nil
}

func NumberUnmarshal[V typ.Number](ctx context.Context, data string) (V, error) {
	return typ.StringToNumber[V](data)
}

func BoolUnmarshal[V typ.Bool](ctx context.Context, data string) (V, error) {
	return typ.StringToBool[V](data)
}

func JsonMarshal[V any](ctx context.Context, data V) (string, error) {
	return sonic.MarshalString(data)
}

func PbMarshal[V proto.Message](ctx context.Context, data V) (string, error) {
	b, err := proto.Marshal(data)
	if err != nil {
		return "", err
	}
	return typ.BytesToString(b), nil
}

func StringMarshal[V typ.String](ctx context.Context, data V) (string, error) {
	return string(data), nil
}

func NumberMarshal[V typ.Number](ctx context.Context, data V) (string, error) {
	return typ.NumberToString(data), nil
}

func BoolMarshal[V typ.Bool](ctx context.Context, data V) (string, error) {
	return typ.BoolToString(data), nil
}
