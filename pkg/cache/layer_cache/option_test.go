package layer_cache

import (
	"testing"
)

func TestSeparateOptions(t *testing.T) {
	tests := []struct {
		name           string
		opts           []LevelOption
		expectAsync    bool
		expectCacheLen int
	}{
		{
			name:           "empty options",
			opts:           []LevelOption{},
			expectAsync:    false,
			expectCacheLen: 0,
		},
		{
			name:           "only cache options",
			opts:           []LevelOption{WithCustomExpire(300), WithHotExpire()},
			expectAsync:    false,
			expectCacheLen: 2,
		},
		{
			name:           "only async option",
			opts:           []LevelOption{WithAsyncSet()},
			expectAsync:    true,
			expectCacheLen: 0,
		},
		{
			name:           "mixed options",
			opts:           []LevelOption{WithCustomExpire(300), WithAsyncSet(), WithHotExpire()},
			expectAsync:    true,
			expectCacheLen: 2,
		},
		{
			name:           "nil option in slice",
			opts:           []LevelOption{WithCustomExpire(300), nil, WithAsyncSet()},
			expectAsync:    true,
			expectCacheLen: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cacheOpts, hasAsync := SeparateOptions(tt.opts)

			if hasAsync != tt.expectAsync {
				t.Errorf("SeparateOptions() hasAsync = %v, want %v", hasAsync, tt.expectAsync)
			}

			if len(cacheOpts) != tt.expectCacheLen {
				t.Errorf("SeparateOptions() cacheOpts length = %v, want %v", len(cacheOpts), tt.expectCacheLen)
			}

			// 验证返回的缓存选项中不包含异步选项
			for _, opt := range cacheOpts {
				if GetOptionType(opt) == OptionTypeAsync {
					t.Error("SeparateOptions() returned async option in cache options")
				}
			}
		})
	}
}

func TestGetOptionType(t *testing.T) {
	tests := []struct {
		name     string
		opt      LevelOption
		expected OptionType
	}{
		{
			name:     "async option",
			opt:      WithAsyncSet(),
			expected: OptionTypeAsync,
		},
		{
			name:     "cache option - custom expire",
			opt:      WithCustomExpire(300),
			expected: OptionTypeCache,
		},
		{
			name:     "cache option - hot expire",
			opt:      WithHotExpire(),
			expected: OptionTypeCache,
		},
		{
			name:     "cache option - warm expire",
			opt:      WithWarmExpire(),
			expected: OptionTypeCache,
		},
		{
			name:     "cache option - cold expire",
			opt:      WithColdExpire(),
			expected: OptionTypeCache,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetOptionType(tt.opt)
			if result != tt.expected {
				t.Errorf("GetOptionType() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func BenchmarkSeparateOptions(b *testing.B) {
	opts := []LevelOption{
		WithCustomExpire(300),
		WithHotExpire(),
		WithAsyncSet(),
		WithWarmExpire(),
		WithColdExpire(),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = SeparateOptions(opts)
	}
}

func BenchmarkGetOptionType(b *testing.B) {
	opt := WithAsyncSet()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = GetOptionType(opt)
	}
}
