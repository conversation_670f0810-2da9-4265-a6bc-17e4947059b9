package layer_cache

import (
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
)

var (
	namespaceGroupRedisMap = map[NamespaceGroup]redishelper.CacheCloudClusterName{
		Default: redishelper.Default,
		Shop:    redishelper.Default,
	}

	namespaceMap = map[Namespace]NamespaceGroup{
		// Shop相关namespace映射
		ShopDetailNamespace: Shop,
	}
)

const (
	defaultTimeout    = time.Duration(1000) * time.Millisecond
	defaultMemorySize = 500000
)

type (
	NamespaceGroup string
	Namespace      string
)

// Namespace
const (
	// Shop相关缓存命名空间
	ShopDetailNamespace Namespace = "shop.detail"
)

// NamespaceGroup
const (
	Default NamespaceGroup = "default"
	Shop    NamespaceGroup = "shop"
)
