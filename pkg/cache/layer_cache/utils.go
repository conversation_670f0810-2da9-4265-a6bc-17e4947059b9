package layer_cache

import (
	"context"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type (
	keyConvertorWithCtx[K any]             func(ctx context.Context, key K) string
	singleLoader[K any, V any]             func(ctx context.Context, key K) (V, error)
	manyLoaderFromSlice[K any, V any]      func(ctx context.Context, keys []K) ([]V, error)
	manyLoaderFromMap[K comparable, V any] func(ctx context.Context, keys []K) (map[K]V, error)

	unmarshalFromBytes[V any] func([]byte) (V, error)
)

// CacheLoadManyFromSlice 批量根据 k 获取 value, 返回 map 可能只包含部分数据
//
//	todo: 当前底层实现是多次的 Get + Set ，后续可以优化成 MGet + MSet
func CacheLoadManyFromSlice[K comparable, V any](
	ctx context.Context,
	c LayerCache,
	rawKeys []K,
	keyConvertor keyConvertorWithCtx[K],
	unmarshalFunc unmarshalFromBytes[V],
	loader manyLoaderFromSlice[K, V],
	opts ...LevelOption,
) (map[K]V, error) {
	rawToKey := make(map[K]string, len(rawKeys))
	receiverMap := make(map[K]V, len(rawKeys))
	var missing []K

	opts = append(opts, WithUnmarshalFunc(
		func(bytes []byte) (interface{}, error) {
			return unmarshalFunc(bytes)
		},
	))

	for _, rawKey := range rawKeys {
		key := keyConvertor(ctx, rawKey)
		rawToKey[rawKey] = key
		value, err := c.Get(ctx, key, opts...)
		if err != nil {
			missing = append(missing, rawKey)
			continue
		}
		v, ok := value.(V)
		if !ok {
			missing = append(missing, rawKey)
			continue
		}
		receiverMap[rawKey] = v
	}
	if len(missing) == 0 {
		return receiverMap, nil
	}

	values, err := loader(ctx, missing)
	if err != nil {
		return nil, err
	}

	if len(missing) != len(values) {
		return nil, errDataLoadValueNotFound
	}

	for index, rawKey := range missing {
		receiverMap[rawKey] = values[index]
		_ = c.Set(ctx, rawToKey[rawKey], values[index], opts...)
	}
	return receiverMap, nil
}

// JsonUnmarshalFromBytes 默认使用 json 反序列化， redis 存储的是 json 字符串
func JsonUnmarshalFromBytes[T any](data []byte) (T, error) {
	var t T
	if err := sonic.Unmarshal(data, &t); err != nil {
		return t, err
	}
	return t, nil
}

// PbUnmarshalFromBytes 默认使用 proto 反序列化， redis 存储的是 pb 字符串
func PbUnmarshalFromBytes[T proto.Message](data []byte) (T, error) {
	t := typ.ReceiverZeroValue[T]()
	if err := proto.Unmarshal(data, t); err != nil {
		return t, err
	}
	return t, nil
}
