package layer_cache

import (
	"context"
	"time"

	"github.com/panjf2000/ants/v2"

	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

const (
	// 缓存异步写入协程池大小
	asyncWritePoolSize = 128
	// 异步写入超时
	asyncWriteTimeout = 2 * time.Second
)

// AsyncWriter 异步缓存写入器
type AsyncWriter struct {
	pool *ants.Pool
}

// NewAsyncWriter 创建异步写入器
func NewAsyncWriter() *AsyncWriter {
	// 创建ants协程池
	pool, err := ants.NewPool(asyncWritePoolSize, ants.WithOptions(ants.Options{
		// 协程空闲1分钟后释放
		ExpiryDuration: time.Minute,
		// 非阻塞模式：当协程池满时，返回错误而不是等待
		Nonblocking: true,
		// 预分配协程
		PreAlloc: true,
		// panic处理
		PanicHandler: func(err interface{}) {
			Logger.LogErrorf("async cache writer panic: %v", err)
		},
	}))

	if err != nil {
		Logger.LogErrorf("create async cache writer pool failed: %v", err)
		return nil
	}

	return &AsyncWriter{
		pool: pool,
	}
}

// AsyncWriteTask 异步写入任务
type AsyncWriteTask struct {
	Namespace Namespace
	ID        string
	Write     func() error
}

// SubmitAsyncWrite 提交异步写入任务
func (w *AsyncWriter) SubmitAsyncWrite(ctx context.Context, task AsyncWriteTask) error {
	if w == nil || w.pool == nil {
		// 降级到同步写入
		return task.Write()
	}

	// 提交任务到协程池
	err := w.pool.Submit(func() {
		w.executeAsyncWrite(ctx, task)
	})

	if err != nil {
		// 协程池满或其他错误，降级到同步写入
		Logger.CtxLogErrorf(ctx, "submit async cache write task failed, namespace=%s, id=%s, err=%v",
			task.Namespace, task.ID, err)
		return task.Write()
	}

	return nil
}

// executeAsyncWrite 执行异步写入
func (w *AsyncWriter) executeAsyncWrite(ctx context.Context, task AsyncWriteTask) {
	ctx, cancel := context.WithTimeout(ctx, asyncWriteTimeout)
	defer cancel()

	err := task.Write()
	if err != nil {
		Logger.CtxLogErrorf(ctx, "async cache write failed: namespace=%s, id=%s, err=%v",
			task.Namespace, task.ID, err)
	}
}

// Stop 停止异步写入器
func (w *AsyncWriter) Stop() {
	if w == nil || w.pool == nil {
		return
	}
	w.pool.Release()
}

// GetPoolStats 获取协程池统计信息（用于监控）
func (w *AsyncWriter) GetPoolStats() (running, free, cap int) {
	if w == nil || w.pool == nil {
		return 0, 0, 0
	}
	return w.pool.Running(), w.pool.Free(), w.pool.Cap()
}
