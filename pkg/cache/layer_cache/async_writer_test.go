package layer_cache

import (
	"context"
	"sync"
	"testing"
	"time"
)

func TestNewAsyncWriter(t *testing.T) {
	writer := NewAsyncWriter()
	if writer == nil {
		t.<PERSON><PERSON>("Expected AsyncWriter instance, got nil")
	}

	// 验证协程池统计信息
	running, free, cap := writer.GetPoolStats()
	if cap != asyncWritePoolSize {
		t.<PERSON><PERSON>("Expected capacity %d, got %d", asyncWritePoolSize, cap)
	}
	if running != 0 {
		t.<PERSON><PERSON><PERSON>("Expected 0 running goroutines, got %d", running)
	}
	if free <= 0 {
		t.<PERSON><PERSON><PERSON>("Expected positive free goroutines, got %d", free)
	}

	// 清理
	writer.Stop()
}

func TestAsyncWriterSubmitTask(t *testing.T) {
	writer := NewAsyncWriter()
	if writer == nil {
		t.Fatal("Expected AsyncWriter instance, got nil")
	}
	defer writer.Stop()

	var executedCount int32
	var wg sync.WaitGroup

	ctx := context.Background()

	// 提交多个任务
	for i := 0; i < 10; i++ {
		wg.Add(1)
		task := AsyncWriteTask{
			Namespace: Namespace("test"),
			ID:        "test_id",
			Write: func() error {
				defer wg.Done()
				executedCount++
				return nil
			},
		}

		err := writer.SubmitAsyncWrite(ctx, task)
		if err != nil {
			t.Errorf("Unexpected error submitting task: %v", err)
		}
	}

	// 等待所有任务完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 任务完成
	case <-time.After(time.Second * 5):
		t.Fatal("Tasks did not complete within 5 seconds")
	}
}

func TestAsyncWriterNilHandling(t *testing.T) {
	var writer *AsyncWriter

	// 测试 nil AsyncWriter 的处理
	running, free, cap := writer.GetPoolStats()
	if running != 0 || free != 0 || cap != 0 {
		t.Errorf("Expected all stats to be 0 for nil writer, got running=%d, free=%d, cap=%d", running, free, cap)
	}

	// 测试 nil AsyncWriter 的 Stop 方法
	writer.Stop() // 应该不会 panic

	// 测试 nil AsyncWriter 的 SubmitAsyncWrite 方法
	var executed bool
	task := AsyncWriteTask{
		Namespace: Namespace("test"),
		ID:        "test_id",
		Write: func() error {
			executed = true
			return nil
		},
	}

	err := writer.SubmitAsyncWrite(context.Background(), task)
	if err != nil {
		t.Errorf("Expected no error for nil writer, got %v", err)
	}

	if !executed {
		t.Error("Expected task to be executed synchronously when writer is nil")
	}
}

func TestAsyncWriterIntegrationWithLayerCache(t *testing.T) {
	// 创建自定义的 AsyncWriter
	customWriter := NewAsyncWriter()
	if customWriter == nil {
		t.Fatal("Expected AsyncWriter instance, got nil")
	}
	defer customWriter.Stop()

	// 创建包含 asyncWriter 的 LayerCacheManager
	manager := &LayerCacheManager{
		caches:      make(map[Namespace]LayerCache),
		asyncWriter: customWriter,
	}

	// 验证 LayerCacheManager 的异步写入器配置
	if manager.asyncWriter == nil {
		t.Error("Expected asyncWriter to be configured in manager, got nil")
	}

	// 验证统计信息
	running, free, cap := manager.GetAsyncWriterStats()
	if cap != asyncWritePoolSize {
		t.Errorf("Expected capacity %d, got %d", asyncWritePoolSize, cap)
	}
	if running != 0 {
		t.Errorf("Expected 0 running goroutines, got %d", running)
	}
	if free <= 0 {
		t.Errorf("Expected positive free goroutines, got %d", free)
	}

	// 创建 layerCache 实例（无需引用 manager，避免循环引用）
	layerCache := &layerCache{
		cache:        nil, // 在实际测试中这里需要真实的 cache 实现
		confAccessor: nil, // 在实际测试中这里需要真实的 confAccessor
	}

	// 验证 layerCache 不再持有循环引用
	if layerCache.cache == nil && layerCache.confAccessor == nil {
		// 这只是为了验证结构体字段，实际测试中需要提供真实的依赖
		t.Log("layerCache structure is clean without circular references")
	}
}
