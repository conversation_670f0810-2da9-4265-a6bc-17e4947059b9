package layer_cache

import (
	"context"
	"sync"

	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/api"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/layered"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/memory"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/remote"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

var (
	newLayerCacheManager *LayerCacheManager
	initOnce             sync.Once // 保证 layerCache 只获取一次
)

func IsDataNotFoundErr(err error) bool {
	return err == errDataNotFound || err == cache.HotPenetrationError
}

type LayerCacheManager struct {
	caches      map[Namespace]LayerCache
	asyncWriter *AsyncWriter // 全局异步写入器
}

func NewLayerCacheManager(ctx context.Context, confAccessor config.ConfAccessor, globalRedisClients redishelper.GlobalRedisClients) *LayerCacheManager {
	initOnce.Do(func() {
		namespaceGroupCacheMap := make(map[NamespaceGroup]api.CacheClient)

		cfg := confAccessor.GetLayerCacheConfig(ctx)
		namespaceGroupCfg := mutable_application_config.LayerCacheNamespaceConfig{
			MemSize:          cfg.MemSize,
			MemNumOfCounters: cfg.MemNumOfCounters,
			MemLogicTTL:      cfg.MemLogicTTL,
			MemPhysicalTTL:   cfg.MemPhysicalTTL,
		}

		layerCacheManager := &LayerCacheManager{
			caches:      make(map[Namespace]LayerCache),
			asyncWriter: NewAsyncWriter(),
		}
		for namespace, namespaceGroup := range namespaceMap {
			if _, ok := layerCacheManager.getCache(namespace); ok {
				continue
			}
			c, ok := namespaceGroupCacheMap[namespaceGroup]
			if !ok {
				client, redisErr := globalRedisClients.GetRedisClusterByClusterName(namespaceGroupRedisMap[namespaceGroup])
				if redisErr != nil {
					Logger.CtxLogErrorf(context.Background(), "init opt layer cache redis failed|namespace=%v|err=%v", namespace, redisErr)
					continue
				}

				namespaceConfig := namespaceGroupCfg
				namespaceCfg, nok := cfg.Namespaces[string(namespaceGroup)]
				if nok {
					namespaceConfig = updateMemoryCacheConfig(namespaceConfig, namespaceCfg)
				}
				memoryCacheConfig := memory.MemoryCacheConfig{
					Size:               uint32(namespaceConfig.MemSize),
					NumOfCounters:      uint32(namespaceConfig.MemNumOfCounters),
					PhysicalTtlSeconds: uint32(namespaceConfig.MemPhysicalTTL),
					LogicTtlSeconds:    uint32(namespaceConfig.MemLogicTTL),
				}
				if memoryCacheConfig.Size == 0 {
					memoryCacheConfig.Size = defaultMemorySize
				}
				c = layered.NewCacheClient(&layered.CacheConfig{
					MemoryCacheConfig: memoryCacheConfig,
					RedisConfig: remote.RedisConfig{
						Client: client,
					},
					LimitKeySize:         false,
					CircuitBreakerEnable: false,
					PenetrationQpsLimit:  uint32(cfg.PenetrationQpsLimit),
				})
				namespaceGroupCacheMap[namespaceGroup] = c
			}
			ca := NewLayerCache(namespace, c, confAccessor)
			layerCacheManager.setCache(ca)
		}
		newLayerCacheManager = layerCacheManager
	})
	return newLayerCacheManager
}

func (m *LayerCacheManager) Get(ctx context.Context, namespace Namespace, id string, opts ...LevelOption) (interface{}, error) {
	c, err := m.GetCacheByNamespace(ctx, namespace)
	if err != nil {
		return nil, err
	}
	return c.Get(ctx, id, opts...)
}

func (m *LayerCacheManager) Set(ctx context.Context, namespace Namespace, id string, obj interface{}, opts ...LevelOption) error {
	c, err := m.GetCacheByNamespace(ctx, namespace)
	if err != nil {
		return err
	}

	// 分离选项：缓存选项和异步选项
	cacheOpts, hasAsync := SeparateOptions(opts)

	// 根据是否有异步选项决定处理方式
	if hasAsync {
		return m.asyncSet(ctx, c, id, obj, cacheOpts)
	}

	// 同步写入
	return c.Set(ctx, id, obj, cacheOpts...)
}

func (m *LayerCacheManager) GetCacheByNamespace(ctx context.Context, namespace Namespace) (LayerCache, error) {
	c, ok := m.getCache(namespace)
	if !ok {
		sendMonitor(ctx, namespace, "", constant.StatusNamespaceNotFound)
		return nil, errNamespaceNotFound
	}
	return c, nil
}

func (m *LayerCacheManager) getCache(namespace Namespace) (LayerCache, bool) {
	if m == nil || m.caches == nil {
		return nil, false
	}
	c, ok := m.caches[namespace]
	return c, ok
}

func (m *LayerCacheManager) setCache(layerCache LayerCache) {
	if m == nil || m.caches == nil {
		return
	}
	m.caches[layerCache.Namespace()] = layerCache
}

// GetAsyncWriterStats 获取异步写入器统计信息（用于监控）
func (m *LayerCacheManager) GetAsyncWriterStats() (running, free, cap int) {
	if m == nil || m.asyncWriter == nil {
		return 0, 0, 0
	}
	return m.asyncWriter.GetPoolStats()
}

// StopAsyncWriter 停止异步写入器
func (m *LayerCacheManager) StopAsyncWriter() {
	if m == nil || m.asyncWriter == nil {
		return
	}
	m.asyncWriter.Stop()
}

// Manager 层的异步写入实现
func (m *LayerCacheManager) asyncSet(ctx context.Context, cache LayerCache, id string, obj interface{}, cacheOpts []LevelOption) error {
	if m.asyncWriter == nil {
		// 降级到同步写入
		return cache.Set(ctx, id, obj, cacheOpts...)
	}

	return m.asyncWriter.SubmitAsyncWrite(ctx, AsyncWriteTask{
		Namespace: cache.Namespace(),
		ID:        id,
		Write: func() error {
			return cache.Set(ctx, id, obj, cacheOpts...)
		},
	})
}

func updateMemoryCacheConfig(source, target mutable_application_config.LayerCacheNamespaceConfig) mutable_application_config.LayerCacheNamespaceConfig {
	if target.MemSize != 0 {
		source.MemSize = target.MemSize
	}
	if target.MemNumOfCounters != 0 {
		source.MemNumOfCounters = target.MemNumOfCounters
	}
	if target.MemPhysicalTTL != 0 {
		source.MemPhysicalTTL = target.MemPhysicalTTL
	}
	if target.MemLogicTTL != 0 {
		source.MemLogicTTL = target.MemLogicTTL
	}
	return source
}

// GetAsyncWriterStats 获取异步写入器统计信息（用于监控，保持向后兼容性）
func GetAsyncWriterStats() (running, free, cap int) {
	if newLayerCacheManager == nil {
		return 0, 0, 0
	}
	return newLayerCacheManager.GetAsyncWriterStats()
}
