package layer_cache

import "time"

// 缓存过期时间常量
const (
	// DefaultCacheTTL 默认缓存过期时间（5分钟）
	DefaultCacheTTL = uint32(5 * 60)

	// ShortCacheTTL 短期缓存过期时间（1分钟）
	ShortCacheTTL = uint32(1 * 60)

	// LongCacheTTL 长期缓存过期时间（30分钟）
	LongCacheTTL = uint32(30 * 60)

	// WarmCacheTTL 预热缓存过期时间（5分钟）
	WarmCacheTTL = uint32(5 * 60)
)

// 缓存过期时间（time.Duration格式，用于业务逻辑）
const (
	DefaultCacheDuration = 5 * time.Minute
	ShortCacheDuration   = 1 * time.Minute
	LongCacheDuration    = 30 * time.Minute
	WarmCacheDuration    = 5 * time.Minute
)
