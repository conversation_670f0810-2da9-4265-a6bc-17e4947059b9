package cache

type Name string

// LruCacheName LRU缓存实例名称
const (
	EntityTagLruName   Name = "EntityTagApi"
	TagValueLruName    Name = "TagValueApi"
	ShopChannelLruName Name = "ShopChannelApi"
)

// LocalCacheName 本地缓存实例名称
const (
	Channels          Name = "Channels"
	WarehouseChannels Name = "WarehouseChannels"
	ApiTokens         Name = "ApiTokens"
	MPChannels        Name = "MPChannels"
)

// LayerCacheName 分层缓存实例名称
const ()

// MultiLayerCacheName 多层缓存实例名称
const (
	ItemInfoLruName              Name = "ItemInfoApi"
	SocVersionCacheName          Name = "SoCVersion"
	SoCServiceabilityCacheName   Name = "SOCServiceability"
	WarehousePriorityCacheName   Name = "WarehousePriority"
	ShopLocalSipCacheName        Name = "ShopLocalSIP"
	ShopWhPriorityCacheName      Name = "ShopWarehousePriority"
	ShopWhitelistDetailCacheName Name = "ShopWhitelistDetailCache"
	ShopWarehouseListCacheName   Name = "ShopWarehouseList"
	ShopWarehouseGeoCacheName    Name = "ShopWarehouseGeo"
	ShopSellerUserIdCacheName    Name = "ShopSellerUserId"
	ShopMultiWHFlagCacheName     Name = "ShopMultiWHFlag"
	SBSShipmentGroup             Name = "SBSShipmentGroup"
)
