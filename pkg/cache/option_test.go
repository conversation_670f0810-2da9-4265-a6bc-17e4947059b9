package cache

import (
	"context"
	"testing"
	"time"

	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
)

// 测试用的简单结构体
type TestStruct struct {
	Name  string `json:"name"`
	Value int    `json:"value"`
}

// 测试用的 protobuf 消息
type TestProto struct {
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
}

func (m *TestProto) Reset()         { *m = TestProto{} }
func (m *TestProto) String() string { return proto.CompactTextString(m) }
func (m *TestProto) ProtoMessage()  {}

func TestInitOption(t *testing.T) {
	tests := []struct {
		name     string
		opts     []Options[string, int]
		expected Option[string, int]
	}{
		{
			name: "空选项",
			opts: []Options[string, int]{},
			expected: Option[string, int]{
				Timeout:              0,
				WithoutRemoteTimeout: false,
				RemoteTimeout:        0,
				MarshalFunc:          nil,
				UnmarshalFunc:        nil,
				KeyConvertor:         nil,
			},
		},
		{
			name: "单个选项",
			opts: []Options[string, int]{
				WithTimeout[string, int](time.Minute),
			},
			expected: Option[string, int]{
				Timeout:              time.Minute,
				WithoutRemoteTimeout: false,
				RemoteTimeout:        0,
				MarshalFunc:          nil,
				UnmarshalFunc:        nil,
				KeyConvertor:         nil,
			},
		},
		{
			name: "多个选项",
			opts: []Options[string, int]{
				WithTimeout[string, int](time.Minute),
				WithRemoteTimeout[string, int](time.Second * 30),
				WithoutRemoteTimeout[string, int](),
			},
			expected: Option[string, int]{
				Timeout:              time.Minute,
				WithoutRemoteTimeout: true,
				RemoteTimeout:        time.Second * 30,
				MarshalFunc:          nil,
				UnmarshalFunc:        nil,
				KeyConvertor:         nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := InitOption(tt.opts...)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestWithOptions(t *testing.T) {
	original := Option[string, int]{
		Timeout:              time.Minute,
		WithoutRemoteTimeout: true,
		RemoteTimeout:        time.Second * 30,
	}

	opt := WithOptions(original)
	var result Option[string, int]
	opt(&result)

	assert.Equal(t, original, result)
}

func TestWithTimeout(t *testing.T) {
	tests := []struct {
		name     string
		timeout  time.Duration
		expected time.Duration
	}{
		{"零超时", 0, 0},
		{"1秒", time.Second, time.Second},
		{"1分钟", time.Minute, time.Minute},
		{"1小时", time.Hour, time.Hour},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			opt := WithTimeout[string, int](tt.timeout)
			var result Option[string, int]
			opt(&result)

			assert.Equal(t, tt.expected, result.Timeout)
		})
	}
}

func TestDefaultTimeoutOptions(t *testing.T) {
	tests := []struct {
		name     string
		opt      Options[string, int]
		expected time.Duration
	}{
		{"默认超时", WithDefaultTimeout[string, int](), DefaultCacheDuration},
		{"短超时", WithShortTimeout[string, int](), ShortCacheDuration},
		{"预热超时", WithWarnTimeout[string, int](), WarmCacheDuration},
		{"长超时", WithLongTimeout[string, int](), LongCacheDuration},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result Option[string, int]
			tt.opt(&result)

			assert.Equal(t, tt.expected, result.Timeout)
		})
	}
}

func TestRemoteTimeoutOptions(t *testing.T) {
	tests := []struct {
		name     string
		opt      Options[string, int]
		expected time.Duration
		field    string
	}{
		{"默认远程超时", WithDefaultRemoteTimeout[string, int](), DefaultCacheDuration, "RemoteTimeout"},
		{"短远程超时", WithRemoteShortTimeout[string, int](), ShortCacheDuration, "Timeout"},
		{"预热远程超时", WithRemoteWarnTimeout[string, int](), WarmCacheDuration, "Timeout"},
		{"长远程超时", WithRemoteLongTimeout[string, int](), LongCacheDuration, "Timeout"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result Option[string, int]
			tt.opt(&result)

			if tt.field == "RemoteTimeout" {
				assert.Equal(t, tt.expected, result.RemoteTimeout)
			} else {
				assert.Equal(t, tt.expected, result.Timeout)
			}
		})
	}
}

func TestWithoutRemoteTimeout(t *testing.T) {
	opt := WithoutRemoteTimeout[string, int]()
	var result Option[string, int]
	opt(&result)

	assert.True(t, result.WithoutRemoteTimeout)
}

func TestWithMarshalFunc(t *testing.T) {
	marshalFunc := func(ctx context.Context, data int) (string, error) {
		return "test", nil
	}

	opt := WithMarshalFunc[string, int](marshalFunc)
	var result Option[string, int]
	opt(&result)

	assert.NotNil(t, result.MarshalFunc)
}

func TestWithUnmarshalFunc(t *testing.T) {
	unmarshalFunc := func(ctx context.Context, data string) (int, error) {
		return 42, nil
	}

	opt := WithUnmarshalFunc[string, int](unmarshalFunc)
	var result Option[string, int]
	opt(&result)

	assert.NotNil(t, result.UnmarshalFunc)
}

func TestWithKeyConvertor(t *testing.T) {
	keyConvertor := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	opt := WithKeyConvertor[string, int](keyConvertor)
	var result Option[string, int]
	opt(&result)

	assert.NotNil(t, result.KeyConvertor)
}

func TestJsonUnmarshal(t *testing.T) {
	tests := []struct {
		name    string
		data    string
		want    TestStruct
		wantErr bool
	}{
		{
			name:    "有效JSON",
			data:    `{"name":"test","value":42}`,
			want:    TestStruct{Name: "test", Value: 42},
			wantErr: false,
		},
		{
			name:    "无效JSON",
			data:    `{"name":"test","value":42`,
			want:    TestStruct{},
			wantErr: true,
		},
		{
			name:    "空字符串",
			data:    "",
			want:    TestStruct{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := JsonUnmarshal[TestStruct](ctx, tt.data)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, result)
			}
		})
	}
}

func TestPbUnmarshal(t *testing.T) {
	// 跳过 Protobuf 测试，因为需要正确的 protobuf 定义
	t.Skip("Protobuf 测试需要正确的 protobuf 定义文件")
}

func TestStringUnmarshal(t *testing.T) {
	tests := []struct {
		name string
		data string
		want string
	}{
		{"普通字符串", "hello", "hello"},
		{"空字符串", "", ""},
		{"特殊字符", "test@123", "test@123"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := StringUnmarshal[string](ctx, tt.data)

			assert.NoError(t, err)
			assert.Equal(t, tt.want, result)
		})
	}
}

func TestNumberUnmarshal(t *testing.T) {
	tests := []struct {
		name    string
		data    string
		want    int
		wantErr bool
	}{
		{"正整数", "42", 42, false},
		{"负整数", "-42", -42, false},
		{"零", "0", 0, false},
		{"无效数字", "abc", 0, true},
		{"空字符串", "", 0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := NumberUnmarshal[int](ctx, tt.data)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, result)
			}
		})
	}
}

func TestBoolUnmarshal(t *testing.T) {
	tests := []struct {
		name    string
		data    string
		want    bool
		wantErr bool
	}{
		{"true", "true", true, false},
		{"false", "false", false, false},
		{"1", "1", true, false},
		{"0", "0", false, false},
		{"无效值", "invalid", false, true},
		{"空字符串", "", false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := BoolUnmarshal[bool](ctx, tt.data)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, result)
			}
		})
	}
}

func TestJsonMarshal(t *testing.T) {
	tests := []struct {
		name    string
		data    TestStruct
		want    string
		wantErr bool
	}{
		{
			name:    "正常结构体",
			data:    TestStruct{Name: "test", Value: 42},
			want:    `{"name":"test","value":42}`,
			wantErr: false,
		},
		{
			name:    "空结构体",
			data:    TestStruct{},
			want:    `{"name":"","value":0}`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := JsonMarshal(ctx, tt.data)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, result)
			}
		})
	}
}

func TestPbMarshal(t *testing.T) {
	// 跳过 Protobuf 测试，因为需要正确的 protobuf 定义
	t.Skip("Protobuf 测试需要正确的 protobuf 定义文件")
}

func TestStringMarshal(t *testing.T) {
	tests := []struct {
		name string
		data string
		want string
	}{
		{"普通字符串", "hello", "hello"},
		{"空字符串", "", ""},
		{"特殊字符", "test@123", "test@123"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := StringMarshal(ctx, tt.data)

			assert.NoError(t, err)
			assert.Equal(t, tt.want, result)
		})
	}
}

func TestNumberMarshal(t *testing.T) {
	tests := []struct {
		name string
		data int
		want string
	}{
		{"正整数", 42, "42"},
		{"负整数", -42, "-42"},
		{"零", 0, "0"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := NumberMarshal(ctx, tt.data)

			assert.NoError(t, err)
			assert.Equal(t, tt.want, result)
		})
	}
}

func TestBoolMarshal(t *testing.T) {
	tests := []struct {
		name string
		data bool
		want string
	}{
		{"true", true, "true"},
		{"false", false, "false"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := BoolMarshal(ctx, tt.data)

			assert.NoError(t, err)
			assert.Equal(t, tt.want, result)
		})
	}
}

func TestConstants(t *testing.T) {
	assert.Equal(t, 60*time.Second, DefaultCacheDuration)
	assert.Equal(t, 1*time.Minute, ShortCacheDuration)
	assert.Equal(t, 30*time.Minute, LongCacheDuration)
	assert.Equal(t, 5*time.Minute, WarmCacheDuration)
}

func TestErrors(t *testing.T) {
	assert.Equal(t, "cache not found", ErrCacheNotFound.Error())
	assert.Equal(t, "cache not enable", ErrCacheNotEnable.Error())
	assert.Equal(t, "key not found", ErrKeyNotFound.Error())
	assert.Equal(t, "key expired", ErrKeyExpired.Error())
	assert.Equal(t, "key not convertor not found", ErrKeyConvertorNotFound.Error())
	assert.Equal(t, "marshal function not found", ErrMarshalFuncNotFound.Error())
	assert.Equal(t, "unmarshal function not found", ErrUnmarshalFuncNotFound.Error())
	assert.Equal(t, "eviction", ErrEviction.Error())
}

// 测试选项函数的空指针处理
func TestOptionsWithNilPointer(t *testing.T) {
	tests := []struct {
		name string
		opt  Options[string, int]
	}{
		{"WithTimeout", WithTimeout[string, int](time.Minute)},
		{"WithRemoteTimeout", WithRemoteTimeout[string, int](time.Minute)},
		{"WithoutRemoteTimeout", WithoutRemoteTimeout[string, int]()},
		{"WithDefaultTimeout", WithDefaultTimeout[string, int]()},
		{"WithShortTimeout", WithShortTimeout[string, int]()},
		{"WithWarnTimeout", WithWarnTimeout[string, int]()},
		{"WithLongTimeout", WithLongTimeout[string, int]()},
		{"WithDefaultRemoteTimeout", WithDefaultRemoteTimeout[string, int]()},
		{"WithRemoteShortTimeout", WithRemoteShortTimeout[string, int]()},
		{"WithRemoteWarnTimeout", WithRemoteWarnTimeout[string, int]()},
		{"WithRemoteLongTimeout", WithRemoteLongTimeout[string, int]()},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试传入 nil 指针
			tt.opt(nil)
			// 不应该 panic
		})
	}
}

// 测试 Marshal 和 Unmarshal 函数的组合使用
func TestMarshalUnmarshalRoundTrip(t *testing.T) {
	tests := []struct {
		name string
		test func(t *testing.T)
	}{
		{
			name: "JSON round trip",
			test: func(t *testing.T) {
				original := TestStruct{Name: "test", Value: 42}
				ctx := context.Background()

				// Marshal
				marshaled, err := JsonMarshal(ctx, original)
				assert.NoError(t, err)

				// Unmarshal
				unmarshaled, err := JsonUnmarshal[TestStruct](ctx, marshaled)
				assert.NoError(t, err)

				assert.Equal(t, original, unmarshaled)
			},
		},
		{
			name: "Protobuf round trip",
			test: func(t *testing.T) {
				t.Skip("Protobuf 测试需要正确的 protobuf 定义文件")
			},
		},
		{
			name: "String round trip",
			test: func(t *testing.T) {
				original := "test"
				ctx := context.Background()

				// Marshal
				marshaled, err := StringMarshal(ctx, original)
				assert.NoError(t, err)

				// Unmarshal
				unmarshaled, err := StringUnmarshal[string](ctx, marshaled)
				assert.NoError(t, err)

				assert.Equal(t, original, unmarshaled)
			},
		},
		{
			name: "Number round trip",
			test: func(t *testing.T) {
				original := 42
				ctx := context.Background()

				// Marshal
				marshaled, err := NumberMarshal(ctx, original)
				assert.NoError(t, err)

				// Unmarshal
				unmarshaled, err := NumberUnmarshal[int](ctx, marshaled)
				assert.NoError(t, err)

				assert.Equal(t, original, unmarshaled)
			},
		},
		{
			name: "Bool round trip",
			test: func(t *testing.T) {
				original := true
				ctx := context.Background()

				// Marshal
				marshaled, err := BoolMarshal(ctx, original)
				assert.NoError(t, err)

				// Unmarshal
				unmarshaled, err := BoolUnmarshal[bool](ctx, marshaled)
				assert.NoError(t, err)

				assert.Equal(t, original, unmarshaled)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, tt.test)
	}
}
