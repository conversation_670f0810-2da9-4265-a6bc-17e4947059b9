package localcache

import (
	"context"
	"sync"

	"github.com/dolthub/swiss"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/recordutil"
)

type LocalCache[K comparable, V any] interface {
	Get(ctx context.Context, key K) (V, bool)
	MGet(ctx context.Context, keys []K) map[K]V
	Range(ctx context.Context, callback func(context.Context, K, V) (stop bool))
	All(ctx context.Context) map[K]V
	Count(ctx context.Context) int
	ReplaceAll(data *swiss.Map[K, V])
}

// 具体实现类
type swissMapCache[K comparable, V any] struct {
	mu sync.RWMutex
	m  *swiss.Map[K, V]

	wrapOfGet   func(ctx context.Context, key K) (V, bool)
	wrapOfMGet  func(ctx context.Context, keys []K) map[K]V
	wrapOfAll   func(ctx context.Context) map[K]V
	wrapOfIter  func(ctx context.Context, callback func(context.Context, K, V) (stop bool))
	wrapOfCount func(ctx context.Context) int
}

// New 创建缓存实例（返回接口类型）
func New[K comparable, V any]() LocalCache[K, V] {
	c := &swissMapCache[K, V]{
		m: swiss.NewMap[K, V](0),
	}
	c.wrapOfGet = recordutil.Wrap(c.get)
	c.wrapOfMGet = recordutil.Wrap(c.mGet)
	c.wrapOfAll = recordutil.Wrap(c.all)
	c.wrapOfIter = recordutil.Wrap(c.iter)
	c.wrapOfCount = recordutil.Wrap(c.count)
	return c
}

// Get 获取单个键值
func (c *swissMapCache[K, V]) Get(ctx context.Context, key K) (V, bool) {
	if chassis_config.IsRecordEnable() {
		return c.wrapOfGet(ctx, key)
	}
	return c.get(ctx, key)
}

func (c *swissMapCache[K, V]) get(ctx context.Context, key K) (V, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.m.Get(key)
}

// MGet 批量获取键值（返回map[K]V）
func (c *swissMapCache[K, V]) MGet(ctx context.Context, keys []K) map[K]V {
	if chassis_config.IsRecordEnable() {
		return c.wrapOfMGet(ctx, keys)
	}
	return c.mGet(ctx, keys)
}

func (c *swissMapCache[K, V]) mGet(ctx context.Context, keys []K) map[K]V {
	if len(keys) == 0 {
		return make(map[K]V)
	}
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make(map[K]V, len(keys))
	for _, k := range keys {
		if v, ok := c.m.Get(k); ok {
			result[k] = v
		}
	}
	return result
}

func (c *swissMapCache[K, V]) Range(ctx context.Context, callback func(context.Context, K, V) (stop bool)) {
	if chassis_config.IsRecordEnable() {
		c.wrapOfIter(ctx, callback)
		return
	}
	c.iter(ctx, callback)
}

func (c *swissMapCache[K, V]) iter(ctx context.Context, f func(context.Context, K, V) (stop bool)) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	c.m.Iter(func(k K, v V) bool {
		return f(ctx, k, v)
	})
}

// All 获取全部键值（返回map[K]V）
func (c *swissMapCache[K, V]) All(ctx context.Context) map[K]V {
	if chassis_config.IsRecordEnable() {
		return c.wrapOfAll(ctx)
	}
	return c.all(ctx)
}

func (c *swissMapCache[K, V]) all(context.Context) map[K]V {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make(map[K]V, c.m.Count())
	c.m.Iter(func(k K, v V) bool {
		result[k] = v
		return false
	})
	return result
}

func (c *swissMapCache[K, V]) Count(ctx context.Context) int {
	if chassis_config.IsRecordEnable() {
		return c.wrapOfCount(ctx)
	}
	return c.count(ctx)
}

func (c *swissMapCache[K, V]) count(context.Context) int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.m.Count()
}

// ReplaceAll 全量替换缓存（接受swiss.Map输入）
func (c *swissMapCache[K, V]) ReplaceAll(data *swiss.Map[K, V]) {
	c.mu.Lock()
	defer c.mu.Unlock()
	if data == nil {
		data = swiss.NewMap[K, V](0)
	}
	c.m = data
}
