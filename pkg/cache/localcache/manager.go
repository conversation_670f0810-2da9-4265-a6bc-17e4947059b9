package localcache

import (
	"context"
	"errors"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"github.com/dolthub/swiss"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

const (
	smallNumber      = 1000
	mediumNumber     = 10000
	largeNumber      = 100000
	superLargeNumber = 500000
)

// CAT reporter for local cache
const (
	// cat domain
	localCacheSize = "localCacheSize"
	// cat interface
	normalNumberReport     = "normalNumberReport(<1000)"
	smallNumberReport      = "smallNumber(>=1000)"
	mediumNumberReport     = "mediumNumber(>=10000)"
	largeNumberReport      = "largeNumber(>=100000)"
	superLargeNumberReport = "superLargeNumber(>=500000)"
)

var (
	localCacheNotFound = errors.New("local cache not found")
)

// 定义 Dependency 结构体
type Dependency struct {
	ConfAccessor config.ConfAccessor
}

// 函数式 loader
type LoaderFunc[K comparable, V any] func(ctx context.Context, confAccessor config.ConfAccessor) (*swiss.Map[K, V], error)

// 全局 CacheManager
var globalCacheManager *CacheManager

// CacheOption 是一个函数类型，用于设置 CacheConfig 的选项
type CacheOption func(*CacheConfig)

// WithInterval 设置自动 reload 的周期
func WithInterval(interval time.Duration) CacheOption {
	return func(cfg *CacheConfig) {
		cfg.Interval = interval
	}
}

// CacheConfig 配置结构，可拓展
type CacheConfig struct {
	Interval time.Duration // 自动 reload 的周期
}

// NewCacheConfig 创建一个新的 CacheConfig，并应用给定的选项
func NewCacheConfig(opts ...CacheOption) CacheConfig {
	cfg := CacheConfig{
		Interval: 10 * time.Minute, // 默认 10 分钟
	}
	for _, opt := range opts {
		opt(&cfg)
	}
	return cfg
}

// CacheManager 管理缓存的结构体
type CacheManager struct {
	mu         sync.RWMutex
	caches     map[cache.Name]cacheInit
	dependency Dependency
}

// NewManager 初始化全局 CacheManager
func NewManager(confAccessor config.ConfAccessor) *CacheManager {
	dep := Dependency{
		ConfAccessor: confAccessor,
	}
	if globalCacheManager == nil {
		globalCacheManager = &CacheManager{
			caches:     make(map[cache.Name]cacheInit),
			dependency: dep,
		}
	}
	return globalCacheManager
}

// cacheInit 接口定义
type cacheInit interface {
	start(ctx context.Context)
	wait() error
}

// managedCache 结构体定义
type managedCache[K comparable, V any] struct {
	name       cache.Name
	cache      LocalCache[K, V]
	loader     LoaderFunc[K, V]
	cfg        CacheConfig
	ticker     *time.Ticker
	init       chan error
	dependency Dependency
}

// Register 注册缓存
func Register[K comparable, V any](
	name cache.Name,
	loader LoaderFunc[K, V],
	opts ...CacheOption,
) {
	if globalCacheManager == nil {
		return
	}
	globalCacheManager.mu.Lock()
	defer globalCacheManager.mu.Unlock()

	if _, exists := globalCacheManager.caches[name]; exists {
		panic("duplicate cache name: " + name)
	}

	cfg := NewCacheConfig(opts...)

	mc := &managedCache[K, V]{
		name:       name,
		cache:      New[K, V](),
		loader:     loader,
		cfg:        cfg,
		ticker:     time.NewTicker(cfg.Interval),
		init:       make(chan error),
		dependency: globalCacheManager.dependency,
	}
	globalCacheManager.caches[name] = mc
}

// GetCache 获取缓存
func GetCache[K comparable, V any](name cache.Name) (LocalCache[K, V], error) {
	if globalCacheManager == nil {
		return nil, localCacheNotFound
	}
	globalCacheManager.mu.RLock()
	defer globalCacheManager.mu.RUnlock()

	ci, ok := globalCacheManager.get(name)
	if !ok {
		return nil, localCacheNotFound
	}
	cacheItem, ok := ci.(*managedCache[K, V])
	if !ok {
		return nil, localCacheNotFound
	}
	return cacheItem.cache, nil
}

// managedCache 方法实现
func (mc *managedCache[K, V]) start(ctx context.Context) {
	go func() {
		initErr := mc.loadCache(ctx, mc.dependency)
		mc.init <- initErr
		defer mc.ticker.Stop()
		for {
			select {
			case <-mc.ticker.C:
				err := mc.loadCache(ctx, mc.dependency)
				if err != nil {
					return
				}
			}
		}
	}()
}

func (mc *managedCache[K, V]) wait() error {
	return <-mc.init
}

func (mc *managedCache[K, V]) loadCache(ctx context.Context, dep Dependency) error {
	loadCtx, reportFunc := monitor.AwesomeReportTransactionStart2(ctx)
	var (
		data *swiss.Map[K, V]
		err  error
	)
	defer func() {
		if err != nil {
			reportFunc(constant.CatModuleLocalCache, string(mc.name), monitor.StatusError, err.Error())
		} else {
			reportFunc(constant.CatModuleLocalCache, string(mc.name), monitor.StatusSuccess, "")
		}
	}()

	data, err = mc.loader(loadCtx, dep.ConfAccessor)
	if err != nil {
		Logger.CtxLogErrorf(loadCtx, "[localcache] load failed for %s: %v", mc.name, err)
		return err
	}

	numKeys := data.Count()
	var dataScale string
	if numKeys >= superLargeNumber {
		dataScale = superLargeNumberReport
	} else if numKeys >= largeNumber {
		dataScale = largeNumberReport
	} else if numKeys >= mediumNumber {
		dataScale = mediumNumberReport
	} else if numKeys >= smallNumber {
		dataScale = smallNumberReport
	} else {
		dataScale = normalNumberReport
	}

	mc.cache.ReplaceAll(data)

	_ = monitor.AwesomeReportEvent(ctx, localCacheSize, dataScale+"|"+string(mc.name), constant.StatusSuccess, "")
	return nil
}

func (m *CacheManager) Init(ctx context.Context) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var wg sync.WaitGroup
	errChan := make(chan error, len(m.caches))
	done := make(chan struct{})

	// 启动所有缓存加载
	for _, cache := range m.caches {
		cache.start(ctx)
	}

	// 监听错误
	go func() {
		for _, cache := range m.caches {
			wg.Add(1)
			go func(c cacheInit) {
				defer wg.Done()
				if err := c.wait(); err != nil {
					errChan <- err
				}
			}(cache)
		}
		wg.Wait()
		close(done)
	}()

	// 等待第一个错误或完成
	select {
	case err := <-errChan:
		return err
	case <-done:
		return nil
	}
}

func (m *CacheManager) get(name cache.Name) (cacheInit, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	c, ok := m.caches[name]
	return c, ok
}
