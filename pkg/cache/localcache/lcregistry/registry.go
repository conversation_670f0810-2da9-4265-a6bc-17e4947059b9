package lcregistry

import (
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/api_token"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/sbslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/localcache"
)

func Register() {
	localcache.Register(cache.Channels, lpslib.DumpChannels, localcache.WithInterval(5*time.Minute))
	localcache.Register(cache.WarehouseChannels, sbslib.DumpWarehouseChannels, localcache.WithInterval(5*time.Minute))
	localcache.Register(cache.ApiTokens, api_token.DumpAPITokens, localcache.WithInterval(5*time.Minute))
	localcache.Register(cache.MPChannels, spexlib.DumpMPChannels, localcache.WithInterval(5*time.Minute))
}
