package localcache

import (
	"context"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/dolthub/swiss"
	"github.com/stretchr/testify/assert"
)

func TestNew(t *testing.T) {
	t.Run("创建整数-字符串缓存", func(t *testing.T) {
		cache := New[int, string]()
		assert.NotNil(t, cache)

		// 验证缓存为空
		ctx := context.Background()
		assert.Equal(t, 0, cache.Count(ctx))
	})

	t.Run("创建字符串-整数缓存", func(t *testing.T) {
		cache := New[string, int]()
		assert.NotNil(t, cache)

		ctx := context.Background()
		assert.Equal(t, 0, cache.Count(ctx))
	})

	t.Run("创建结构体类型缓存", func(t *testing.T) {
		type TestStruct struct {
			ID   int
			Name string
		}
		cache := New[string, TestStruct]()
		assert.NotNil(t, cache)

		ctx := context.Background()
		assert.Equal(t, 0, cache.Count(ctx))
	})
}

func TestLocalCache_Get(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("获取不存在的键", func(t *testing.T) {
		value, ok := cache.Get(ctx, "nonexistent")
		assert.False(t, ok)
		assert.Equal(t, 0, value) // 零值
	})

	t.Run("获取存在的键", func(t *testing.T) {
		// 先通过 ReplaceAll 添加数据
		data := swiss.NewMap[string, int](1)
		data.Put("key1", 100)
		cache.ReplaceAll(data)

		value, ok := cache.Get(ctx, "key1")
		assert.True(t, ok)
		assert.Equal(t, 100, value)
	})

	t.Run("获取空字符串键", func(t *testing.T) {
		data := swiss.NewMap[string, int](1)
		data.Put("", 42)
		cache.ReplaceAll(data)

		value, ok := cache.Get(ctx, "")
		assert.True(t, ok)
		assert.Equal(t, 42, value)
	})
}

func TestLocalCache_MGet(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("批量获取空键列表", func(t *testing.T) {
		result := cache.MGet(ctx, []string{})
		assert.Empty(t, result)
	})

	t.Run("批量获取不存在的键", func(t *testing.T) {
		result := cache.MGet(ctx, []string{"key1", "key2", "key3"})
		assert.Empty(t, result)
	})

	t.Run("批量获取部分存在的键", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](2)
		data.Put("key1", 100)
		data.Put("key3", 300)
		cache.ReplaceAll(data)

		result := cache.MGet(ctx, []string{"key1", "key2", "key3"})
		assert.Len(t, result, 2)
		assert.Equal(t, 100, result["key1"])
		assert.Equal(t, 300, result["key3"])
		assert.NotContains(t, result, "key2")
	})

	t.Run("批量获取全部存在的键", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](3)
		data.Put("key1", 100)
		data.Put("key2", 200)
		data.Put("key3", 300)
		cache.ReplaceAll(data)

		result := cache.MGet(ctx, []string{"key1", "key2", "key3"})
		assert.Len(t, result, 3)
		assert.Equal(t, 100, result["key1"])
		assert.Equal(t, 200, result["key2"])
		assert.Equal(t, 300, result["key3"])
	})

	t.Run("批量获取重复键", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](1)
		data.Put("key1", 100)
		cache.ReplaceAll(data)

		result := cache.MGet(ctx, []string{"key1", "key1", "key1"})
		assert.Len(t, result, 1)
		assert.Equal(t, 100, result["key1"])
	})
}

func TestLocalCache_Range(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("遍历空缓存", func(t *testing.T) {
		callCount := 0
		cache.Range(ctx, func(ctx context.Context, key string, value int) bool {
			callCount++
			return false
		})
		assert.Equal(t, 0, callCount)
	})

	t.Run("遍历有数据的缓存", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](3)
		data.Put("key1", 100)
		data.Put("key2", 200)
		data.Put("key3", 300)
		cache.ReplaceAll(data)

		visited := make(map[string]int)
		cache.Range(ctx, func(ctx context.Context, key string, value int) bool {
			visited[key] = value
			return false
		})

		assert.Len(t, visited, 3)
		assert.Equal(t, 100, visited["key1"])
		assert.Equal(t, 200, visited["key2"])
		assert.Equal(t, 300, visited["key3"])
	})

	t.Run("遍历时提前停止", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](5)
		for i := 1; i <= 5; i++ {
			data.Put("key"+strconv.Itoa(i), i*100)
		}
		cache.ReplaceAll(data)

		callCount := 0
		cache.Range(ctx, func(ctx context.Context, key string, value int) bool {
			callCount++
			return callCount >= 3 // 访问3次后停止
		})

		assert.Equal(t, 3, callCount)
	})
}

func TestLocalCache_All(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("获取空缓存的所有数据", func(t *testing.T) {
		result := cache.All(ctx)
		assert.Empty(t, result)
	})

	t.Run("获取有数据缓存的所有数据", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](3)
		data.Put("key1", 100)
		data.Put("key2", 200)
		data.Put("key3", 300)
		cache.ReplaceAll(data)

		result := cache.All(ctx)
		assert.Len(t, result, 3)
		assert.Equal(t, 100, result["key1"])
		assert.Equal(t, 200, result["key2"])
		assert.Equal(t, 300, result["key3"])
	})

	t.Run("获取单个数据缓存的所有数据", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](1)
		data.Put("single", 42)
		cache.ReplaceAll(data)

		result := cache.All(ctx)
		assert.Len(t, result, 1)
		assert.Equal(t, 42, result["single"])
	})
}

func TestLocalCache_Count(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("空缓存计数", func(t *testing.T) {
		assert.Equal(t, 0, cache.Count(ctx))
	})

	t.Run("有数据缓存计数", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](3)
		data.Put("key1", 100)
		data.Put("key2", 200)
		data.Put("key3", 300)
		cache.ReplaceAll(data)

		assert.Equal(t, 3, cache.Count(ctx))
	})

	t.Run("单个数据缓存计数", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](1)
		data.Put("single", 42)
		cache.ReplaceAll(data)

		assert.Equal(t, 1, cache.Count(ctx))
	})
}

func TestLocalCache_ReplaceAll(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("空数据替换", func(t *testing.T) {
		data := swiss.NewMap[string, int](0)
		cache.ReplaceAll(data)

		assert.Equal(t, 0, cache.Count(ctx))
	})

	t.Run("替换为新数据", func(t *testing.T) {
		// 先添加一些数据
		data1 := swiss.NewMap[string, int](2)
		data1.Put("old1", 1)
		data1.Put("old2", 2)
		cache.ReplaceAll(data1)

		assert.Equal(t, 2, cache.Count(ctx))

		// 替换为新数据
		data2 := swiss.NewMap[string, int](3)
		data2.Put("new1", 100)
		data2.Put("new2", 200)
		data2.Put("new3", 300)
		cache.ReplaceAll(data2)

		assert.Equal(t, 3, cache.Count(ctx))

		// 验证新数据
		value, ok := cache.Get(ctx, "new1")
		assert.True(t, ok)
		assert.Equal(t, 100, value)

		// 验证旧数据不存在
		_, ok = cache.Get(ctx, "old1")
		assert.False(t, ok)
	})

	t.Run("从有数据替换为空数据", func(t *testing.T) {
		// 先添加数据
		data1 := swiss.NewMap[string, int](2)
		data1.Put("key1", 1)
		data1.Put("key2", 2)
		cache.ReplaceAll(data1)

		assert.Equal(t, 2, cache.Count(ctx))

		// 替换为空数据
		data2 := swiss.NewMap[string, int](0)
		cache.ReplaceAll(data2)

		assert.Equal(t, 0, cache.Count(ctx))
	})
}

func TestLocalCache_ConcurrentAccess(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("并发读取", func(t *testing.T) {
		// 添加测试数据
		data := swiss.NewMap[string, int](100)
		for i := 0; i < 100; i++ {
			data.Put("key"+strconv.Itoa(i), i)
		}
		cache.ReplaceAll(data)

		var wg sync.WaitGroup
		errors := make(chan error, 10)

		// 启动10个并发读取goroutine
		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				for j := 0; j < 100; j++ {
					key := "key" + strconv.Itoa(j)
					value, ok := cache.Get(ctx, key)
					if !ok {
						errors <- assert.AnError
						return
					}
					if value != j {
						errors <- assert.AnError
						return
					}
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// 验证没有错误
		for err := range errors {
			assert.NoError(t, err)
		}
	})

	t.Run("并发读取和写入", func(t *testing.T) {
		// 添加初始数据
		data := swiss.NewMap[string, int](50)
		for i := 0; i < 50; i++ {
			data.Put("key"+strconv.Itoa(i), i)
		}
		cache.ReplaceAll(data)

		var wg sync.WaitGroup
		done := make(chan bool)

		// 启动读取goroutine
		for i := 0; i < 5; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for {
					select {
					case <-done:
						return
					default:
						cache.Get(ctx, "key1")
						cache.MGet(ctx, []string{"key1", "key2", "key3"})
						cache.Count(ctx)
					}
				}
			}()
		}

		// 启动写入goroutine
		for i := 0; i < 3; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				for j := 0; j < 10; j++ {
					newData := swiss.NewMap[string, int](10)
					for k := 0; k < 10; k++ {
						newData.Put("key"+strconv.Itoa(k), k*10+id)
					}
					cache.ReplaceAll(newData)
					time.Sleep(time.Millisecond)
				}
			}(i)
		}

		// 等待一段时间后停止
		time.Sleep(100 * time.Millisecond)
		close(done)
		wg.Wait()

		// 验证缓存仍然可用
		assert.Equal(t, 10, cache.Count(ctx))
	})
}

func TestLocalCache_DifferentTypes(t *testing.T) {
	t.Run("字符串类型", func(t *testing.T) {
		cache := New[string, string]()
		ctx := context.Background()

		data := swiss.NewMap[string, string](2)
		data.Put("key1", "value1")
		data.Put("key2", "value2")
		cache.ReplaceAll(data)

		value, ok := cache.Get(ctx, "key1")
		assert.True(t, ok)
		assert.Equal(t, "value1", value)

		result := cache.MGet(ctx, []string{"key1", "key2"})
		assert.Len(t, result, 2)
		assert.Equal(t, "value1", result["key1"])
		assert.Equal(t, "value2", result["key2"])
	})

	t.Run("结构体类型", func(t *testing.T) {
		type TestStruct struct {
			ID   int
			Name string
		}

		cache := New[string, TestStruct]()
		ctx := context.Background()

		data := swiss.NewMap[string, TestStruct](2)
		data.Put("struct1", TestStruct{ID: 1, Name: "test1"})
		data.Put("struct2", TestStruct{ID: 2, Name: "test2"})
		cache.ReplaceAll(data)

		value, ok := cache.Get(ctx, "struct1")
		assert.True(t, ok)
		assert.Equal(t, 1, value.ID)
		assert.Equal(t, "test1", value.Name)

		result := cache.MGet(ctx, []string{"struct1", "struct2"})
		assert.Len(t, result, 2)
		assert.Equal(t, 1, result["struct1"].ID)
		assert.Equal(t, 2, result["struct2"].ID)
	})

	t.Run("指针类型", func(t *testing.T) {
		cache := New[string, *string]()
		ctx := context.Background()

		value1 := "pointer1"
		value2 := "pointer2"

		data := swiss.NewMap[string, *string](2)
		data.Put("ptr1", &value1)
		data.Put("ptr2", &value2)
		cache.ReplaceAll(data)

		result, ok := cache.Get(ctx, "ptr1")
		assert.True(t, ok)
		assert.NotNil(t, result)
		assert.Equal(t, "pointer1", *result)
	})
}

func TestLocalCache_EdgeCases(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("零值处理", func(t *testing.T) {
		data := swiss.NewMap[string, int](1)
		data.Put("zero", 0)
		cache.ReplaceAll(data)

		value, ok := cache.Get(ctx, "zero")
		assert.True(t, ok)
		assert.Equal(t, 0, value)
	})

	t.Run("nil context", func(t *testing.T) {
		data := swiss.NewMap[string, int](1)
		data.Put("key", 42)
		cache.ReplaceAll(data)

		// 即使 context 为 nil，也应该正常工作
		value, ok := cache.Get(nil, "key")
		assert.True(t, ok)
		assert.Equal(t, 42, value)
	})

	t.Run("空键处理", func(t *testing.T) {
		data := swiss.NewMap[string, int](1)
		data.Put("", 42)
		cache.ReplaceAll(data)

		value, ok := cache.Get(ctx, "")
		assert.True(t, ok)
		assert.Equal(t, 42, value)

		result := cache.MGet(ctx, []string{""})
		assert.Len(t, result, 1)
		assert.Equal(t, 42, result[""])
	})
}

func TestLocalCache_Performance(t *testing.T) {
	cache := New[string, int]()
	ctx := context.Background()

	t.Run("大量数据性能测试", func(t *testing.T) {
		// 添加大量数据
		const dataSize = 100000
		data := swiss.NewMap[string, int](dataSize)
		for i := 0; i < dataSize; i++ {
			data.Put("key"+strconv.Itoa(i), i)
		}

		start := time.Now()
		cache.ReplaceAll(data)
		replaceTime := time.Since(start)

		// 测试Get性能
		start = time.Now()
		for i := 0; i < dataSize; i++ {
			cache.Get(ctx, "key"+strconv.Itoa(i))
		}
		getTime := time.Since(start)

		// 测试MGet性能
		keys := make([]string, 100)
		for i := 0; i < 100; i++ {
			keys[i] = "key" + strconv.Itoa(i)
		}
		start = time.Now()
		for i := 0; i < 100; i++ {
			cache.MGet(ctx, keys)
		}
		mgetTime := time.Since(start)

		// 测试Count性能
		start = time.Now()
		for i := 0; i < 1000; i++ {
			cache.Count(ctx)
		}
		countTime := time.Since(start)

		t.Logf("性能测试结果 (数据量: %d):", dataSize)
		t.Logf("  ReplaceAll: %v", replaceTime)
		t.Logf("  Get (100000): %v", getTime)
		t.Logf("  MGet (100次, 每次100个键): %v", mgetTime)
		t.Logf("  Count (1000次): %v", countTime)

		// 验证数据正确性
		assert.Equal(t, dataSize, cache.Count(ctx))
		value, ok := cache.Get(ctx, "key999")
		assert.True(t, ok)
		assert.Equal(t, 999, value)
	})
}

func TestLocalCache_MemoryUsage(t *testing.T) {
	t.Run("内存使用测试", func(t *testing.T) {
		cache := New[string, string]()
		ctx := context.Background()

		// 添加数据
		dataSize := 100000
		data := swiss.NewMap[string, string](uint32(dataSize))
		for i := 0; i < dataSize; i++ {
			data.Put("key"+strconv.Itoa(i), "value"+strconv.Itoa(i))
		}
		cache.ReplaceAll(data)

		assert.Equal(t, dataSize, cache.Count(ctx))

		// 清空数据
		emptyData := swiss.NewMap[string, string](0)
		cache.ReplaceAll(emptyData)

		assert.Equal(t, 0, cache.Count(ctx))

		// 重新添加数据
		cache.ReplaceAll(data)
		assert.Equal(t, dataSize, cache.Count(ctx))
	})
}
