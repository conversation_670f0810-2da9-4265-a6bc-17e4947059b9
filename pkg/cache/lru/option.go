package lru

import (
	"time"
)

type InitOption struct {
	Size    int
	Timeout time.Duration
}

type InitOptions func(options *InitOption)

func WithLruSize(size int) InitOptions {
	return func(options *InitOption) {
		if options == nil {
			options = new(InitOption)
		}
		options.Size = size
	}
}

func WithLruTimeout(timeout time.Duration) InitOptions {
	return func(options *InitOption) {
		if options == nil {
			options = new(InitOption)
		}
		options.Timeout = timeout
	}
}
