package lru

import (
	"context"
	"math/rand"
	"time"

	lru "git.garena.com/shopee/bg-logistics/go/lrucache"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/randutil"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

const (
	defaultSize   int = 10000 // 默认缓存长度
	resizeTimeout     = 300   // 变更时间默认 300s，随机在 300 秒完成 resize
)

type Cache[K comparable, V any] interface {
	Name() cache.Name
	Get(ctx context.Context, key K, opts ...cache.Options[K, V]) (V, error)
	Set(ctx context.Context, key K, val V, opts ...cache.Options[K, V]) error
	MGet(ctx context.Context, keys []K, opts ...cache.Options[K, V]) (data map[K]V, err error)
	MSet(ctx context.Context, kvMap map[K]V, opts ...cache.Options[K, V]) (int, error)
	Exist(ctx context.Context, key K, opts ...cache.Options[K, V]) bool
	Len(ctx context.Context) int
}

type Dependency struct {
	ConfAccessor config.ConfAccessor
	init         bool
}

var (
	dependency Dependency
)

func InitLruDependency(ctx context.Context, confAccessor config.ConfAccessor) error {
	dependency.ConfAccessor = confAccessor
	dependency.init = true
	return nil
}

type LruCache[K comparable, V any] struct {
	name    cache.Name    // LRU缓存实例名称
	timeout time.Duration // LRU 缓存过期时间
	Client  lru.Cache     // lruCacheClient
	size    int           // 初始化缓存长度
	enable  bool          // LruCache 是否可用
	empty   V             // 缓存元素的空值，避免每次都new一个空值
}

// NewLruCache 创建LRU内存缓存实例
// @param lruName 缓存实例名称
// @return LRU缓存实例 *LruCache
// <AUTHOR> Bo | SLS BE | <EMAIL>
func NewLruCache[K comparable, V any](lruName cache.Name, opts ...InitOptions) (Cache[K, V], error) {
	// 定义LRU缓存实例
	options := new(InitOption)
	for _, opt := range opts {
		opt(options)
	}

	lc := new(LruCache[K, V])
	lc.name = lruName
	if options.Size <= 0 {
		options.Size = defaultSize
	}
	if options.Timeout <= 0 {
		options.Timeout = cache.DefaultCacheDuration
	}
	lc.size = options.Size
	lc.timeout = options.Timeout
	// 默认为true
	lc.enable = true
	var emptyKey K
	var emptyValue V
	// 初始化lruCacheClient，采用默认值进行初始化，在增加元素时再进行重新计算size
	l, err := lru.NewLruCache(string(lruName), lc.size, lru.WithModel(emptyKey), lru.WithModel(emptyValue))
	if err != nil {
		return nil, err
	}
	lc.Client = l
	lc.empty = emptyValue

	return lc, nil
}

func (lc *LruCache[K, V]) Name() cache.Name {
	return lc.name
}

func (lc *LruCache[K, V]) Get(ctx context.Context, key K, opts ...cache.Options[K, V]) (V, error) {
	opt := cache.InitOption(opts...)
	return lc.doGet(ctx, key, opt)
}

func (lc *LruCache[K, V]) doGet(ctx context.Context, key K, option cache.Option[K, V]) (V, error) {
	if lc == nil || lc.Client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusNotEnable, "cache is null")
		return lc.empty, cache.ErrCacheNotFound
	}

	if !lc.enable {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusNotEnable, "")
		return lc.empty, cache.ErrCacheNotEnable
	}

	convert, err := lc.getKeyConverter(option)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusKeyConverterNotFound, "")
		return lc.empty, err
	}
	cacheKey := convert(ctx, key)

	val, ok := lc.Client.Get(ctx, cacheKey)
	if !ok {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusNotExist, "")
		return lc.empty, cache.ErrKeyNotFound
	}

	lruValue, ok := val.(*Value[V])
	if !ok {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusError, "")
		return lc.empty, cache.ErrUnmarshalFuncNotFound
	}
	if lruValue.expired(ctx) {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusExpired, "")
		lc.Client.Remove(ctx, cacheKey)
		return lc.empty, cache.ErrKeyExpired
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusSuccess, "")
	return lruValue.Item, nil
}

func (lc *LruCache[K, V]) MGet(ctx context.Context, keys []K, opts ...cache.Options[K, V]) (map[K]V, error) {
	if lc == nil || lc.Client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusNotEnable, "cache is null")
		return nil, cache.ErrCacheNotFound
	}
	opt := cache.InitOption(opts...)
	if _, err := lc.getKeyConverter(opt); err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusKeyConverterNotFound, "")
		return nil, err
	}
	ret := make(map[K]V, len(keys))
	for _, key := range keys {
		val, err := lc.doGet(ctx, key, opt)
		if err != nil {
			continue
		}
		ret[key] = val
	}
	return ret, nil
}

func (lc *LruCache[K, V]) Set(ctx context.Context, key K, val V, opts ...cache.Options[K, V]) error {
	if lc == nil || lc.Client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusNotEnable, "cache is null")
		return cache.ErrCacheNotFound
	}

	opt := cache.InitOption(opts...)
	convert, err := lc.getKeyConverter(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusKeyConverterNotFound, "")
		return cache.ErrKeyConvertorNotFound
	}

	timeout, size, enable := lc.getParam(ctx)
	if !enable {
		if !lc.enable {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusNotEnable, "")
			return cache.ErrCacheNotEnable
		} else {
			lc.enable = false
			lc.resize(ctx, 0)
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusNotEnable, "")
			return cache.ErrCacheNotEnable
		}
	}

	if !lc.enable {
		lc.enable = true
	}
	// 增加、减少元素时，重新设置LRU缓存长度，不在get时计算，因为查询远多过新增
	lc.resize(ctx, size)
	if opt.Timeout > 0 {
		timeout = opt.Timeout
	}

	lruValue := newLruValue(ctx, val, timeout)

	cacheKey := convert(ctx, key)
	if ok := lc.Client.Add(ctx, cacheKey, lruValue); ok {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusEviction, "")
		return nil
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusSuccess, "")
	return nil
}

func (lc *LruCache[K, V]) MSet(ctx context.Context, kvMap map[K]V, opts ...cache.Options[K, V]) (int, error) {
	if lc == nil || lc.Client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusNotEnable, "cache is null")
		return 0, cache.ErrCacheNotFound
	}

	opt := cache.InitOption(opts...)
	convert, err := lc.getKeyConverter(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusKeyConverterNotFound, "")
		return 0, cache.ErrKeyConvertorNotFound
	}

	timeout, size, enable := lc.getParam(ctx)
	if !enable {
		if !lc.enable {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusNotEnable, "")
			return 0, cache.ErrCacheNotEnable
		} else {
			lc.enable = false
			lc.resize(ctx, 0)
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusNotEnable, "")
			return 0, cache.ErrCacheNotEnable
		}
	}

	if !lc.enable {
		lc.enable = true
	}
	// 增加、减少元素时，重新设置LRU缓存长度，不在get时计算，因为查询远多过新增
	lc.resize(ctx, size)
	if opt.Timeout > 0 {
		timeout = opt.Timeout
	}

	successCount := 0
	for key, val := range kvMap {
		cacheKey := convert(ctx, key)
		lruValue := newLruValue(ctx, val, timeout)
		if !lc.Client.Add(ctx, cacheKey, lruValue) {
			successCount++
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusSuccess, "")
		} else {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCacheSet, string(lc.name), constant.StatusEviction, "")
		}
	}
	return successCount, nil
}

func (lc *LruCache[K, V]) Exist(ctx context.Context, key K, opts ...cache.Options[K, V]) bool {
	if lc == nil || lc.Client == nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusNotEnable, "cache is null")
		return false
	}

	opt := cache.InitOption(opts...)
	convert, err := lc.getKeyConverter(opt)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusKeyConverterNotFound, "cache is null")
		return false
	}
	_, ok := lc.Client.Get(ctx, convert(ctx, key))
	if !ok {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusMiss, "cache is null")
		return false
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusSuccess, "cache is null")
	return true
}

// resize 重新设定LRU缓存长度
//
//	1.小于当前长度，增加随机时间进行移除，避免缓存雪崩
//	2.大于当前长度，则刷新长度
//	3.长度为0,清空缓存
//	@param size 重设定的缓存长度
//	<AUTHOR>
func (lc *LruCache[K, V]) resize(ctx context.Context, size int) {
	if size == 0 {
		// 清零需要增加睡眠时间，避免缓存雪崩
		Logger.CtxLogInfof(ctx, "lru_cache %v resize purge, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		go func(size int) {
			seconds := randutil.RandIntN(ctx, resizeTimeout)
			time.Sleep(time.Duration(seconds) * time.Second)
			// 双重判断，保证缩容、清零多次触发时只执行最后一次
			if lc.size == size {
				lc.Client.Purge(ctx)
			}
			Logger.CtxLogInfof(ctx, "lru_cache %v resize after %d seconds finish purge", lc.name, seconds)
		}(size)
	} else if lc.size == size {

	} else if lc.size < size {
		// 扩容直接扩
		Logger.CtxLogInfof(ctx, "lru_cache %v resize scale up, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		lc.Client.Resize(ctx, size)
	} else if lc.size > size {
		// 缩容需要增加睡眠时间，避免缓存雪崩
		Logger.CtxLogInfof(ctx, "lru_cache %v resize scale down, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		go func(size int) {
			rand.Seed(time.Now().UnixNano())
			seconds := rand.Intn(resizeTimeout)
			time.Sleep(time.Duration(seconds) * time.Second)
			// 双重判断，保证缩容、清零多次触发时只执行最后一次
			if lc.size == size {
				lc.Client.Resize(ctx, size)
				Logger.CtxLogInfof(ctx, "lru_cache %v resize after %d seconds finish scale down", lc.name, seconds)
			}
		}(size)
	}
}

// getParam 获取lru参数，来源于Apollo配置
//
//	根据LRU缓存实例名，来查询Apollo对应的配置，若未配置，则取默认值返回
//	@return 过期时长，单位：秒（s） time.Duration
//	@return 缓存长度 int
//	<AUTHOR> Bo | SLS BE | <EMAIL>
func (lc *LruCache[K, V]) getParam(ctx context.Context) (timeout time.Duration, size int, enable bool) {
	name := string(lc.name)
	timeout = lc.timeout
	size = lc.size
	enable = true

	if dependency.init {
		lruCacheConfig := dependency.ConfAccessor.GetLruCacheConfig(ctx)
		param, ok := lruCacheConfig.Param[name]
		if ok {
			// 配置过期时长数值，默认单位：秒（s），转换为过期时长（time.Duration实例）
			timeout = time.Duration(param.Timeout) * time.Second
			size = param.Size
			enable = param.Enable
		}
	}
	if size <= 0 {
		size = defaultSize
	}
	return timeout, size, enable
}

func (lc *LruCache[K, V]) getKeyConverter(option cache.Option[K, V]) (cache.KeyConvertor[K], error) {
	if option.KeyConvertor == nil {
		return nil, cache.ErrKeyConvertorNotFound
	}
	return option.KeyConvertor, nil
}

func (lc *LruCache[K, V]) Len(ctx context.Context) int {
	return lc.Client.Len(ctx)
}
