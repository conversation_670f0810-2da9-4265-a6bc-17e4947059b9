package lru

import (
	"context"
	"time"

	"github.com/patrickmn/go-cache"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/timeutil"
)

// Value 元素包装值
// 与原生value的区别：增加了过期时间
// 目的：快速淘汰元素，不等lru队列填满，即进行释放，提高使用率
type Value[V any] struct {
	Item       V     // 缓存的元素
	Expiration int64 // 过期时间（时间戳）
}

// newLruValue 设置元素值与超时时长
// @param value 缓存元素的value
// @param expire 超时时长
// @return true：元素过期；false：元素未过期
// <AUTHOR>
func newLruValue[V any](ctx context.Context, value V, expire time.Duration) *Value[V] {
	lruValue := new(Value[V])
	var endTime int64
	if expire == 0 {
		expire = cache.DefaultExpiration
	}
	if expire > 0 {
		endTime = timeutil.Now(ctx).Add(expire).UnixNano()
	}
	lruValue.Item = value
	lruValue.Expiration = endTime
	return lruValue
}

// expired 过期
// @return true：元素过期；false：元素未过期
// <AUTHOR>
func (lv *Value[V]) expired(ctx context.Context) bool {
	if lv != nil && timeutil.Now(ctx).UnixNano() > lv.Expiration {
		return true
	}
	return false
}
