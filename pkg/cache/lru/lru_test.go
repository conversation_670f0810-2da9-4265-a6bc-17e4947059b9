package lru

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

func TestNewLruCache(t *testing.T) {
	tests := []struct {
		name    string
		lruName cache.Name
		opts    []InitOptions
		wantErr bool
	}{
		{
			name:    "创建基本LRU缓存",
			lruName: "test_cache",
			opts:    []InitOptions{},
			wantErr: false,
		},
		{
			name:    "创建带自定义大小的LRU缓存",
			lruName: "test_cache_size",
			opts:    []InitOptions{WithLruSize(1000)},
			wantErr: false,
		},
		{
			name:    "创建带自定义超时的LRU缓存",
			lruName: "test_cache_timeout",
			opts:    []InitOptions{WithLruTimeout(30 * time.Second)},
			wantErr: false,
		},
		{
			name:    "创建带自定义大小和超时的LRU缓存",
			lruName: "test_cache_full",
			opts: []InitOptions{
				WithLruSize(500),
				WithLruTimeout(60 * time.Second),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, err := NewLruCache[string, int](tt.lruName, tt.opts...)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, c)
			assert.Equal(t, tt.lruName, c.Name())
		})
	}
}

func TestLruCache_GetSet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 60,
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	c, err := NewLruCache[string, string]("test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	tests := []struct {
		name    string
		key     string
		value   string
		wantErr bool
	}{
		{
			name:    "设置和获取基本值",
			key:     "key1",
			value:   "value1",
			wantErr: false,
		},
		{
			name:    "设置和获取空值",
			key:     "key2",
			value:   "",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置值
			err := c.Set(ctx, tt.key, tt.value, cache.WithKeyConvertor[string, string](keyConverter))
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			// 获取值
			got, err := c.Get(ctx, tt.key, cache.WithKeyConvertor[string, string](keyConverter))
			assert.NoError(t, err)
			assert.Equal(t, tt.value, got)
		})
	}
}

func TestLruCache_MGetMSet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 60,
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	c, err := NewLruCache[string, int]("test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	t.Run("批量设置和获取", func(t *testing.T) {
		// 准备测试数据
		kvMap := map[string]int{
			"key1": 100,
			"key2": 200,
			"key3": 300,
		}

		// 批量设置
		count, err := c.MSet(ctx, kvMap, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, len(kvMap), count)

		// 批量获取
		keys := []string{"key1", "key2", "key3"}
		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, kvMap, result)
	})
}

func TestLruCache_Exist(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 60,
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	c, err := NewLruCache[string, string]("test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	t.Run("测试键存在性", func(t *testing.T) {
		// 设置一个键
		err := c.Set(ctx, "test_key", "test_value", cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		// 检查键是否存在
		exists := c.Exist(ctx, "test_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.True(t, exists)

		// 检查不存在的键
		notExists := c.Exist(ctx, "non_existent_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.False(t, notExists)
	})
}

func TestLruCache_Len(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 60,
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	c, err := NewLruCache[string, string]("test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	t.Run("测试缓存长度", func(t *testing.T) {
		// 初始长度应该为0
		initialLen := c.Len(ctx)
		assert.Equal(t, 0, initialLen)

		// 添加一些键值对
		for i := 0; i < 5; i++ {
			key := "key" + string(rune('0'+i))
			err := c.Set(ctx, key, "value", cache.WithKeyConvertor[string, string](keyConverter))
			assert.NoError(t, err)
		}

		// 长度应该为5
		finalLen := c.Len(ctx)
		assert.Equal(t, 5, finalLen)
	})
}

func TestLruCache_Expiration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 1, // 1秒过期
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	c, err := NewLruCache[string, string]("test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	t.Run("测试过期时间", func(t *testing.T) {
		// 设置一个短期过期的值
		err := c.Set(ctx, "expire_key", "expire_value", cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		// 立即获取应该成功
		value, err := c.Get(ctx, "expire_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, "expire_value", value)

		// 等待过期
		time.Sleep(2 * time.Second)

		// 过期后获取应该失败
		_, err = c.Get(ctx, "expire_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrKeyExpired, err)
	})
}

func TestLruCache_ConfigurationChanges(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	c, err := NewLruCache[string, string]("config_test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	t.Run("测试配置变更 - 禁用缓存", func(t *testing.T) {
		// 设置mock期望 - 禁用缓存
		mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
			Param: map[string]mutable_application_config.LruCacheParam{
				"config_test_cache": {
					Timeout: 60,
					Size:    1000,
					Enable:  false,
				},
			},
		}).AnyTimes()

		// 尝试设置值应该失败
		err := c.Set(ctx, "test_key", "test_value", cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)

		// 尝试获取值应该失败
		_, err = c.Get(ctx, "test_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrCacheNotEnable, err)
	})

	t.Run("测试配置变更 - 启用缓存", func(t *testing.T) {
		// 设置mock期望 - 启用缓存
		mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
			Param: map[string]mutable_application_config.LruCacheParam{
				"config_test_cache": {
					Timeout: 60,
					Size:    1000,
					Enable:  true,
				},
			},
		}).AnyTimes()

		// 设置值应该成功
		err := c.Set(ctx, "test_key", "test_value", cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		// 获取值应该成功
		value, err := c.Get(ctx, "test_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, "test_value", value)
	})
}

func TestLruCache_ErrorCases(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 60,
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	c, err := NewLruCache[string, string]("test_cache")
	require.NoError(t, err)

	t.Run("测试缺少键转换器", func(t *testing.T) {
		// 不提供键转换器应该失败
		err := c.Set(ctx, "test_key", "test_value")
		assert.Error(t, err)
		assert.Equal(t, cache.ErrKeyConvertorNotFound, err)

		_, err = c.Get(ctx, "test_key")
		assert.Error(t, err)
		assert.Equal(t, cache.ErrKeyConvertorNotFound, err)
	})

	t.Run("测试获取不存在的键", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "prefix:" + key
		}

		_, err := c.Get(ctx, "non_existent_key", cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrKeyNotFound, err)
	})
}

func TestLruCache_Resize(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	c, err := NewLruCache[string, string]("resize_test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	t.Run("测试缓存大小调整", func(t *testing.T) {
		// 设置初始配置
		mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
			Param: map[string]mutable_application_config.LruCacheParam{
				"resize_test_cache": {
					Timeout: 60,
					Size:    5, // 小容量
					Enable:  true,
				},
			},
		}).AnyTimes()

		// 添加6个键值对，应该触发LRU淘汰
		for i := 0; i < 6; i++ {
			key := "key" + string(rune('0'+i))
			err := c.Set(ctx, key, "value", cache.WithKeyConvertor[string, string](keyConverter))
			if i < 5 {
				assert.NoError(t, err)
			} else {
				// 第6个应该触发淘汰
				assert.Error(t, err)
				assert.Equal(t, cache.ErrEviction, err)
			}
		}

		// 验证只有最后5个键存在
		for i := 1; i < 6; i++ {
			key := "key" + string(rune('0'+i))
			value, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
			assert.NoError(t, err)
			assert.Equal(t, "value", value)
		}

		// 第一个键应该被淘汰
		_, err := c.Get(ctx, "key0", cache.WithKeyConvertor[string, string](keyConverter))
		assert.Error(t, err)
		assert.Equal(t, cache.ErrKeyNotFound, err)
	})
}

func TestLruCache_ComplexTypes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 60,
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	// 测试复杂类型
	type ComplexStruct struct {
		ID   int                    `json:"id"`
		Name string                 `json:"name"`
		Data map[string]interface{} `json:"data"`
	}

	c, err := NewLruCache[int, ComplexStruct]("test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key int) string {
		return "prefix:" + string(rune(key))
	}

	t.Run("测试复杂类型缓存", func(t *testing.T) {
		complexValue := ComplexStruct{
			ID:   123,
			Name: "test",
			Data: map[string]interface{}{
				"key1": "value1",
				"key2": 42,
			},
		}

		// 设置复杂类型值
		err := c.Set(ctx, 1, complexValue, cache.WithKeyConvertor[int, ComplexStruct](keyConverter))
		assert.NoError(t, err)

		// 获取复杂类型值
		got, err := c.Get(ctx, 1, cache.WithKeyConvertor[int, ComplexStruct](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, complexValue, got)
	})
}

func TestLruCache_ConcurrentAccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConfAccessor := config.NewMockConfAccessor(ctrl)
	ctx := context.Background()

	// 初始化依赖
	err := InitLruDependency(ctx, mockConfAccessor)
	require.NoError(t, err)

	// 设置mock期望
	mockConfAccessor.EXPECT().GetLruCacheConfig(gomock.Any()).Return(mutable_application_config.LruCacheConfig{
		Param: map[string]mutable_application_config.LruCacheParam{
			"test_cache": {
				Timeout: 60,
				Size:    1000,
				Enable:  true,
			},
		},
	}).AnyTimes()

	c, err := NewLruCache[string, int]("test_cache")
	require.NoError(t, err)

	keyConverter := func(ctx context.Context, key string) string {
		return "prefix:" + key
	}

	t.Run("测试并发访问", func(t *testing.T) {
		const numGoroutines = 10
		const numOperations = 100

		// 启动多个goroutine并发访问缓存
		done := make(chan bool, numGoroutines)
		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				for j := 0; j < numOperations; j++ {
					key := "key" + string(rune('0'+id)) + "_" + string(rune('0'+j))
					value := id*1000 + j

					// 设置值
					err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, int](keyConverter))
					if err != nil {
						t.Logf("Set failed: %v", err)
					}

					// 获取值
					got, err := c.Get(ctx, key, cache.WithKeyConvertor[string, int](keyConverter))
					if err == nil {
						assert.Equal(t, value, got)
					}
				}
				done <- true
			}(i)
		}

		// 等待所有goroutine完成
		for i := 0; i < numGoroutines; i++ {
			<-done
		}
	})
}
