package multi_layer_cache

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type MultiLayerCache[K comparable, V any] interface {
	Name() cache.Name
	Get(ctx context.Context, key K, opts ...cache.Options[K, V]) (V, error)
	Set(ctx context.Context, key K, val V, opts ...cache.Options[K, V]) error
	MGet(ctx context.Context, keys []K, opts ...cache.Options[K, V]) (map[K]V, error)
	MSet(ctx context.Context, kvMap map[K]V, opts ...cache.Options[K, V]) (int, error)
}

type multiLayerCache[K comparable, V any] struct {
	name   cache.Name
	local  cache.MultiCache[K, V]
	remote cache.MultiCache[K, V]
}

func NewMultiLayerCache[K comparable, V any](
	name cache.Name,
	local cache.MultiCache[K, V],
	remote cache.MultiCache[K, V],
) MultiLayerCache[K, V] {
	return &multiLayerCache[K, V]{
		name:   name,
		local:  local,
		remote: remote,
	}
}

func (m *multiLayerCache[K, V]) Name() cache.Name {
	return m.name
}

func (m *multiLayerCache[K, V]) Get(ctx context.Context, key K, opts ...cache.Options[K, V]) (V, error) {
	opt := cache.InitOption(opts...)
	ret, err := m.getLocal(ctx, key, opt)
	if err == nil {
		return ret, nil
	}
	ret, err = m.getRemote(ctx, key, opt)
	if err == nil {
		_ = m.setLocal(ctx, key, ret, opt)
	}
	return ret, err
}

func (m *multiLayerCache[K, V]) Set(ctx context.Context, key K, val V, opts ...cache.Options[K, V]) error {
	opt := cache.InitOption(opts...)
	err := m.setLocal(ctx, key, val, opt)
	if rErr := m.setRemote(ctx, key, val, opt); rErr != nil {
		return rErr
	}
	return err
}

func (m *multiLayerCache[K, V]) MGet(ctx context.Context, keys []K, opts ...cache.Options[K, V]) (map[K]V, error) {
	if len(keys) == 0 {
		return make(map[K]V), nil
	}

	opt := cache.InitOption(opts...)
	ret, err := m.mGetLocal(ctx, keys, opt)
	if len(ret) == len(keys) {
		return ret, nil
	}

	if ret == nil {
		ret = make(map[K]V)
	}
	var missingKeys []K
	if err != nil {
		missingKeys = keys
	} else {
		missingKeys = make([]K, 0, len(keys)-len(ret))
		for index := range keys {
			if _, ok := ret[keys[index]]; !ok {
				missingKeys = append(missingKeys, keys[index])
			}
		}
	}

	if len(missingKeys) > 0 {
		remoteRet, _ := m.mGetRemote(ctx, missingKeys, opt)
		for key := range remoteRet {
			ret[key] = remoteRet[key]
		}
		if len(remoteRet) > 0 {
			_, _ = m.mSetLocal(ctx, remoteRet, opt)
		}
	}

	return ret, nil
}

func (m *multiLayerCache[K, V]) MSet(ctx context.Context, kvMap map[K]V, opts ...cache.Options[K, V]) (int, error) {
	opt := cache.InitOption(opts...)
	successCount, _ := m.mSetLocal(ctx, kvMap, opt)
	_, _ = m.mSetRemote(ctx, kvMap, opt)
	return successCount, nil
}

func (m *multiLayerCache[K, V]) getLocal(ctx context.Context, key K, option cache.Option[K, V]) (V, error) {
	if m.local == nil {
		return typ.ZeroValue[V](), cache.ErrCacheNotEnable
	}
	return m.local.Get(ctx, key, cache.WithOptions(option))
}

func (m *multiLayerCache[K, V]) getRemote(ctx context.Context, key K, option cache.Option[K, V]) (V, error) {
	if m.remote == nil {
		return typ.ZeroValue[V](), cache.ErrCacheNotEnable
	}
	return m.remote.Get(ctx, key, cache.WithOptions(option))
}

func (m *multiLayerCache[K, V]) setLocal(ctx context.Context, key K, val V, option cache.Option[K, V]) error {
	if m.local == nil {
		return cache.ErrCacheNotEnable
	}
	return m.local.Set(ctx, key, val, cache.WithOptions(option))
}

func (m *multiLayerCache[K, V]) setRemote(ctx context.Context, key K, val V, option cache.Option[K, V]) error {
	if m.remote == nil {
		return cache.ErrCacheNotEnable
	}
	return m.remote.Set(ctx, key, val, cache.WithOptions(option))
}

func (m *multiLayerCache[K, V]) mGetLocal(ctx context.Context, keys []K, option cache.Option[K, V]) (map[K]V, error) {
	if m.local == nil {
		return make(map[K]V), cache.ErrCacheNotEnable
	}
	return m.local.MGet(ctx, keys, cache.WithOptions(option))
}

func (m *multiLayerCache[K, V]) mGetRemote(ctx context.Context, keys []K, option cache.Option[K, V]) (map[K]V, error) {
	if m.remote == nil {
		return make(map[K]V), cache.ErrCacheNotEnable
	}
	return m.remote.MGet(ctx, keys, cache.WithOptions(option))
}

func (m *multiLayerCache[K, V]) mSetLocal(ctx context.Context, kvMap map[K]V, option cache.Option[K, V]) (int, error) {
	if m.local == nil {
		return 0, cache.ErrCacheNotEnable
	}
	return m.local.MSet(ctx, kvMap, cache.WithOptions(option))
}

func (m *multiLayerCache[K, V]) mSetRemote(ctx context.Context, kvMap map[K]V, option cache.Option[K, V]) (int, error) {
	if m.remote == nil {
		return 0, cache.ErrCacheNotEnable
	}
	return m.remote.MSet(ctx, kvMap, cache.WithOptions(option))
}
