package mixed_cache

import (
	"context"
	"fmt"
	"testing"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/remote_cache"
)

// 创建 mock Redis 客户端用于 benchmark
func createBenchmarkRedisClient() *redis.Client {
	logger.UpdateLogLevel(logger.LogLevelInfo)
	return redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
}

// 准备 benchmark 数据
func prepareBenchmarkData(size int) map[string]string {
	data := make(map[string]string, size)
	for i := 0; i < size; i++ {
		key := fmt.Sprintf("bench_key_%d", i)
		value := fmt.Sprintf("bench_value_%d", i)
		data[key] = value
	}
	return data
}

// 准备 benchmark 键列表
func prepareBenchmarkKeys(size int) []string {
	keys := make([]string, size)
	for i := 0; i < size; i++ {
		keys[i] = fmt.Sprintf("bench_key_%d", i)
	}
	return keys
}

// BenchmarkLruLayerCache_Set 测试单个 Set 操作的性能
func BenchmarkLruLayerCache_Set(b *testing.B) {
	client := createBenchmarkRedisClient()
	c, err := NewLruLayerCache[string, string](
		"bench_set",
		client,
		[]lru.InitOptions{lru.WithLruSize(10000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	if err != nil {
		b.Fatalf("Failed to create LruLayerCache: %v", err)
	}

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := fmt.Sprintf("parallel_key_%d", i)
			value := fmt.Sprintf("parallel_value_%d", i)
			err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
			if err != nil {
				b.Errorf("Set failed: %v", err)
			}
			i++
		}
	})
}

// BenchmarkLruLayerCache_Get 测试单个 Get 操作的性能
func BenchmarkLruLayerCache_Get(b *testing.B) {
	client := createBenchmarkRedisClient()
	c, err := NewLruLayerCache[string, string](
		"bench_get",
		client,
		[]lru.InitOptions{lru.WithLruSize(10000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	if err != nil {
		b.Fatalf("Failed to create LruLayerCache: %v", err)
	}

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	data := prepareBenchmarkData(1000)
	for key, value := range data {
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatalf("Failed to set data: %v", err)
		}
	}

	keys := prepareBenchmarkKeys(1000)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := keys[i%len(keys)]
			_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
			if err != nil {
				// 忽略缓存未命中的错误
				_ = err
			}
			i++
		}
	})
}

// BenchmarkLruLayerCache_MSet 测试批量 Set 操作的性能
func BenchmarkLruLayerCache_MSet(b *testing.B) {
	client := createBenchmarkRedisClient()
	c, err := NewLruLayerCache[string, string](
		"bench_mset",
		client,
		[]lru.InitOptions{lru.WithLruSize(10000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	if err != nil {
		b.Fatalf("Failed to create LruLayerCache: %v", err)
	}

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 准备不同大小的数据集
	sizes := []int{10, 100, 1000}
	for _, size := range sizes {
		b.Run(fmt.Sprintf("Size_%d", size), func(b *testing.B) {
			data := prepareBenchmarkData(size)
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				// 为每次迭代创建新的数据，避免缓存命中
				iterData := make(map[string]string, size)
				for key, value := range data {
					iterData[fmt.Sprintf("%s_%d", key, i)] = value
				}
				_, err := c.MSet(ctx, iterData, cache.WithKeyConvertor[string, string](keyConverter))
				if err != nil {
					b.Errorf("MSet failed: %v", err)
				}
			}
		})
	}
}

// BenchmarkLruLayerCache_MGet 测试批量 Get 操作的性能
func BenchmarkLruLayerCache_MGet(b *testing.B) {
	client := createBenchmarkRedisClient()
	c, err := NewLruLayerCache[string, string](
		"bench_mget",
		client,
		[]lru.InitOptions{lru.WithLruSize(10000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	if err != nil {
		b.Fatalf("Failed to create LruLayerCache: %v", err)
	}

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置数据
	data := prepareBenchmarkData(1000)
	for key, value := range data {
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatalf("Failed to set data: %v", err)
		}
	}

	// 测试不同大小的批量获取
	sizes := []int{10, 100, 1000}
	for _, size := range sizes {
		b.Run(fmt.Sprintf("Size_%d", size), func(b *testing.B) {
			keys := prepareBenchmarkKeys(size)
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, string](keyConverter))
				if err != nil {
					b.Errorf("MGet failed: %v", err)
				}
			}
		})
	}
}

// BenchmarkLruLayerCache_MixedOperations 测试混合操作的性能
func BenchmarkLruLayerCache_MixedOperations(b *testing.B) {
	client := createBenchmarkRedisClient()
	c, err := NewLruLayerCache[string, string](
		"bench_mixed",
		client,
		[]lru.InitOptions{lru.WithLruSize(10000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	if err != nil {
		b.Fatalf("Failed to create LruLayerCache: %v", err)
	}

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置一些数据
	data := prepareBenchmarkData(1000)
	for key, value := range data {
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatalf("Failed to set data: %v", err)
		}
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			// 混合操作：70% 读取，30% 写入
			if i%10 < 7 {
				// 读取操作
				key := fmt.Sprintf("bench_key_%d", i%1000)
				_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
				if err != nil {
					// 忽略缓存未命中的错误
					_ = err
				}
			} else {
				// 写入操作
				key := fmt.Sprintf("mixed_key_%d", i)
				value := fmt.Sprintf("mixed_value_%d", i)
				err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
				if err != nil {
					b.Errorf("Set failed: %v", err)
				}
			}
			i++
		}
	})
}

// BenchmarkLruLayerCache_ConcurrentAccess 测试并发访问性能
func BenchmarkLruLayerCache_ConcurrentAccess(b *testing.B) {
	client := createBenchmarkRedisClient()
	c, err := NewLruLayerCache[string, string](
		"bench_concurrent",
		client,
		[]lru.InitOptions{lru.WithLruSize(10000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	if err != nil {
		b.Fatalf("Failed to create LruLayerCache: %v", err)
	}

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 预先设置数据
	data := prepareBenchmarkData(1000)
	for key, value := range data {
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatalf("Failed to set data: %v", err)
		}
	}

	// 测试不同并发级别
	concurrencyLevels := []int{1, 4, 8, 16}
	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(b *testing.B) {
			b.SetParallelism(concurrency)
			b.ResetTimer()
			b.RunParallel(func(pb *testing.PB) {
				i := 0
				for pb.Next() {
					key := fmt.Sprintf("concurrent_key_%d", i%1000)
					value := fmt.Sprintf("concurrent_value_%d", i)

					// 交替进行读写操作
					if i%2 == 0 {
						err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
						if err != nil {
							b.Errorf("Set failed: %v", err)
						}
					} else {
						_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
						if err != nil {
							// 忽略缓存未命中的错误
							_ = err
						}
					}
					i++
				}
			})
		})
	}
}

// BenchmarkLruLayerCache_DifferentDataTypes 测试不同数据类型的性能
func BenchmarkLruLayerCache_DifferentDataTypes(b *testing.B) {
	client := createBenchmarkRedisClient()

	// 测试字符串类型
	b.Run("String_Type", func(b *testing.B) {
		c, err := NewLruLayerCache[string, string](
			"bench_string",
			client,
			[]lru.InitOptions{lru.WithLruSize(10000)},
			[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
		)
		if err != nil {
			b.Fatalf("Failed to create LruLayerCache: %v", err)
		}

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "bench:" + key
		}

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				key := fmt.Sprintf("string_key_%d", i)
				value := fmt.Sprintf("string_value_%d", i)
				err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
				if err != nil {
					b.Errorf("Set failed: %v", err)
				}
				i++
			}
		})
	})

	// 测试整数类型
	b.Run("Int_Type", func(b *testing.B) {
		c, err := NewLruLayerCache[string, int](
			"bench_int",
			client,
			[]lru.InitOptions{lru.WithLruSize(10000)},
			[]remote_cache.InitOptions[int]{remote_cache.WithTimeout[int](5 * time.Second)},
		)
		if err != nil {
			b.Fatalf("Failed to create LruLayerCache: %v", err)
		}

		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "bench:" + key
		}

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				key := fmt.Sprintf("int_key_%d", i)
				value := i
				err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, int](keyConverter))
				if err != nil {
					b.Errorf("Set failed: %v", err)
				}
				i++
			}
		})
	})
}

// BenchmarkLruLayerCache_CacheHitVsMiss 测试缓存命中与未命中的性能差异
func BenchmarkLruLayerCache_CacheHitVsMiss(b *testing.B) {
	client := createBenchmarkRedisClient()
	c, err := NewLruLayerCache[string, string](
		"bench_hit_miss",
		client,
		[]lru.InitOptions{lru.WithLruSize(1000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	if err != nil {
		b.Fatalf("Failed to create LruLayerCache: %v", err)
	}

	ctx := context.Background()
	keyConverter := func(ctx context.Context, key string) string {
		return "bench:" + key
	}

	// 测试缓存命中（重复访问相同的键）
	b.Run("Cache_Hit", func(b *testing.B) {
		// 预先设置一个键
		key := "hit_key"
		value := "hit_value"
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, string](keyConverter))
		if err != nil {
			b.Fatalf("Failed to set data: %v", err)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
			if err != nil {
				b.Errorf("Get failed: %v", err)
			}
		}
	})

	// 测试缓存未命中（每次都访问新的键）
	b.Run("Cache_Miss", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("miss_key_%d", i)
			_, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
			if err != nil {
				// 忽略缓存未命中的错误
				_ = err
			}
		}
	})
}
