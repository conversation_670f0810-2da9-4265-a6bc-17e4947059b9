package mixed_cache

import (
	"context"
	"fmt"
	"testing"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/remote_cache"
)

// TestData 用于测试的结构体
type TestData struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Age  int    `json:"age"`
}

// TestProtoMessage 用于测试的 protobuf 消息
type TestProtoMessage struct {
	ID       int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name     string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email    string            `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Age      int32             `protobuf:"varint,4,opt,name=age,proto3" json:"age,omitempty"`
	Active   bool              `protobuf:"varint,5,opt,name=active,proto3" json:"active,omitempty"`
	Tags     []string          `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
	Metadata map[string]string `protobuf:"bytes,7,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (m *TestProtoMessage) Reset()         { *m = TestProtoMessage{} }
func (m *TestProtoMessage) String() string { return proto.CompactTextString(m) }
func (m *TestProtoMessage) ProtoMessage()  {}

// createMockRedisClient 创建一个简单的 mock Redis 客户端
func createMockRedisClient() *redis.Client {
	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	return client
}

// TestLruLayerCache_Creation 测试 LruLayerCache 的创建功能
func TestLruLayerCache_Creation(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	t.Run("创建通用 LruLayerCache", func(t *testing.T) {
		c, err := NewLruLayerCache[string, TestData](
			"test_generic_cache",
			client,
			[]lru.InitOptions{lru.WithLruSize(1000)},
			[]remote_cache.InitOptions[TestData]{remote_cache.WithTimeout[TestData](5 * time.Second)},
		)
		assert.NoError(t, err)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_generic_cache"), c.Name())
	})

	t.Run("创建数字类型 LruLayerCache", func(t *testing.T) {
		c, err := NewNumberLruLayerCache[string, int](
			"test_number_cache",
			client,
			[]lru.InitOptions{lru.WithLruSize(500)},
			[]remote_cache.InitOptions[int]{remote_cache.WithTimeout[int](10 * time.Second)},
		)
		assert.NoError(t, err)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_number_cache"), c.Name())
	})

	t.Run("创建布尔类型 LruLayerCache", func(t *testing.T) {
		c, err := NewBoolLruLayerCache[string, bool](
			"test_bool_cache",
			client,
			[]lru.InitOptions{lru.WithLruSize(200)},
			[]remote_cache.InitOptions[bool]{remote_cache.WithTimeout[bool](3 * time.Second)},
		)
		assert.NoError(t, err)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_bool_cache"), c.Name())
	})

	t.Run("创建字符串类型 LruLayerCache", func(t *testing.T) {
		c, err := NewStrLruLayerCache[string, string](
			"test_string_cache",
			client,
			[]lru.InitOptions{lru.WithLruSize(800)},
			[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](7 * time.Second)},
		)
		assert.NoError(t, err)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_string_cache"), c.Name())
	})

	t.Run("创建 Protobuf 类型 LruLayerCache", func(t *testing.T) {
		c, err := NewPbLruLayerCache[string, *TestProtoMessage](
			"test_proto_cache",
			client,
			[]lru.InitOptions{lru.WithLruSize(300)},
			[]remote_cache.InitOptions[*TestProtoMessage]{remote_cache.WithTimeout[*TestProtoMessage](15 * time.Second)},
		)
		assert.NoError(t, err)
		assert.NotNil(t, c)
		assert.Equal(t, cache.Name("test_proto_cache"), c.Name())
	})
}

// TestLruLayerCache_Interface 测试 LruLayerCache 接口实现
func TestLruLayerCache_Interface(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	c, err := NewLruLayerCache[string, string](
		"test_interface",
		client,
		[]lru.InitOptions{lru.WithLruSize(100)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	require.NoError(t, err)

	// 测试接口实现
	var _ LruLayerCache[string, string] = c
	var _ cache.MultiCache[string, string] = c

	t.Run("测试接口方法", func(t *testing.T) {
		// 测试 Name 方法
		name := c.Name()
		assert.Equal(t, cache.Name("test_interface"), name)

		// 测试 Get 方法（即使可能失败，也应该有正确的签名）
		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}
		_, _ = c.Get(ctx, "test_key", cache.WithKeyConvertor[string, string](keyConverter))
		// 由于 Redis 连接可能失败，我们不检查具体错误
		// 只验证方法可以调用
	})

	t.Run("测试批量操作接口", func(t *testing.T) {
		ctx := context.Background()
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		// 测试 MGet 方法
		_, _ = c.MGet(ctx, []string{"key1", "key2"}, cache.WithKeyConvertor[string, string](keyConverter))
		// 由于 Redis 连接可能失败，我们不检查具体错误
		// 只验证方法可以调用

		// 测试 MSet 方法
		kvMap := map[string]string{
			"key1": "value1",
			"key2": "value2",
		}
		_, _ = c.MSet(ctx, kvMap, cache.WithKeyConvertor[string, string](keyConverter))
		// 由于 Redis 连接可能失败，我们不检查具体错误
		// 只验证方法可以调用
	})
}

// TestLruLayerCache_ErrorHandling 测试错误处理
func TestLruLayerCache_ErrorHandling(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	c, err := NewLruLayerCache[string, int](
		"test_error_handling",
		client,
		[]lru.InitOptions{lru.WithLruSize(100)},
		[]remote_cache.InitOptions[int]{remote_cache.WithTimeout[int](1 * time.Second)},
	)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("测试空键列表", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}
		result, err := c.MGet(ctx, []string{}, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("测试空映射", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}
		successCount, err := c.MSet(ctx, map[string]int{}, cache.WithKeyConvertor[string, int](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 0, successCount)
	})

	t.Run("测试获取不存在的键", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}
		_, err := c.Get(ctx, "non_existent_key", cache.WithKeyConvertor[string, int](keyConverter))
		assert.Error(t, err)
	})
}

// TestLruLayerCache_ContextHandling 测试上下文处理
func TestLruLayerCache_ContextHandling(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	c, err := NewLruLayerCache[string, int](
		"test_context",
		client,
		[]lru.InitOptions{lru.WithLruSize(100)},
		[]remote_cache.InitOptions[int]{remote_cache.WithTimeout[int](1 * time.Second)},
	)
	require.NoError(t, err)

	t.Run("测试带超时的上下文", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
		defer cancel()

		key := "timeout_key"
		value := 42
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, int](keyConverter))
		// 由于超时时间很短，可能会失败，这是正常的
		if err != nil {
			t.Logf("Set with timeout context failed: %v", err)
		}
	})

	t.Run("测试已取消的上下文", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // 立即取消

		key := "cancelled_key"
		value := 42
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, int](keyConverter))
		// 由于上下文已取消，可能会失败，这是正常的
		if err != nil {
			t.Logf("Set with cancelled context failed: %v", err)
		}
	})
}

// TestLruLayerCache_EdgeCases 测试边界情况
func TestLruLayerCache_EdgeCases(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	c, err := NewLruLayerCache[string, string](
		"test_edge_cases",
		client,
		[]lru.InitOptions{lru.WithLruSize(100)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](5 * time.Second)},
	)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("测试空键", func(t *testing.T) {
		emptyKey := ""
		value := "empty_key_value"
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		err := c.Set(ctx, emptyKey, value, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, emptyKey, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})

	t.Run("测试空值", func(t *testing.T) {
		key := "empty_value_key"
		emptyValue := ""
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		err := c.Set(ctx, key, emptyValue, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, emptyValue, result)
	})

	t.Run("测试特殊字符键", func(t *testing.T) {
		specialKey := "key_with_special_chars:!@#$%^&*()_+-=[]{}|;':\",./<>?"
		value := "special_value"
		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		err := c.Set(ctx, specialKey, value, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, specialKey, cache.WithKeyConvertor[string, string](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)
	})
}

// TestLruLayerCache_ConcurrentAccess 测试并发访问
func TestLruLayerCache_ConcurrentAccess(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	c, err := NewLruLayerCache[string, int](
		"test_concurrent",
		client,
		[]lru.InitOptions{lru.WithLruSize(1000)},
		[]remote_cache.InitOptions[int]{remote_cache.WithTimeout[int](5 * time.Second)},
	)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("并发读写操作", func(t *testing.T) {
		const numGoroutines = 5
		const numOperations = 10

		// 启动多个 goroutine 并发访问缓存
		done := make(chan bool, numGoroutines)
		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				keyConverter := func(ctx context.Context, key string) string {
					return "test:" + key
				}
				for j := 0; j < numOperations; j++ {
					key := "key" + string(rune('0'+id)) + "_" + string(rune('0'+j))
					value := id*1000 + j

					// 设置值
					err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, int](keyConverter))
					if err != nil {
						t.Logf("Set failed: %v", err)
					}

					// 获取值
					_, err = c.Get(ctx, key, cache.WithKeyConvertor[string, int](keyConverter))
					if err != nil {
						t.Logf("Get failed: %v", err)
					}
				}
				done <- true
			}(i)
		}

		// 等待所有 goroutine 完成
		for i := 0; i < numGoroutines; i++ {
			<-done
		}
	})

	t.Run("并发批量操作", func(t *testing.T) {
		const numGoroutines = 3

		done := make(chan bool, numGoroutines)
		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				keyConverter := func(ctx context.Context, key string) string {
					return "test:" + key
				}
				// 批量设置
				kvMap := map[string]int{
					"batch_key1": id*100 + 1,
					"batch_key2": id*100 + 2,
					"batch_key3": id*100 + 3,
				}

				_, err := c.MSet(ctx, kvMap, cache.WithKeyConvertor[string, int](keyConverter))
				if err != nil {
					t.Logf("MSet failed: %v", err)
				}

				// 批量获取
				keys := []string{"batch_key1", "batch_key2", "batch_key3"}
				_, err = c.MGet(ctx, keys, cache.WithKeyConvertor[string, int](keyConverter))
				if err != nil {
					t.Logf("MGet failed: %v", err)
				}

				done <- true
			}(i)
		}

		// 等待所有 goroutine 完成
		for i := 0; i < numGoroutines; i++ {
			<-done
		}
	})
}

// TestLruLayerCache_Performance 测试性能
func TestLruLayerCache_Performance(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	c, err := NewLruLayerCache[string, string](
		"test_performance",
		client,
		[]lru.InitOptions{lru.WithLruSize(10000)},
		[]remote_cache.InitOptions[string]{remote_cache.WithTimeout[string](10 * time.Second)},
	)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("大量数据操作性能", func(t *testing.T) {
		const numItems = 100

		// 准备大量数据
		kvMap := make(map[string]string, numItems)
		for i := 0; i < numItems; i++ {
			key := fmt.Sprintf("perf_key_%d", i)
			value := fmt.Sprintf("value_%d", i)
			kvMap[key] = value
		}

		keyConverter := func(ctx context.Context, key string) string {
			return "test:" + key
		}

		// 批量设置
		start := time.Now()
		successCount, err := c.MSet(ctx, kvMap, cache.WithKeyConvertor[string, string](keyConverter))
		setDuration := time.Since(start)

		assert.NoError(t, err)
		assert.Equal(t, numItems, successCount)
		t.Logf("批量设置 %d 个键值对耗时: %v", numItems, setDuration)

		// 批量获取
		keys := make([]string, 0, numItems)
		for key := range kvMap {
			keys = append(keys, key)
		}

		start = time.Now()
		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, string](keyConverter))
		getDuration := time.Since(start)

		assert.NoError(t, err)
		assert.Len(t, result, numItems)
		t.Logf("批量获取 %d 个键值对耗时: %v", numItems, getDuration)

		// 验证数据完整性
		for key, expectedValue := range kvMap {
			actualValue, exists := result[key]
			assert.True(t, exists, "Key %s should exist", key)
			assert.Equal(t, expectedValue, actualValue)
		}
	})
}

// TestLruLayerCache_JsonValue 测试 JSON 值的缓存操作
func TestLruLayerCache_JsonValue(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	// 定义复杂的 JSON 结构体
	type ComplexJsonData struct {
		ID       int                    `json:"id"`
		Name     string                 `json:"name"`
		Age      int                    `json:"age"`
		Email    string                 `json:"email"`
		Active   bool                   `json:"active"`
		Tags     []string               `json:"tags"`
		Metadata map[string]interface{} `json:"metadata"`
		Address  struct {
			Street  string `json:"street"`
			City    string `json:"city"`
			Country string `json:"country"`
		} `json:"address"`
	}

	c, err := NewLruLayerCache[string, ComplexJsonData](
		"test_json_cache",
		client,
		[]lru.InitOptions{lru.WithLruSize(1000)},
		[]remote_cache.InitOptions[ComplexJsonData]{remote_cache.WithTimeout[ComplexJsonData](10 * time.Second)},
	)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("JSON 复杂结构体 Set 和 Get", func(t *testing.T) {
		key := "complex_json_key"
		value := ComplexJsonData{
			ID:     12345,
			Name:   "张三",
			Age:    30,
			Email:  "<EMAIL>",
			Active: true,
			Tags:   []string{"developer", "golang", "backend"},
			Metadata: map[string]interface{}{
				"department": "engineering",
				"level":      5,
				"skills":     []string{"Go", "Redis", "Microservices"},
			},
		}
		value.Address.Street = "科技路123号"
		value.Address.City = "深圳"
		value.Address.Country = "中国"

		keyConverter := func(ctx context.Context, key string) string {
			return "test:json:" + key
		}

		// 设置值
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)

		// 获取值
		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, value, result)

		// 验证嵌套结构
		assert.Equal(t, value.Address.Street, result.Address.Street)
		assert.Equal(t, value.Address.City, result.Address.City)
		assert.Equal(t, value.Metadata["department"], result.Metadata["department"])
	})

	t.Run("JSON 批量操作", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:json:" + key
		}

		// 准备多个复杂 JSON 数据
		jsonDataMap := map[string]ComplexJsonData{
			"user1": {
				ID:     1,
				Name:   "用户1",
				Age:    25,
				Email:  "<EMAIL>",
				Active: true,
				Tags:   []string{"user", "active"},
				Metadata: map[string]interface{}{
					"role": "admin",
				},
			},
			"user2": {
				ID:     2,
				Name:   "用户2",
				Age:    28,
				Email:  "<EMAIL>",
				Active: false,
				Tags:   []string{"user", "inactive"},
				Metadata: map[string]interface{}{
					"role": "user",
				},
			},
		}

		// 批量设置
		successCount, err := c.MSet(ctx, jsonDataMap, cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 2, successCount)

		// 批量获取
		keys := []string{"user1", "user2"}
		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)
		assert.Len(t, result, 2)

		// 验证数据完整性
		for key, expectedValue := range jsonDataMap {
			actualValue, exists := result[key]
			assert.True(t, exists, "Key %s should exist", key)
			assert.Equal(t, expectedValue, actualValue)
		}
	})

	t.Run("JSON 空值和零值处理", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:json:" + key
		}

		// 测试空结构体
		emptyData := ComplexJsonData{}
		err := c.Set(ctx, "empty_json", emptyData, cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, "empty_json", cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, emptyData, result)

		// 测试部分字段为空的结构体
		partialData := ComplexJsonData{
			ID:   999,
			Name: "部分数据",
			// 其他字段保持零值
		}
		err = c.Set(ctx, "partial_json", partialData, cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)

		result, err = c.Get(ctx, "partial_json", cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, partialData, result)
	})

	t.Run("JSON 特殊字符和 Unicode 处理", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:json:" + key
		}

		specialData := ComplexJsonData{
			ID:     999,
			Name:   "特殊字符测试 🚀 中文测试",
			Email:  "<EMAIL>",
			Active: true,
			Tags:   []string{"特殊", "emoji 🎉", "unicode"},
			Metadata: map[string]interface{}{
				"special_chars": "!@#$%^&*()_+-=[]{}|;':\",./<>?",
				"unicode":       "中文测试 🚀 🎉 💻",
			},
		}

		err := c.Set(ctx, "special_chars", specialData, cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, "special_chars", cache.WithKeyConvertor[string, ComplexJsonData](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, specialData, result)
	})
}

// 定义更复杂的 Protobuf 消息类型
type ComplexProtoMessage struct {
	ID       int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name     string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email    string            `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Age      int32             `protobuf:"varint,4,opt,name=age,proto3" json:"age,omitempty"`
	Active   bool              `protobuf:"varint,5,opt,name=active,proto3" json:"active,omitempty"`
	Tags     []string          `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
	Metadata map[string]string `protobuf:"bytes,7,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Address  *Address          `protobuf:"bytes,8,opt,name=address,proto3" json:"address,omitempty"`
}

type Address struct {
	Street  string `protobuf:"bytes,1,opt,name=street,proto3" json:"street,omitempty"`
	City    string `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	Country string `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`
}

// 实现 Protobuf 接口方法
func (m *ComplexProtoMessage) Reset()         { *m = ComplexProtoMessage{} }
func (m *ComplexProtoMessage) String() string { return proto.CompactTextString(m) }
func (m *ComplexProtoMessage) ProtoMessage()  {}

func (m *Address) Reset()         { *m = Address{} }
func (m *Address) String() string { return proto.CompactTextString(m) }
func (m *Address) ProtoMessage()  {}

// TestLruLayerCache_ProtobufValue 测试 Protobuf 值的缓存操作
func TestLruLayerCache_ProtobufValue(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	c, err := NewPbLruLayerCache[string, *ComplexProtoMessage](
		"test_proto_cache",
		client,
		[]lru.InitOptions{lru.WithLruSize(1000)},
		[]remote_cache.InitOptions[*ComplexProtoMessage]{remote_cache.WithTimeout[*ComplexProtoMessage](10 * time.Second)},
	)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("Protobuf 复杂消息 Set 和 Get", func(t *testing.T) {
		key := "complex_proto_key"
		value := &ComplexProtoMessage{
			ID:     12345,
			Name:   "李四",
			Email:  "<EMAIL>",
			Age:    35,
			Active: true,
			Tags:   []string{"manager", "protobuf", "backend"},
			Metadata: map[string]string{
				"department": "engineering",
				"level":      "senior",
				"location":   "shenzhen",
			},
			Address: &Address{
				Street:  "创新大道456号",
				City:    "深圳",
				Country: "中国",
			},
		}

		keyConverter := func(ctx context.Context, key string) string {
			return "test:proto:" + key
		}

		// 设置值
		err := c.Set(ctx, key, value, cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)

		// 获取值
		result, err := c.Get(ctx, key, cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.NotNil(t, result)

		// 验证字段
		assert.Equal(t, value.ID, result.ID)
		assert.Equal(t, value.Name, result.Name)
		assert.Equal(t, value.Email, result.Email)
		assert.Equal(t, value.Age, result.Age)
		assert.Equal(t, value.Active, result.Active)
		assert.Equal(t, value.Tags, result.Tags)
		assert.Equal(t, value.Metadata, result.Metadata)

		// 验证嵌套结构
		assert.NotNil(t, result.Address)
		assert.Equal(t, value.Address.Street, result.Address.Street)
		assert.Equal(t, value.Address.City, result.Address.City)
		assert.Equal(t, value.Address.Country, result.Address.Country)
	})

	t.Run("Protobuf 批量操作", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:proto:" + key
		}

		// 准备多个 Protobuf 消息
		protoDataMap := map[string]*ComplexProtoMessage{
			"proto1": {
				ID:     1,
				Name:   "Proto用户1",
				Email:  "<EMAIL>",
				Age:    25,
				Active: true,
				Tags:   []string{"proto", "user"},
				Metadata: map[string]string{
					"type": "test",
				},
				Address: &Address{
					Street:  "测试街道1号",
					City:    "北京",
					Country: "中国",
				},
			},
			"proto2": {
				ID:     2,
				Name:   "Proto用户2",
				Email:  "<EMAIL>",
				Age:    30,
				Active: false,
				Tags:   []string{"proto", "inactive"},
				Metadata: map[string]string{
					"type": "production",
				},
				Address: &Address{
					Street:  "生产街道2号",
					City:    "上海",
					Country: "中国",
				},
			},
		}

		// 批量设置
		successCount, err := c.MSet(ctx, protoDataMap, cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.Equal(t, 2, successCount)

		// 批量获取
		keys := []string{"proto1", "proto2"}
		result, err := c.MGet(ctx, keys, cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.Len(t, result, 2)

		// 验证数据完整性
		for key, expectedValue := range protoDataMap {
			actualValue, exists := result[key]
			assert.True(t, exists, "Key %s should exist", key)
			assert.NotNil(t, actualValue)
			assert.Equal(t, expectedValue.ID, actualValue.ID)
			assert.Equal(t, expectedValue.Name, actualValue.Name)
			assert.Equal(t, expectedValue.Email, actualValue.Email)
			assert.Equal(t, expectedValue.Age, actualValue.Age)
			assert.Equal(t, expectedValue.Active, actualValue.Active)
			assert.Equal(t, expectedValue.Tags, actualValue.Tags)
			assert.Equal(t, expectedValue.Metadata, actualValue.Metadata)
		}
	})

	t.Run("Protobuf 空值和零值处理", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:proto:" + key
		}

		// 测试空消息
		emptyData := &ComplexProtoMessage{}
		err := c.Set(ctx, "empty_proto", emptyData, cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, "empty_proto", cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, emptyData.ID, result.ID)
		assert.Equal(t, emptyData.Name, result.Name)

		// 测试部分字段为零值的消息
		partialData := &ComplexProtoMessage{
			ID:   999,
			Name: "部分Protobuf数据",
			// 其他字段保持零值
		}
		err = c.Set(ctx, "partial_proto", partialData, cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)

		result, err = c.Get(ctx, "partial_proto", cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, partialData.ID, result.ID)
		assert.Equal(t, partialData.Name, result.Name)
	})

	t.Run("Protobuf 特殊字符和 Unicode 处理", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:proto:" + key
		}

		specialData := &ComplexProtoMessage{
			ID:     888,
			Name:   "特殊字符Protobuf测试 🚀 中文测试",
			Email:  "<EMAIL>",
			Active: true,
			Tags:   []string{"特殊", "protobuf", "emoji 🎉", "unicode"},
			Metadata: map[string]string{
				"special_chars": "!@#$%^&*()_+-=[]{}|;':\",./<>?",
				"unicode":       "中文测试 🚀 🎉 💻",
			},
			Address: &Address{
				Street:  "特殊街道 🏠",
				City:    "深圳 🏙️",
				Country: "中国 🇨🇳",
			},
		}

		err := c.Set(ctx, "special_proto", specialData, cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)

		result, err := c.Get(ctx, "special_proto", cache.WithKeyConvertor[string, *ComplexProtoMessage](keyConverter))
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, specialData.Name, result.Name)
		assert.Equal(t, specialData.Email, result.Email)
		assert.Equal(t, specialData.Tags, result.Tags)
		assert.Equal(t, specialData.Metadata, result.Metadata)
		assert.Equal(t, specialData.Address.Street, result.Address.Street)
		assert.Equal(t, specialData.Address.City, result.Address.City)
		assert.Equal(t, specialData.Address.Country, result.Address.Country)
	})
}

// 定义相同结构的测试数据类型
type TestStruct struct {
	ID       int                    `json:"id"`
	Name     string                 `json:"name"`
	Email    string                 `json:"email"`
	Age      int                    `json:"age"`
	Active   bool                   `json:"active"`
	Tags     []string               `json:"tags"`
	Metadata map[string]interface{} `json:"metadata"`
}

// TestLruLayerCache_JsonProtobufComparison 测试 JSON 和 Protobuf 的性能和功能对比
func TestLruLayerCache_JsonProtobufComparison(t *testing.T) {
	client := createMockRedisClient()
	require.NotNil(t, client)

	// 创建 JSON 和 Protobuf 缓存
	jsonCache, err := NewLruLayerCache[string, *TestStruct](
		"test_json_comparison",
		client,
		[]lru.InitOptions{lru.WithLruSize(1000)},
		[]remote_cache.InitOptions[*TestStruct]{remote_cache.WithTimeout[*TestStruct](10 * time.Second)},
	)
	require.NoError(t, err)

	protoCache, err := NewPbLruLayerCache[string, *TestProtoMessage](
		"test_proto_comparison",
		client,
		[]lru.InitOptions{lru.WithLruSize(1000)},
		[]remote_cache.InitOptions[*TestProtoMessage]{remote_cache.WithTimeout[*TestProtoMessage](10 * time.Second)},
	)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("JSON vs Protobuf 序列化性能对比", func(t *testing.T) {
		// 准备测试数据
		jsonData := &TestStruct{
			ID:     12345,
			Name:   "性能测试用户",
			Email:  "<EMAIL>",
			Age:    30,
			Active: true,
			Tags:   []string{"performance", "test", "json"},
			Metadata: map[string]interface{}{
				"department": "engineering",
				"level":      5,
				"skills":     []string{"Go", "Redis", "JSON"},
			},
		}

		protoData := &TestProtoMessage{
			ID:     12345,
			Name:   "性能测试用户",
			Email:  "<EMAIL>",
			Age:    30,
			Active: true,
			Tags:   []string{"performance", "test", "protobuf"},
			Metadata: map[string]string{
				"department": "engineering",
				"level":      "5",
				"skills":     "Go,Redis,Protobuf",
			},
		}

		keyConverter := func(ctx context.Context, key string) string {
			return "test:comparison:" + key
		}

		// JSON 性能测试
		jsonStart := time.Now()
		err := jsonCache.Set(ctx, "json_perf", jsonData, cache.WithKeyConvertor[string, *TestStruct](keyConverter))
		assert.NoError(t, err)
		jsonSetDuration := time.Since(jsonStart)

		jsonStart = time.Now()
		jsonResult, err := jsonCache.Get(ctx, "json_perf", cache.WithKeyConvertor[string, *TestStruct](keyConverter))
		assert.NoError(t, err)
		jsonGetDuration := time.Since(jsonStart)

		// Protobuf 性能测试
		protoStart := time.Now()
		err = protoCache.Set(ctx, "proto_perf", protoData, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		protoSetDuration := time.Since(protoStart)

		protoStart = time.Now()
		protoResult, err := protoCache.Get(ctx, "proto_perf", cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		protoGetDuration := time.Since(protoStart)

		// 输出性能对比结果
		t.Logf("JSON Set 耗时: %v", jsonSetDuration)
		t.Logf("JSON Get 耗时: %v", jsonGetDuration)
		t.Logf("Protobuf Set 耗时: %v", protoSetDuration)
		t.Logf("Protobuf Get 耗时: %v", protoGetDuration)

		// 验证数据完整性
		assert.Equal(t, jsonData, jsonResult)
		assert.Equal(t, protoData.ID, protoResult.ID)
		assert.Equal(t, protoData.Name, protoResult.Name)
		assert.Equal(t, protoData.Email, protoResult.Email)
	})

	t.Run("JSON vs Protobuf 批量操作对比", func(t *testing.T) {
		keyConverter := func(ctx context.Context, key string) string {
			return "test:comparison:" + key
		}

		// 准备批量测试数据
		jsonBatchData := map[string]*TestStruct{
			"json_batch1": &TestStruct{ID: 1, Name: "JSON用户1", Email: "<EMAIL>", Age: 25, Active: true},
			"json_batch2": &TestStruct{ID: 2, Name: "JSON用户2", Email: "<EMAIL>", Age: 30, Active: false},
			"json_batch3": &TestStruct{ID: 3, Name: "JSON用户3", Email: "<EMAIL>", Age: 35, Active: true},
		}

		protoBatchData := map[string]*TestProtoMessage{
			"proto_batch1": {ID: 1, Name: "Proto用户1", Email: "<EMAIL>", Age: 25, Active: true},
			"proto_batch2": {ID: 2, Name: "Proto用户2", Email: "<EMAIL>", Age: 30, Active: false},
			"proto_batch3": {ID: 3, Name: "Proto用户3", Email: "<EMAIL>", Age: 35, Active: true},
		}

		// JSON 批量操作
		jsonStart := time.Now()
		jsonSuccessCount, err := jsonCache.MSet(ctx, jsonBatchData, cache.WithKeyConvertor[string, *TestStruct](keyConverter))
		assert.NoError(t, err)
		jsonSetDuration := time.Since(jsonStart)

		jsonStart = time.Now()
		jsonKeys := []string{"json_batch1", "json_batch2", "json_batch3"}
		jsonResult, err := jsonCache.MGet(ctx, jsonKeys, cache.WithKeyConvertor[string, *TestStruct](keyConverter))
		assert.NoError(t, err)
		jsonGetDuration := time.Since(jsonStart)

		// Protobuf 批量操作
		protoStart := time.Now()
		protoSuccessCount, err := protoCache.MSet(ctx, protoBatchData, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		protoSetDuration := time.Since(protoStart)

		protoStart = time.Now()
		protoKeys := []string{"proto_batch1", "proto_batch2", "proto_batch3"}
		protoResult, err := protoCache.MGet(ctx, protoKeys, cache.WithKeyConvertor[string, *TestProtoMessage](keyConverter))
		assert.NoError(t, err)
		protoGetDuration := time.Since(protoStart)

		// 输出批量操作性能对比
		t.Logf("JSON MSet 耗时: %v, 成功数量: %d", jsonSetDuration, jsonSuccessCount)
		t.Logf("JSON MGet 耗时: %v, 结果数量: %d", jsonGetDuration, len(jsonResult))
		t.Logf("Protobuf MSet 耗时: %v, 成功数量: %d", protoSetDuration, protoSuccessCount)
		t.Logf("Protobuf MGet 耗时: %v, 结果数量: %d", protoGetDuration, len(protoResult))

		// 验证批量操作结果
		assert.Equal(t, 3, jsonSuccessCount)
		assert.Equal(t, 3, protoSuccessCount)
		assert.Len(t, jsonResult, 3)
		assert.Len(t, protoResult, 3)
	})
}
