package mixed_cache

import (
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/remote_cache"
)

type LruLayerCache[K comparable, V any] interface {
	multi_layer_cache.MultiLayerCache[K, V]
}

func NewLruLayerCache[K comparable, V any](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (LruLayerCache[K, V], error) {
	remoteCache := remote_cache.NewRemoteCache[K, V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[K, V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[K, V](name, lruCache, remoteCache), nil
}

func NewNumberLruLayerCache[K comparable, V typ.Number](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (LruLayerCache[K, V], error) {
	remoteCache := remote_cache.NewNumberRemoteCache[K, V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[K, V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[K, V](name, lruCache, remoteCache), nil
}

func NewBoolLruLayerCache[K comparable, V typ.Bool](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (LruLayerCache[K, V], error) {
	remoteCache := remote_cache.NewBoolRemoteCache[K, V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[K, V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[K, V](name, lruCache, remoteCache), nil
}

func NewStrLruLayerCache[K comparable, V typ.String](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (multi_layer_cache.MultiLayerCache[K, V], error) {
	remoteCache := remote_cache.NewStrRemoteCache[K, V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[K, V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[K, V](name, lruCache, remoteCache), nil
}

func NewPbLruLayerCache[K comparable, V proto.Message](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (multi_layer_cache.MultiLayerCache[K, V], error) {
	remoteCache := remote_cache.NewProtoRemoteCache[K, V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[K, V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[K, V](name, lruCache, remoteCache), nil
}
