package cache

import (
	"context"
)

type Cache[K comparable, V any] interface {
	Name() Name
	Get(ctx context.Context, key K, opts ...Options[K, V]) (V, error)
	Set(ctx context.Context, key K, val V, opts ...Options[K, V]) error
}

type MultiCache[K comparable, V any] interface {
	Cache[K, V]
	MGet(ctx context.Context, keys []K, opts ...Options[K, V]) (map[K]V, error)
	MSet(ctx context.Context, kvMap map[K]V, opts ...Options[K, V]) (int, error)
}

type LoaderCache[K comparable, V any] interface {
	MultiCache[K, V]
	Load(ctx context.Context, key K, opts ...Options[K, V]) (V, error)
}

type MultiLoaderCache[K comparable, V any] interface {
	MultiCache[K, V]
	LoadMany(ctx context.Context, keys []K, opts ...Options[K, V]) (map[string]V, error)
}
