package cache

import (
	"context"
	"reflect"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
)

type (
	FetchFunc[K any, V any] func(context.Context, K) (V, error)

	SliceFetchFunc[K any, V any] func(context.Context, []K) ([]V, error)

	MapFetchFunc[K comparable, V any] func(context.Context, []K) (map[K]V, error)
)

func Get[K comparable, V any](
	ctx context.Context,
	c Cache[K, V],
	key K,
	opts ...Options[K, V],
) (V, bool) {
	option := InitOption(opts...)

	v, err := c.Get(
		ctx,
		key,
		WithOptions(option),
	)

	if err != nil {
		return receiverZeroValue[V](), false
	}
	return v, true
}

// LoadManyFromMap 通用缓存加载函数，实现缓存旁路模式
// 现在直接使用LRU缓存的LoadManyWithKeyFunc函数
// K: 键类型 (如 uint64, string 等)
// V: 值类型 (如 entity.ShopDisplayChannelData 等)
//
// 参数说明:
// - ctx: 上下文
// - cache: LRU缓存实例
// - keys: 需要查询的键列表
// - keyFunc: 将键转换为缓存key的函数
// - fetchFunc: 当缓存未命中时，从数据源获取数据的函数
func LoadManyFromMap[K comparable, V any](
	ctx context.Context,
	cache Cache[K, V],
	keys []K,
	fetchFunc MapFetchFunc[K, V],
	opts ...Options[K, V],
) (map[K]V, error) {
	option := InitOption(opts...)

	receiverMap := make(map[K]V)
	var missedKeys []K
	for _, key := range keys {
		val, err := cache.Get(ctx, key, WithOptions(option))
		if err != nil {
			missedKeys = append(missedKeys, key)
			continue
		}
		receiverMap[key] = val
	}

	// 第二步：如果有缓存未命中的键，从数据源获取
	if len(missedKeys) > 0 {
		fetchedData, err := fetchFunc(ctx, missedKeys)
		if err != nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusError, "")
			return nil, err
		}
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusSuccess, "")

		// 第三步：将获取的数据加入缓存并添加到结果中
		for rawKey, data := range fetchedData {
			_ = cache.Set(ctx, rawKey, data, WithOptions(option))
			receiverMap[rawKey] = data
		}
	}
	return receiverMap, nil
}

// LoadManyFromSlice 通用缓存加载函数，适用于返回切片的场景
// K: 键类型
// V: 值类型
//
// 参数说明:
// - ctx: 上下文
// - cache: LRU缓存实例
// - keys: 需要查询的键列表
// - keyFunc: 将键转换为缓存key的函数
// - fetchFunc: 当缓存未命中时，从数据源获取数据的函数（返回切片）
// - extractKeyFunc: 从获取的数据中提取键的函数
func LoadManyFromSlice[K comparable, V any](
	ctx context.Context,
	cache Cache[K, V],
	keys []K,
	fetchFunc SliceFetchFunc[K, V],
	extractKeyFunc func(V) K,
	opts ...Options[K, V],
) (map[K]V, error) {
	// 包装fetchFunc，将切片转换为map
	wrappedFetchFunc := func(ctx context.Context, missedKeys []K) (map[K]V, error) {
		slice, err := fetchFunc(ctx, missedKeys)
		if err != nil {
			return nil, err
		}

		result := make(map[K]V)
		for _, item := range slice {
			key := extractKeyFunc(item)
			result[key] = item
		}
		return result, nil
	}

	return LoadManyFromMap(ctx, cache, keys, wrappedFetchFunc, opts...)
}

// LoadSingle 单个键的缓存加载函数
// K: 键类型
// V: 值类型
func LoadSingle[K comparable, V any](
	ctx context.Context,
	cache Cache[K, V],
	key K,
	fetchFunc FetchFunc[K, V],
	opts ...Options[K, V],
) (V, error) {
	option := InitOption(opts...)

	if val, err := cache.Get(ctx, key, WithOptions(option)); err == nil {
		return val, nil
	}

	data, err := fetchFunc(ctx, key)
	if err != nil {
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusError, "")
		return receiverZeroValue[V](), err
	}
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusSuccess, "")
	_ = cache.Set(ctx, key, data, WithOptions(option))
	return data, nil
}

// LoadManyFromMapWithAdapter 使用适配器的版本（向后兼容）
// 保留原有的适配器接口，以便与现有代码兼容
func LoadManyFromMapWithAdapter[K comparable, V any](
	ctx context.Context,
	cache Cache[K, V],
	keys []K,
	fetchFunc MapFetchFunc[K, V],
	opts ...Options[K, V],
) (map[K]V, error) {
	option := InitOption(opts...)

	result := make(map[K]V)
	var missedKeys []K

	// 第一步：尝试从缓存获取
	for _, key := range keys {
		if val, err := cache.Get(ctx, key, WithOptions(option)); err == nil {
			result[key] = val
			continue
		}
		missedKeys = append(missedKeys, key)
	}

	// 第二步：如果有缓存未命中的键，从数据源获取
	if len(missedKeys) > 0 {
		fetchedData, err := fetchFunc(ctx, missedKeys)
		if err != nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusError, "")
			return nil, err
		}
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusSuccess, "")

		// 第三步：将获取的数据加入缓存并添加到结果中
		for key, data := range fetchedData {
			_ = cache.Set(ctx, key, data, WithOptions(option))
			result[key] = data
		}
	}
	return result, nil
}

func receiverZeroValue[V any]() V {
	var zeroValue V
	if reflect.TypeOf(zeroValue).Kind() == reflect.Ptr {
		baseType := reflect.TypeOf(zeroValue).Elem()
		newValue := reflect.New(baseType).Interface()
		return newValue.(V)
	}
	return zeroValue
}
