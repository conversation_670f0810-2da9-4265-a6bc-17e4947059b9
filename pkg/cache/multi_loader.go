package cache

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
)

func MultiGetMany[K comparable, V any](
	ctx context.Context,
	cache MultiCache[K, V],
	keys []K,
	opts ...Options[K, V],
) map[K]V {
	option := InitOption(opts...)
	cacheResult, err := cache.MGet(ctx, keys, WithOptions(option))
	if err != nil {
		return make(map[K]V)
	}
	return cacheResult
}

// MultiLoadManyFromMap 通用缓存加载函数，实现缓存旁路模式
// 现在直接使用LRU缓存的LoadManyWithKeyFunc函数
// K: 键类型 (如 uint64, string 等)
// V: 值类型 (如 entity.ShopDisplayChannelData 等)
//
// 参数说明:
// - ctx: 上下文
// - cache: LRU缓存实例
// - keys: 需要查询的键列表
// - keyFunc: 将键转换为缓存key的函数
// - fetchFunc: 当缓存未命中时，从数据源获取数据的函数
func MultiLoadManyFromMap[K comparable, V any](
	ctx context.Context,
	cache MultiCache[K, V],
	keys []K,
	fetchFunc MapFetchFunc[K, V],
	opts ...Options[K, V],
) (map[K]V, error) {
	option := InitOption(opts...)
	// 第一步：尝试从缓存获取
	result, err := cache.MGet(ctx, keys, WithOptions(option))
	var missedKeys []K
	if err != nil {
		result = make(map[K]V)
		missedKeys = keys
	} else {
		missedKeys = make([]K, 0, len(keys)-len(result))
		for _, key := range keys {
			if _, ok := result[key]; !ok {
				missedKeys = append(missedKeys, key)
			}
		}
	}
	// 第二步：如果有缓存未命中的键，从数据源获取
	if len(missedKeys) > 0 {
		fetchedData, fetchErr := fetchFunc(ctx, missedKeys)
		if fetchErr != nil {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusError, "")
			return nil, fetchErr
		}
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleCacheLoader, string(cache.Name()), constant.StatusSuccess, "")

		cacheMap := make(map[K]V, len(fetchedData))
		// 第三步：将获取的数据加入缓存并添加到结果中
		for key, data := range fetchedData {
			cacheMap[key] = data
			result[key] = data
		}
		_, _ = cache.MSet(ctx, cacheMap, opts...)
	}
	return result, nil
}

// MultiLoadManyFromSlice 通用缓存加载函数，适用于返回切片的场景
// K: 键类型
// V: 值类型
//
// 参数说明:
// - ctx: 上下文
// - cache: LRU缓存实例
// - keys: 需要查询的键列表
// - keyFunc: 将键转换为缓存key的函数
// - fetchFunc: 当缓存未命中时，从数据源获取数据的函数（返回切片）
// - extractKeyFunc: 从获取的数据中提取键的函数
func MultiLoadManyFromSlice[K comparable, V any](
	ctx context.Context,
	cache MultiCache[K, V],
	keys []K,
	fetchFunc SliceFetchFunc[K, V],
	extractKeyFunc func(V) K,
	opts ...Options[K, V],
) (map[K]V, error) {
	// 包装fetchFunc，将切片转换为map
	wrappedFetchFunc := func(ctx context.Context, missedKeys []K) (map[K]V, error) {
		slice, err := fetchFunc(ctx, missedKeys)
		if err != nil {
			return nil, err
		}

		result := make(map[K]V)
		for _, item := range slice {
			key := extractKeyFunc(item)
			result[key] = item
		}
		return result, nil
	}

	return MultiLoadManyFromMap(ctx, cache, keys, wrappedFetchFunc, opts...)
}
