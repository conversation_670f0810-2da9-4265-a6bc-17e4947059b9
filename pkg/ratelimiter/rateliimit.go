package ratelimiter

import (
	"context"
	"sync"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"github.com/juju/ratelimit"
	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

var (
	gBucketMap sync.Map
	QpsLimit   = errors.New("qps limit")
)

func CheckRateLimit(ctx context.Context, bucketName string) bool {
	value, ok := gBucketMap.Load(bucketName)
	if !ok {
		return true
	}
	bucket := value.(*ratelimit.Bucket)
	result := bucket.TakeAvailable(1)
	if result == 0 {
		logger.CtxLogErrorf(ctx, "check-limiter,rejected", bucketName)
		_ = monitor.AwesomeReportEvent(ctx, "RateLimit", bucketName, "0", "")
	}
	return result != 0
}
