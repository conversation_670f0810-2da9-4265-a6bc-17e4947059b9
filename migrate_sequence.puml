@startuml
!theme vibrant
' BatchGetItemShopLocation – 迁移后详细时序 (FSS 处理选仓 / FP 处理地址展示)
' 说明：
'   1. FP 将库存快照、location_id、fulfilment_type 打包调用 FSS.GroupItems
'   2. FSS 内部通过仓库优先级服务计算 priority 并返回
'   3. FP 用 location_id+priority 继续原有地址补全逻辑（去掉 OMS 调用）

' ───── 参与者定义 ─────
participant Upstream
participant "Fulfilment Preview Service" as FP
participant "Fulfillment Sourcing Service" as FSS
participant "ItemShopLocationCache" as Cache
participant "Seller API"  as Seller
participant "Shop API"    as Shop
participant "Account API" as Account
participant "Map API"     as Map
participant "OF API"      as OF
participant "WarehousePrioritySvc" as WP

' 自动编号
autonumber "<b>[0]"

' ───── 入口调用 ─────
Upstream -> FP : BatchGetItemShopLocation(items)
activate FP

note over FP #FFFBCC
1. splitItemByFulfilmentType
2. build GroupItemsRequest
   • 填 buyer_user_id / buyer_address_id
   • 每个 model → SourcingItem
   • fulfilment_locations = StockBreakDownByLocationReq
end note

FP -> FSS : GroupItems(request)
activate FSS

' ───── FSS 内部选仓 & 优先级 ─────
box "FSS Internal" #E0E0E0
    FSS -> WP : GetShopeeWarehousePriority()
    WP --> FSS
    FSS -> OF : GetSellerWHPriority()
    OF --> FSS
    note over FSS #DDFFDD
    • 依据配置 & SellerTag 选仓
    • 写 FulfillmentInfo.location_id / priority
    end note
end box

FSS --> FP : GroupItemsResp(location_id, priority, fulfilment_type)
deactivate FSS

note over FP #FFFBCC
3. mergeByQueryID → 把 location_id / priority 写入 StockBreakDownByLocationRes
4. remove sortWarehousePriority (直接用 priority)
end note

== 地址数据阶段 ==

par 并发处理各分组
    FP -> Cache : GetLocationData(...)
    activate Cache

    alt Cache Hit
        Cache --> FP : 地址缓存
    else Cache Miss
        note over Cache #FFE0E0
        并发回源 (errgroup)
        end note
        Cache -> Seller : GetShop3PFMWHFlag()
        Seller --> Cache
        Cache -> Shop   : GetShopInfo()
        Shop   --> Cache
        Cache -> Account: GetAddress()
        Account --> Cache
        Cache -> Map    : Translate(addr)
        Map    --> Cache
        note over Cache #FFEEEE
        回写缓存
        end note
        Cache --> FP : 聚合下游数据
    end
    deactivate Cache
end

note over FP #FFFBCC
5. 遍历结果
   • 填 Item.ShopLocation (最高 priority)
   • 填 Stock.DisplayLocation (各自地址)
end note

FP --> Upstream : BatchGetItemShopLocationResponse
deactivate FP
@enduml