rest_server:
  application:
    name: fss-${MODULE_NAME||api}-${ENV||local}-${CID||sg}
    environment: ${SSC_ENV||local}
  service:
    rest:
      listenAddress: 0.0.0.0:${PORT||8090}
      cors: # 跨域请求的options配置
        allowOrigins: ".*" # 配置了origin，则跨域请求生效，若为空则不生效 , 正则 压测 130ns/ops
        allowMethods: PUT,POST,GET
        allowHeaders: Origin, X-Request-ID, Content-Type, Accept, Content-Length
  initial:
    config:
      appId: fulfillment-sourcing-service
      cluster: ${APOLLO_CLUSTER||default}
      namespaceList: application,business,mutable_application,rest_server,datasource
    cat:
      domain: fss-${MODULE_NAME||api}-${ENV||LOCAL}-${CID||SG}
      drDomainForIdc: us3
    log:
      logLevel: 0 #日志级别配在这里，交给框架初始化
    pprof:
      enabled: true

search_server:
  application:
    name: fss-${MODULE_NAME||searchapi}-${ENV||local}-${CID||sg}
    environment: ${SSC_ENV||local}
  service:
    rest:
      listenAddress: 0.0.0.0:${PORT||8090}
      cors: # 跨域请求的options配置
        allowOrigins: ".*" # 配置了origin，则跨域请求生效，若为空则不生效 , 正则 压测 130ns/ops
        allowMethods: PUT,POST,GET
        allowHeaders: Origin, X-Request-ID, Content-Type, Accept, Content-Length
  initial:
    config:
      appId: fulfillment-sourcing-service
      cluster: ${APOLLO_CLUSTER||default}
      namespaceList: application,business,mutable_application,search_server,datasource
    cat:
      domain: fss-${MODULE_NAME||searchapi}-${ENV||LOCAL}-${CID||SG}
      drDomainForIdc: us3
    log:
      logLevel: 0 #日志级别配在这里，交给框架初始化
    pprof:
      enabled: true

task_server:
  application:
    name: fss-${MODULE_NAME||task}-${ENV||local}-${CID||sg}
    environment: ${SSC_ENV||local}
  service:
    rest:
      listenAddress: 0.0.0.0:${PORT||8090}
      cors: # 跨域请求的options配置
        allowOrigins: ".*" # 配置了origin，则跨域请求生效，若为空则不生效 , 正则 压测 130ns/ops
        allowMethods: PUT,POST,GET
        allowHeaders: Origin, X-Request-ID, Content-Type, Accept, Content-Length
    saturn:
      listenAddress: fss.${ENV||test}.shopee.${CID||sg}
  initial:
    config:
      appId: fulfillment-sourcing-service
      cluster: ${APOLLO_CLUSTER||default}
      namespaceList: application,business,mutable_application,task_server,datasource
    cat:
      domain: fss-${MODULE_NAME||task}-${ENV||LOCAL}-${CID||SG}
      drDomainForIdc: us3
    log:
      logLevel: 0 #日志级别配在这里，交给框架初始化
    pprof:
      enabled: true