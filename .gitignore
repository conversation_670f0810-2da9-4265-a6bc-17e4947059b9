### Example user template template
### Example user template

# IntelliJ project files
.idea
.fleet
.vscode

*.iml
out
gen
### Go template
# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# env file
.env

### macOS template
# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

go.sum
main

# Log
log
*.log
*.lock

conf/*/*.properties
conf/local/
conf/test/
conf/uat/
conf/staging/
conf/*.sync.json
conf/fss*/
conf/*.json

*.code-workspace

# Go 开发工具相关文件
# 覆盖率报告
coverage.out
coverage.html
coverage.xml

# 性能分析文件
*.prof
*.pprof
*.trace

# 临时文件
tmp/
temp/

# 构建产物
bin/
dist/
build/

# 调试文件
debug/

# 文档生成
docs/


# 测试数据
testdata/

# 本地配置文件
*.local
*.local.*