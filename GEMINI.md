# Fulfillment Sourcing Service

## Project Overview

This project is the Fulfillment Sourcing Service, a Go-based microservice built using a Domain-Driven Design (DDD) architecture. Its primary purpose is to handle the logic related to sourcing and fulfilling orders in an e-commerce environment. The service exposes a RESTful API and is designed to be scalable and maintainable.

The project is structured into several layers, including `application`, `domain`, `infrastructure`, and `interface`, which is characteristic of a DDD approach. It uses `go-chassis` as a microservice framework and `wire` for dependency injection.

The core domain of the service revolves around the following entities:
*   **Item:** Represents a product that can be sold.
*   **Order:** Represents a customer's request to purchase one or more items.
*   **Shop:** Represents a seller on the platform.
*   **Stock:** Represents the availability of an item.
*   **Warehouse:** Represents a location where items are stored.

## Building and Running

The project uses a `Makefile` to automate common development tasks.

**Key Commands:**

*   **Install development tools:**
    ```bash
    make install-tools
    ```
*   **Download and update dependencies:**
    ```bash
    make deps
    ```
*   **Generate dependency injection code:**
    ```bash
    make wire
    ```
*   **Build the project:**
    ```bash
    make build
    ```
*   **Run the tests:**
    ```bash
    make test
    ```
*   **Run the linter:**
    ```bash
    make lint
    ```
*   **Run the service:**
    The `Makefile` builds the `rest_server` into an executable named `server`. To run the service, use the following command:
    ```bash
    ./server
    ```

## Development Conventions

*   **Domain-Driven Design (DDD):** The project follows the principles of DDD, with a clear separation of concerns between the different layers of the application.
*   **Dependency Injection:** The project uses `wire` to manage dependencies. When adding new components, you will likely need to update the `wire.go` files to ensure that the dependencies are injected correctly.
*   **Testing:** The project has a suite of unit and integration tests. All new code should be accompanied by corresponding tests.
*   **Linting:** The project uses `golangci-lint` to enforce code quality. Before committing any code, make sure to run the linter and fix any issues that it reports.
