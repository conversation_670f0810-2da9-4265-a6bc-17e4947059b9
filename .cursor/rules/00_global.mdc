# Fulfillment Sourcing Service - Cursor 规则

> 本文件由 AI 生成，用于指导在 Cursor 中与 AI 协作开发 `fulfillment-sourcing-service` 时的行为与最佳实践。

## 对话习惯
1. **统一中文**：所有与开发者的对话、代码审查、评审都使用中文。
2. **角色定位**：AI 充当 Go、微服务架构和 Clean Architecture 专家，确保代码符合现代最佳实践、可维护、可测试且可观测。

## 项目结构约定
```
cmd/        # 应用程序入口（main 包）
internal/   # 核心业务逻辑（不可被其他模块导入）
    ├─ application/
    ├─ domain/
    ├─ infrastructure/
    └─ interface/
api/        # gRPC/REST 协议定义与 handler
pkg/        # 可复用工具库
conf/       # 配置文件与配置加载
scripts/    # 辅助脚本
```
- **按功能聚合代码**：在 `internal` 下按领域拆分子包。
- **禁止** 在 `internal` 之外直接依赖基础设施实现，必须依赖接口。

## Clean Architecture 分层
1. `handler/controller`（interface 层）
2. `service/usecase`（application 层）
3. `repository`（infrastructure abstraction 层）
4. `entity` & `value object`（domain 层）

依赖方向只能从外层指向内层，遵循依赖倒置原则。

## 代码规范
- **函数短小**：推荐 20–40 行，单一职责。
- **错误处理**：显式检查并使用 `fmt.Errorf("context: %w", err)` 包装。
- **依赖注入**：通过构造函数注入接口，而非使用全局变量。
- **Context 传递**：所有对外/长耗时函数首个参数均为 `context.Context`。
- **命名风格**：遵循 Go 官方命名，接口以 `er` 结尾（如 `Reader`、`Loader`）。
- **避免** `panic`，除非在 `main()` 极早期检查。
- **遵循** `go vet`、`golangci-lint`，0 error 0 warning。

## 测试策略
- **表格驱动单元测试**：`go test -race -cover ./...` 覆盖率 > 80%。
- **Mock** 外部依赖（数据库、HTTP、gRPC）以实现单元测试隔离。
- 区分 **unit / integration / e2e** 测试目录。

## 可观测性
- **Tracing / Transaction Monitoring**：使用 `gocommon/monitor`（已在 `internal/common/monitorutils` 封装）上报 [CAT](https://github.com/dianping/cat) 事务与事件，依托 `context.Context` 传递 trace 信息；如需局部关闭可调用 `ctxutils.IsReportDisable`。
- **日志**：统一通过 `internal/logger` 封装的 `gocommon/logger` 输出。业务层优先使用 `CtxLog*` 系列带 `ctx` 的日志函数，日志格式遵循 Shopee 统一 JSON 规范，并自动注入 `trace_id` / `request_id` 等关键信息。
- **指标**：使用 `go/chassis/pkg/metrics`，通过 `internal/common/metrics` 提供的函数 `CounterAdd` / `GaugeSet` / `HistogramObserve` 等上报 Prometheus 指标。所有指标需遵循 `fss_<模块>_<动作>_<metric>` 命名规范，并统一附带 `type_mark` label（由 `ctxutils` 注入）。

## 安全与可靠性
- 参数校验严格，防止注入与越权。
- 外部调用统一加 **超时、重试、熔断（circuit breaker）**。
- 敏感配置通过环境变量或密钥管理系统注入。

## 性能与并发
- Goroutine 应通过 `context` 控制生命周期，避免泄漏。
- 并发安全：共享变量使用 `sync` 原语或 Channel。
- 持续使用 `pprof` & 基准测试监控 `alloc/op``

---
**变更流程**
1. 建立 feature 分支并提交 PR。
2. 通过 AI 进行初步代码审查（静态分析、Design Review、测试覆盖）。
3. 维护者 Review & 合并。 