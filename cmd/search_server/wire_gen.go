// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/service"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/bootstrap"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/datasource_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/abtesting"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/channel_generator"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/checkout_promo"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/processor"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_info"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_stock"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/logistics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/sbs"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_logistics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/soc"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/omslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/handler"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/ping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/layer_cache"
)

// Injectors from injector.go:

func InitPingResource() *ping.PingResource {
	pingResource := &ping.PingResource{}
	return pingResource
}

func NewConfAccessor(ctx context.Context) config.ConfAccessor {
	applicationAccessorImpl := application_config.NewApplicationAccessorImpl()
	businessAccessorImpl := business_config.NewBusinessAccessorImpl()
	mutableApplicationAccessorImpl := mutable_application_config.NewMutableApplicationAccessorImpl()
	serverAccessorImpl := server_config.NewServerAccessorImpl()
	datasourceAccessorImpl := datasource_config.NewDatasourceAccessorImpl()
	confAccessorImpl := config.NewConfAccessorImpl(applicationAccessorImpl, businessAccessorImpl, mutableApplicationAccessorImpl, serverAccessorImpl, datasourceAccessorImpl)
	return confAccessorImpl
}

func initRestServer(ctx context.Context, confAccessor config.ConfAccessor) (*handler.RestServer, error) {
	globalRedisClients, err := redishelper.InitRedisClientMap()
	if err != nil {
		return nil, err
	}
	socServiceImpl, err := soc.NewSOCService(globalRedisClients)
	if err != nil {
		return nil, err
	}
	omsClientImpl, err := omslib.NewOMSClientImpl(confAccessor, globalRedisClients)
	if err != nil {
		return nil, err
	}
	layerCacheManager := layer_cache.NewLayerCacheManager(ctx, confAccessor, globalRedisClients)
	spexClientImpl := spexlib.NewSpexClientImpl(confAccessor, layerCacheManager)
	addrServiceImpl := address.NewAddressService(spexClientImpl, confAccessor)
	sellerTagApiImpl, err := seller_tag.NewSellerTagApiImpl(spexClientImpl, confAccessor, globalRedisClients)
	if err != nil {
		return nil, err
	}
	sellerTagServiceImpl := seller_tag.NewSellerTagServiceImpl(confAccessor, sellerTagApiImpl)
	warehousePriorityServiceImpl := warehouse_priority.NewWarehousePriorityService(confAccessor, omsClientImpl, addrServiceImpl, sellerTagServiceImpl)
	advanceBookingShippingOrderProcessor := processor.NewAdvanceBookingShippingOrderProcessor(socServiceImpl, warehousePriorityServiceImpl, addrServiceImpl)
	warehousePriorityStrategyItemOrganizer := organizer.NewWarehousePriorityStrategyItemOrganizer()
	minimumParcelStrategyItemOrganizer := organizer.NewMinimumParcelStrategyItemOrganizer(confAccessor, warehousePriorityStrategyItemOrganizer)
	itemOrganizerImpl := organizer.NewItemOrganizer(minimumParcelStrategyItemOrganizer, warehousePriorityStrategyItemOrganizer)
	shopLocalSIPServiceImpl, err := shop_local_sip.NewShopLocalSIPServiceWithRedisClients(globalRedisClients, spexClientImpl, confAccessor)
	if err != nil {
		return nil, err
	}
	cblffShippingOrderProcessor := processor.NewCBLFFShippingOrderProcessor(confAccessor, itemOrganizerImpl, warehousePriorityServiceImpl, addrServiceImpl, shopLocalSIPServiceImpl)
	resellShippingOrderProcessor := processor.NewResellShippingOrderProcessor(confAccessor, itemOrganizerImpl, warehousePriorityServiceImpl, addrServiceImpl, shopLocalSIPServiceImpl)
	serviceImpl := abtesting.NewServiceImpl(ctx, confAccessor)
	cacheStoreImpl := order.NewCacheStore(globalRedisClients)
	salesOrdersCountServiceImpl := order.NewSalesOrdersCountServiceImpl(cacheStoreImpl, confAccessor, sellerTagServiceImpl)
	pffShippingOrderProcessor := processor.NewPffShippingOrderProcessor(confAccessor, addrServiceImpl, warehousePriorityServiceImpl, serviceImpl, itemOrganizerImpl, salesOrdersCountServiceImpl, shopLocalSIPServiceImpl)
	tagApiImpl := item_tag.NewTagApiImpl(spexClientImpl)
	itemTagServiceImpl := item_tag.NewItemTagServiceImpl(tagApiImpl)
	shopeeShippingOrderProcessor := processor.NewShopeeShippingOrderProcessor(confAccessor, itemOrganizerImpl, itemTagServiceImpl, warehousePriorityServiceImpl, addrServiceImpl)
	shopServiceImpl := shop.NewShopService(spexClientImpl)
	cb3pfShippingOrderProcessor := processor.NewCb3pfShippingOrderProcessor(confAccessor, itemOrganizerImpl, shopServiceImpl, addrServiceImpl, warehousePriorityServiceImpl, shopLocalSIPServiceImpl)
	sellerShippingOrderProcessor := processor.NewSellerShippingOrderProcessor(confAccessor, itemOrganizerImpl, shopServiceImpl, shopLocalSIPServiceImpl, addrServiceImpl, warehousePriorityServiceImpl)
	shopLogisticsInfoApiImpl, err := shop_logistics.NewShopLogisticsInfoApiImpl(spexClientImpl, confAccessor, globalRedisClients)
	if err != nil {
		return nil, err
	}
	warehouseLogisticsInfoApiImpl, err := shop_logistics.NewWarehouseLogisticsInfoApiImpl(confAccessor)
	if err != nil {
		return nil, err
	}
	shopLogisticsServiceImpl := shop_logistics.NewShopLogisticsServiceImpl(shopLogisticsInfoApiImpl, warehouseLogisticsInfoApiImpl)
	itemInfoApiImpl, err := item_info.NewItemInfoApiImpl(confAccessor, globalRedisClients, spexClientImpl)
	if err != nil {
		return nil, err
	}
	itemInfoServiceImpl := item_info.NewItemInfoServiceImpl(itemInfoApiImpl, confAccessor)
	generatorServiceImpl := channel_generator.NewChannelGeneratorServiceImpl(confAccessor, shopLogisticsServiceImpl, itemInfoServiceImpl, itemTagServiceImpl)
	lpsClientImpl, err := lpslib.NewLpsClientImpl(confAccessor)
	if err != nil {
		return nil, err
	}
	logisticsInfoApiImpl := logistics.NewLogisticsInfoApiImpl(lpsClientImpl)
	logisticsServiceImpl := logistics.NewLogisticsServiceImpl(logisticsInfoApiImpl)
	weightDimensionProcessor := processor.NewWeightDimensionProcessor(confAccessor, generatorServiceImpl, itemInfoServiceImpl, logisticsServiceImpl)
	sbsServiceImpl, err := sbs.NewServiceImpl(ctx, globalRedisClients, spexClientImpl)
	if err != nil {
		return nil, err
	}
	groupShipmentOrderProcessor := processor.NewGroupShipmentOrderProcessor(sbsServiceImpl, warehousePriorityServiceImpl, addrServiceImpl, itemOrganizerImpl)
	groupingRulesProcessorImpl := processor.NewGroupingRulesProcessor()
	defaultItemGrouper := item_grouping.NewItemGrouper(confAccessor, advanceBookingShippingOrderProcessor, cblffShippingOrderProcessor, resellShippingOrderProcessor, pffShippingOrderProcessor, shopeeShippingOrderProcessor, cb3pfShippingOrderProcessor, sellerShippingOrderProcessor, weightDimensionProcessor, groupShipmentOrderProcessor, groupingRulesProcessorImpl)
	checkoutPromoApiImpl := checkout_promo.NewCheckoutPromoApiImpl(spexClientImpl)
	stockServiceImpl := item_stock.NewStockServiceImpl(spexClientImpl, checkoutPromoApiImpl)
	itemStockServiceImpl := item_stock.NewItemStockServiceImpl(stockServiceImpl)
	shopMultiWHCacheImpl, err := shop.NewShopMultiWHCacheImplWithClient(confAccessor, globalRedisClients, addrServiceImpl, spexClientImpl)
	if err != nil {
		return nil, err
	}
	sellerWHPriorityServiceImpl := warehouse_priority.NewSellerWHPriorityService(shopMultiWHCacheImpl, addrServiceImpl, shopLocalSIPServiceImpl)
	itemGroupingServiceImpl := service.NewItemGroupingService(defaultItemGrouper, shopServiceImpl, itemStockServiceImpl, stockServiceImpl, sellerTagServiceImpl, sellerWHPriorityServiceImpl, warehousePriorityServiceImpl, socServiceImpl, addrServiceImpl, confAccessor)
	itemGroupingHandler := handler.NewItemGroupingHandler(itemGroupingServiceImpl)
	debugHandler := handler.NewDebugHandler(confAccessor)
	restServer := &handler.RestServer{
		ItemGroupingHandler: itemGroupingHandler,
		DebugHandler:        debugHandler,
	}
	return restServer, nil
}

// injector.go:

func InitConf(ctx context.Context, confAccessor config.ConfAccessor) error {
	if err := bootstrap.InitChassisConfigListener(ctx); err != nil {
		return err
	}
	if err := confAccessor.Init(ctx); err != nil {
		return err
	}
	return nil
}

func InitRestServer(ctx context.Context, confAccessor config.ConfAccessor) (*handler.RestServer, error) {
	if err := bootstrap.InitLocalCache(ctx, confAccessor); err != nil {
		return nil, err
	}
	return initRestServer(ctx, confAccessor)
}
