//go:generate wire
//go:build wireinject
// +build wireinject

package main

import (
	"context"

	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/bootstrap"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/handler"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/ping"
)

func InitPingResource() *ping.PingResource {
	wire.Build(
		ping.ProviderSet,
	)
	return new(ping.PingResource)
}

func NewConfAccessor(ctx context.Context) config.ConfAccessor {
	wire.Build(config.ConfAccessorSet)
	return nil
}

func InitConf(ctx context.Context, confAccessor config.ConfAccessor) error {
	if err := bootstrap.InitChassisConfigListener(ctx); err != nil {
		return err
	}
	if err := confAccessor.Init(ctx); err != nil {
		return err
	}
	return nil
}

func InitRestServer(ctx context.Context, confAccessor config.ConfAccessor) (*handler.RestServer, error) {
	if err := bootstrap.InitLocalCache(ctx, confAccessor); err != nil {
		return nil, err
	}
	return initRestServer(ctx, confAccessor)
}

func initRestServer(ctx context.Context, confAccessor config.ConfAccessor) (*handler.RestServer, error) {
	wire.Build(
		handler.ProviderSet,
	)
	return new(handler.RestServer), nil
}
