package main

import (
	"context"
	"log"

	"google.golang.org/grpc"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	chassisconfig "git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/bootstrap"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interceptor/middleware"
)

func main() {
	ctx := context.Background()

	if err := bootstrap.InitEnvironmentVariables(ctx); err != nil {
		log.Fatal(err)
	}

	// 优先初始化 confAccessor
	confAccessor := NewConfAccessor(ctx)

	// init chassis
	if err := initChassis(confAccessor); err != nil {
		log.Fatalf("chassis init failed: %v", err)
	}

	// 初始化配置
	if err := InitConf(ctx, confAccessor); err != nil {
		log.Fatalf("init conf failed: %v", err)
	}

	// 初始化服务依赖
	if err := bootstrap.InitRestServerDependency(ctx, confAccessor); err != nil {
		log.Fatalf("init rest server dependency failed: %v", err)
	}

	if err := bootstrap.InitMetrics(); err != nil {
		log.Fatalf("init metrics failed: %v", err)
	}

	// init rest server and register
	restServer, err := InitRestServer(ctx, confAccessor)
	if err != nil {
		log.Fatalf("init rest server failed: %v", err)
	}
	restServer.Register()

	log.Printf("[%s] start listen addr: %s\n",
		chassisconfig.GetStringWithContext(ctx, "service_description.name", ""),
		chassisconfig.GetStringWithContext(ctx, "cse.protocols.rest.listenAddress", ""))
	if err := chassis.Run(); err != nil {
		log.Fatalf("chassis run failed: %v", err)
	}
}

func initChassis(confAccessor config.ConfAccessor) error {
	// register handler
	handler.RegisterSplitMarketHandler()
	handler.RegisterInjectHandler()
	unaryServerInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		return handler(ctx, req)
	}
	handler.RegisterRecorderHandler(handler.RecorderHandlerOptions{})
	handler.RegisterReplayerHandler()
	handler.RegisterPrometheusMetricHandler()
	middleware.RegisterRequestContextHandler(confAccessor)
	middleware.RegisterPBLoggerHandler()
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("rest_server"),
		chassis.WithDefaultProviderHandlerChain(
			handler.RecorderProviderName,
			handler.RepalyerProviderName,
			handler.LogProviderHandlerName,
			handler.NameOfSplitMarketHandlerOfProvider,
			handler.NameOfPrometheusMetricProvider,
			middleware.RequestContextHandlerName,
			middleware.PBLoggerHandlerName,
		),
		chassis.WithDefaultConsumerHandlerChain(
			handler.NameOfPrometheusMetricConsumer,
		),
		chassis.WithGRPCUnaryServerInterceptor(unaryServerInterceptor),
	); err != nil {
		return err
	}

	chassis.RegisterSchema("rest", InitPingResource())
	return nil
}
