// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/bootstrap"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/datasource_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/ping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/task_handler"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/layer_cache"
)

// Injectors from injector.go:

func InitPingResource() *ping.PingResource {
	pingResource := &ping.PingResource{}
	return pingResource
}

func NewConfAccessor(ctx context.Context) config.ConfAccessor {
	applicationAccessorImpl := application_config.NewApplicationAccessorImpl()
	businessAccessorImpl := business_config.NewBusinessAccessorImpl()
	mutableApplicationAccessorImpl := mutable_application_config.NewMutableApplicationAccessorImpl()
	serverAccessorImpl := server_config.NewServerAccessorImpl()
	datasourceAccessorImpl := datasource_config.NewDatasourceAccessorImpl()
	confAccessorImpl := config.NewConfAccessorImpl(applicationAccessorImpl, businessAccessorImpl, mutableApplicationAccessorImpl, serverAccessorImpl, datasourceAccessorImpl)
	return confAccessorImpl
}

func initTaskServer(ctx context.Context, confAccessor config.ConfAccessor) (*task_handler.TaskServer, error) {
	globalRedisClients, err := redishelper.InitRedisClientMap()
	if err != nil {
		return nil, err
	}
	cacheStoreImpl := order.NewCacheStore(globalRedisClients)
	layerCacheManager := layer_cache.NewLayerCacheManager(ctx, confAccessor, globalRedisClients)
	spexClientImpl := spexlib.NewSpexClientImpl(confAccessor, layerCacheManager)
	sellerTagApiImpl, err := seller_tag.NewSellerTagApiImpl(spexClientImpl, confAccessor, globalRedisClients)
	if err != nil {
		return nil, err
	}
	sellerTagServiceImpl := seller_tag.NewSellerTagServiceImpl(confAccessor, sellerTagApiImpl)
	salesOrdersCountServiceImpl := order.NewSalesOrdersCountServiceImpl(cacheStoreImpl, confAccessor, sellerTagServiceImpl)
	salesOrderListener := task_handler.NewSalesOrderListener(salesOrdersCountServiceImpl)
	taskServer := &task_handler.TaskServer{
		SalesOrderListener: salesOrderListener,
	}
	return taskServer, nil
}

// injector.go:

func InitConf(ctx context.Context, confAccessor config.ConfAccessor) error {
	if err := bootstrap.InitChassisConfigListener(ctx); err != nil {
		return err
	}
	if err := confAccessor.Init(ctx); err != nil {
		return err
	}
	return nil
}

func InitTaskServer(ctx context.Context, confAccessor config.ConfAccessor) (*task_handler.TaskServer, error) {
	if err := bootstrap.InitLocalCache(ctx, confAccessor); err != nil {
		return nil, err
	}
	return initTaskServer(ctx, confAccessor)
}
