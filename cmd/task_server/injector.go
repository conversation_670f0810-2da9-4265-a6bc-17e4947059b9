//go:generate wire
//go:build wireinject
// +build wireinject

package main

import (
	"context"

	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/bootstrap"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/ping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/task_handler"
)

func InitPingResource() *ping.PingResource {
	wire.Build(
		ping.ProviderSet,
	)
	return new(ping.PingResource)
}

func NewConfAccessor(ctx context.Context) config.ConfAccessor {
	wire.Build(config.ConfAccessorSet)
	return nil
}

func InitConf(ctx context.Context, confAccessor config.ConfAccessor) error {
	if err := bootstrap.InitChassisConfigListener(ctx); err != nil {
		return err
	}
	if err := confAccessor.Init(ctx); err != nil {
		return err
	}
	return nil
}

func InitTaskServer(ctx context.Context, confAccessor config.ConfAccessor) (*task_handler.TaskServer, error) {
	if err := bootstrap.InitLocalCache(ctx, confAccessor); err != nil {
		return nil, err
	}
	return initTaskServer(ctx, confAccessor)
}

func initTaskServer(ctx context.Context, confAccessor config.ConfAccessor) (*task_handler.TaskServer, error) {
	wire.Build(
		task_handler.TaskProvideSet,
	)
	return new(task_handler.TaskServer), nil
}
